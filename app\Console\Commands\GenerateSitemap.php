<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Country;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate XML sitemap for the EU driving license website';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating sitemap...');

        $sitemap = Sitemap::create();

        // Add homepage for each language
        foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
            $url = $localeCode === 'en' ? '/' : "/{$localeCode}/";
            $sitemap->add(Url::create($url)
                ->setLastModificationDate(now())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                ->setPriority(1.0));
        }

        // Add country pages for each language
        $countries = Country::active()->get();

        foreach ($countries as $country) {
            foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
                // Skip if country doesn't have translation for this locale
                if (!$country->hasTranslation($localeCode)) {
                    continue;
                }

                $baseUrl = $localeCode === 'en' ? '' : "/{$localeCode}";

                // Country main page
                $sitemap->add(Url::create("{$baseUrl}/driving-license/{$country->translate($localeCode)->slug}")
                    ->setLastModificationDate($country->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                    ->setPriority(0.9));

                // Buy page
                $sitemap->add(Url::create("{$baseUrl}/buy-{$country->translate($localeCode)->slug}-driving-licence-online")
                    ->setLastModificationDate($country->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                    ->setPriority(0.9));

                // Requirements page
                $sitemap->add(Url::create("{$baseUrl}/{$country->translate($localeCode)->slug}-driving-license-requirements")
                    ->setLastModificationDate($country->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));

                // Process page
                $sitemap->add(Url::create("{$baseUrl}/{$country->translate($localeCode)->slug}-driving-license-process")
                    ->setLastModificationDate($country->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));

                // FAQ page
                $sitemap->add(Url::create("{$baseUrl}/{$country->translate($localeCode)->slug}-driving-license-faq")
                    ->setLastModificationDate($country->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.7));
            }
        }

        // Add static pages for each language
        $staticPages = ['how-it-works', 'pricing', 'contact', 'about', 'privacy-policy', 'terms-of-service'];

        foreach (LaravelLocalization::getSupportedLocales() as $localeCode => $properties) {
            $baseUrl = $localeCode === 'en' ? '' : "/{$localeCode}";

            foreach ($staticPages as $page) {
                $sitemap->add(Url::create("{$baseUrl}/{$page}")
                    ->setLastModificationDate(now())
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.6));
            }
        }

        $sitemap->writeToFile(public_path('sitemap.xml'));

        $this->info('Sitemap generated successfully at public/sitemap.xml');

        return 0;
    }
}
