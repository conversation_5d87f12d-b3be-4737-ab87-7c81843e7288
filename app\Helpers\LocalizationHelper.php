<?php

namespace App\Helpers;

class LocalizationHelper
{
    /**
     * Generate a localized route URL
     */
    public static function route($name, $parameters = [], $locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $routeName = $name . '.' . $locale;
        
        try {
            return route($routeName, $parameters);
        } catch (\Exception $e) {
            // Fallback to English if route doesn't exist
            try {
                return route($name . '.en', $parameters);
            } catch (\Exception $e) {
                // Last fallback - try the original route name
                return route($name, $parameters);
            }
        }
    }
    
    /**
     * Get all supported locales
     */
    public static function getSupportedLocales()
    {
        return [
            'en' => ['name' => 'English', 'native' => 'English'],
            'de' => ['name' => 'German', 'native' => 'Deutsch'],
            'fr' => ['name' => 'French', 'native' => 'Français'],
            'es' => ['name' => 'Spanish', 'native' => 'Español'],
            'it' => ['name' => 'Italian', 'native' => 'Italiano'],
            'nl' => ['name' => 'Dutch', 'native' => 'Nederlands'],
        ];
    }
    
    /**
     * Generate language switcher URLs
     */
    public static function getLanguageSwitcherUrls($currentRoute = null, $parameters = [])
    {
        $urls = [];
        $currentRoute = $currentRoute ?: request()->route()->getName();
        
        // Remove locale suffix from current route
        $baseRoute = preg_replace('/\.(en|de|fr|es|it|nl)$/', '', $currentRoute);
        
        foreach (self::getSupportedLocales() as $locale => $data) {
            try {
                $urls[$locale] = [
                    'url' => self::route($baseRoute, $parameters, $locale),
                    'name' => $data['name'],
                    'native' => $data['native'],
                    'active' => app()->getLocale() === $locale
                ];
            } catch (\Exception $e) {
                // Fallback to homepage for that locale
                $urls[$locale] = [
                    'url' => '/' . $locale,
                    'name' => $data['name'],
                    'native' => $data['native'],
                    'active' => app()->getLocale() === $locale
                ];
            }
        }
        
        return $urls;
    }
    
    /**
     * Generate hreflang attributes for SEO
     */
    public static function getHreflangUrls($currentRoute = null, $parameters = [])
    {
        $urls = [];
        $currentRoute = $currentRoute ?: request()->route()->getName();
        
        // Remove locale suffix from current route
        $baseRoute = preg_replace('/\.(en|de|fr|es|it|nl)$/', '', $currentRoute);
        
        foreach (self::getSupportedLocales() as $locale => $data) {
            try {
                $urls[$locale] = self::route($baseRoute, $parameters, $locale);
            } catch (\Exception $e) {
                $urls[$locale] = '/' . $locale;
            }
        }
        
        return $urls;
    }
}
