<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Country;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    // Middleware is now handled at the route level

    /**
     * Show the admin dashboard
     */
    public function dashboard()
    {
        // Get basic stats
        $stats = [
            'total_applications' => Application::count(),
            'pending_applications' => Application::whereIn('application_status', [
                'submitted',
                'under_review',
                'documents_required',
                'payment_pending'
            ])->count(),
            'processing_applications' => Application::where('application_status', 'processing')->count(),
            'completed_applications' => Application::where('application_status', 'delivered')->count(),
            'total_revenue' => Application::where('payment_status', 'completed')->sum('total_amount'),
            'monthly_revenue' => Application::where('payment_status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),
        ];

        // Calculate growth percentages
        $lastMonthApplications = Application::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        $currentMonthApplications = Application::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        $stats['applications_growth'] = $lastMonthApplications > 0
            ? round((($currentMonthApplications - $lastMonthApplications) / $lastMonthApplications) * 100, 1)
            : 0;

        // Recent applications
        $recentApplications = Application::with('country')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Applications by country
        $applicationsByCountry = Application::select('country_translations.name', DB::raw('count(*) as total'))
            ->join('countries', 'applications.country_id', '=', 'countries.id')
            ->join('country_translations', 'countries.id', '=', 'country_translations.country_id')
            ->where('country_translations.locale', app()->getLocale())
            ->groupBy('country_translations.name')
            ->orderBy('total', 'desc')
            ->get();

        // Applications by status
        $applicationsByStatus = Application::select('application_status', DB::raw('count(*) as total'))
            ->groupBy('application_status')
            ->get();

        // Monthly applications chart data - Database agnostic
        $monthlyApplications = $this->getMonthlyApplicationsData();

        return view('admin.dashboard', compact(
            'stats',
            'recentApplications',
            'applicationsByCountry',
            'applicationsByStatus',
            'monthlyApplications'
        ));
    }

    /**
     * Show all applications
     */
    public function applications(Request $request)
    {
        $query = Application::with(['country', 'user']);

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('application_status', $request->status);
        }

        // Filter by country
        if ($request->has('country') && $request->country !== '') {
            $query->where('country_id', $request->country);
        }

        // Search by application number or email
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('application_number', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $applications = $query->paginate(20);

        // Get filter options
        $countries = Country::active()->ordered()->get();
        $statuses = [
            Application::STATUS_DRAFT => 'Draft',
            Application::STATUS_SUBMITTED => 'Submitted',
            Application::STATUS_UNDER_REVIEW => 'Under Review',
            Application::STATUS_DOCUMENTS_REQUIRED => 'Documents Required',
            Application::STATUS_PAYMENT_PENDING => 'Payment Pending',
            Application::STATUS_PAYMENT_CONFIRMED => 'Payment Confirmed',
            Application::STATUS_PROCESSING => 'Processing',
            Application::STATUS_READY_FOR_DELIVERY => 'Ready for Delivery',
            Application::STATUS_DELIVERED => 'Delivered',
            Application::STATUS_CANCELLED => 'Cancelled',
            Application::STATUS_REJECTED => 'Rejected',
        ];

        return view('admin.applications.index', compact('applications', 'countries', 'statuses'));
    }

    /**
     * Show a specific application
     */
    public function showApplication(Application $application)
    {
        $application->load(['country', 'user', 'statusHistory']);

        return view('admin.applications.show', compact('application'));
    }

    /**
     * Update application status
     */
    public function updateApplicationStatus(Request $request, Application $application)
    {
        $request->validate([
            'status' => 'required|string',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $application->updateStatus($request->status, $request->notes);

            return redirect()->back()
                ->with('success', 'Application status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'There was an error updating the application status.');
        }
    }

    /**
     * Show countries management
     */
    public function countries()
    {
        $countries = Country::withCount('applications')->ordered()->get();

        return view('admin.countries.index', compact('countries'));
    }

    /**
     * Show country edit form
     */
    public function editCountry(Country $country)
    {
        return view('admin.countries.edit', compact('country'));
    }

    /**
     * Update country
     */
    public function updateCountry(Request $request, Country $country)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:3|unique:countries,code,' . $country->id,
            'base_price' => 'required|numeric|min:0',
            'processing_time' => 'required|string|max:255',
            'is_active' => 'boolean',
        ]);

        try {
            $country->update($request->all());

            return redirect()->route('admin.countries')
                ->with('success', 'Country updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'There was an error updating the country.')
                ->withInput();
        }
    }

    /**
     * Show users management
     */
    public function users()
    {
        $users = User::withCount('applications')->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show user details
     */
    public function showUser(User $user)
    {
        $user->load(['applications.country']);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show settings
     */
    public function settings()
    {
        $settings = [
            // Application Settings
        ];

        return view('admin.settings', compact('settings'));
    }

    /**
     * Update admin settings
     */
    public function updateSettings(Request $request)
    {
        try {
            // Update application settings here when needed

            return redirect()->route('admin.settings')->with('success', 'Settings updated successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings')->with('error', 'There was an error updating the settings. Please try again.');
        }
    }

    /**
     * Show reports
     */
    public function reports()
    {
        // Revenue reports - Database agnostic
        $monthlyRevenue = $this->getMonthlyRevenueData();

        // Country performance
        $countryPerformance = Application::select(
                'country_translations.name',
                DB::raw('COUNT(*) as total_applications'),
                DB::raw('SUM(total_amount) as total_revenue'),
                DB::raw('AVG(total_amount) as avg_revenue')
            )
            ->join('countries', 'applications.country_id', '=', 'countries.id')
            ->join('country_translations', 'countries.id', '=', 'country_translations.country_id')
            ->where('country_translations.locale', app()->getLocale())
            ->where('payment_status', Application::PAYMENT_COMPLETED)
            ->groupBy('country_translations.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        // Status distribution
        $statusDistribution = Application::select(
                'application_status',
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('application_status')
            ->get();

        return view('admin.reports', compact(
            'monthlyRevenue',
            'countryPerformance',
            'statusDistribution'
        ));
    }

    /**
     * Get monthly applications data in a database-agnostic way
     */
    private function getMonthlyApplicationsData()
    {
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            return Application::select(
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('count(*) as total')
                )
                ->whereYear('created_at', now()->year)
                ->groupBy('year', 'month')
                ->orderBy('month')
                ->get();
        } else {
            // SQLite compatible version
            return Application::select(
                    DB::raw('CAST(strftime("%m", created_at) AS INTEGER) as month'),
                    DB::raw('CAST(strftime("%Y", created_at) AS INTEGER) as year'),
                    DB::raw('count(*) as total')
                )
                ->whereRaw('strftime("%Y", created_at) = ?', [now()->year])
                ->groupBy('year', 'month')
                ->orderBy('month')
                ->get();
        }
    }

    /**
     * Get monthly revenue data in a database-agnostic way
     */
    private function getMonthlyRevenueData()
    {
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            return Application::select(
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('SUM(total_amount) as revenue'),
                    DB::raw('COUNT(*) as applications')
                )
                ->where('payment_status', 'completed')
                ->whereYear('created_at', now()->year)
                ->groupBy('year', 'month')
                ->orderBy('month')
                ->get();
        } else {
            // SQLite compatible version
            return Application::select(
                    DB::raw('CAST(strftime("%m", created_at) AS INTEGER) as month'),
                    DB::raw('CAST(strftime("%Y", created_at) AS INTEGER) as year'),
                    DB::raw('SUM(total_amount) as revenue'),
                    DB::raw('COUNT(*) as applications')
                )
                ->where('payment_status', 'completed')
                ->whereRaw('strftime("%Y", created_at) = ?', [now()->year])
                ->groupBy('year', 'month')
                ->orderBy('month')
                ->get();
        }
    }
}
