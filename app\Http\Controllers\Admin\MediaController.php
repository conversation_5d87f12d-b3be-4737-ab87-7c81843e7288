<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class MediaController extends Controller
{
    public function index(Request $request)
    {
        $query = Media::query();

        // Filter by collection
        if ($request->filled('collection')) {
            $query->where('collection_name', $request->collection);
        }

        // Filter by mime type
        if ($request->filled('type')) {
            switch ($request->type) {
                case 'images':
                    $query->where('mime_type', 'like', 'image/%');
                    break;
                case 'documents':
                    $query->whereIn('mime_type', ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);
                    break;
                case 'videos':
                    $query->where('mime_type', 'like', 'video/%');
                    break;
            }
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $media = $query->orderBy('created_at', 'desc')->paginate(24);
        $collections = Media::distinct()->pluck('collection_name')->filter();

        return view('admin.media.index', compact('media', 'collections'));
    }

    public function upload(Request $request)
    {
        $request->validate([
            'files.*' => 'required|file|max:10240', // 10MB max
            'collection' => 'nullable|string|max:50'
        ]);

        $uploadedFiles = [];
        $collection = $request->collection ?: 'default';

        foreach ($request->file('files') as $file) {
            try {
                // Create a temporary model to handle the upload
                $tempModel = new \App\Models\TempMediaModel();
                
                $mediaItem = $tempModel
                    ->addMediaFromRequest('files')
                    ->usingFileName($this->generateUniqueFileName($file))
                    ->toMediaCollection($collection);

                // Optimize images
                if (str_starts_with($mediaItem->mime_type, 'image/')) {
                    $this->optimizeImage($mediaItem);
                }

                $uploadedFiles[] = [
                    'id' => $mediaItem->id,
                    'name' => $mediaItem->name,
                    'url' => $mediaItem->getUrl(),
                    'size' => $this->formatFileSize($mediaItem->size),
                    'mime_type' => $mediaItem->mime_type,
                    'collection' => $mediaItem->collection_name,
                ];

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Upload failed: ' . $e->getMessage()
                ], 422);
            }
        }

        return response()->json([
            'success' => true,
            'files' => $uploadedFiles,
            'message' => count($uploadedFiles) . ' file(s) uploaded successfully.'
        ]);
    }

    public function show(Media $media)
    {
        return view('admin.media.show', compact('media'));
    }

    public function update(Request $request, Media $media)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'caption' => 'nullable|string|max:500',
        ]);

        $media->update([
            'name' => $request->name,
            'custom_properties' => array_merge($media->custom_properties, [
                'alt_text' => $request->alt_text,
                'caption' => $request->caption,
            ])
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Media updated successfully.'
        ]);
    }

    public function destroy(Media $media)
    {
        $media->delete();

        return response()->json([
            'success' => true,
            'message' => 'Media deleted successfully.'
        ]);
    }

    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:media,id'
        ]);

        Media::whereIn('id', $request->ids)->delete();

        return response()->json([
            'success' => true,
            'message' => count($request->ids) . ' media item(s) deleted successfully.'
        ]);
    }

    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2',
            'type' => 'nullable|in:images,documents,videos',
            'collection' => 'nullable|string'
        ]);

        $query = Media::where('name', 'like', '%' . $request->q . '%');

        if ($request->type) {
            switch ($request->type) {
                case 'images':
                    $query->where('mime_type', 'like', 'image/%');
                    break;
                case 'documents':
                    $query->whereIn('mime_type', ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);
                    break;
                case 'videos':
                    $query->where('mime_type', 'like', 'video/%');
                    break;
            }
        }

        if ($request->collection) {
            $query->where('collection_name', $request->collection);
        }

        $media = $query->orderBy('created_at', 'desc')->limit(20)->get();

        return response()->json([
            'success' => true,
            'media' => $media->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'url' => $item->getUrl(),
                    'thumb_url' => $item->hasGeneratedConversion('thumb') ? $item->getUrl('thumb') : $item->getUrl(),
                    'size' => $this->formatFileSize($item->size),
                    'mime_type' => $item->mime_type,
                    'collection' => $item->collection_name,
                    'alt_text' => $item->getCustomProperty('alt_text'),
                    'caption' => $item->getCustomProperty('caption'),
                ];
            })
        ]);
    }

    private function generateUniqueFileName($file)
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = \Str::slug($name);
        
        return $name . '_' . time() . '.' . $extension;
    }

    private function optimizeImage(Media $mediaItem)
    {
        if (!str_starts_with($mediaItem->mime_type, 'image/')) {
            return;
        }

        try {
            $manager = new ImageManager(new Driver());
            $image = $manager->read($mediaItem->getPath());

            // Resize if too large
            if ($image->width() > 1920 || $image->height() > 1920) {
                $image->scale(width: 1920, height: 1920);
            }

            // Optimize quality
            $image->save($mediaItem->getPath(), quality: 85);

            // Generate thumbnails
            $thumb = $image->scale(width: 300, height: 300);
            $thumbPath = str_replace($mediaItem->file_name, 'thumb_' . $mediaItem->file_name, $mediaItem->getPath());
            $thumb->save($thumbPath, quality: 80);

        } catch (\Exception $e) {
            // Log error but don't fail the upload
            \Log::warning('Image optimization failed: ' . $e->getMessage());
        }
    }

    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
