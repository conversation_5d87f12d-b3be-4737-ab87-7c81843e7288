<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PageController extends Controller
{
    public function index()
    {
        $pages = Page::with(['country', 'translations'])
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.pages.index', compact('pages'));
    }

    public function create()
    {
        $countries = Country::active()->ordered()->get();
        $pageTypes = Page::getPageTypes();
        $templates = $this->getAvailableTemplates();

        return view('admin.pages.create', compact('countries', 'pageTypes', 'templates'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'page_type' => 'required|string|max:50',
            'country_id' => 'nullable|exists:countries,id',
            'template' => 'required|string|max:100',
            'is_published' => 'boolean',
            'sort_order' => 'integer|min:0',
            'schema_markup' => 'nullable|json',
            
            // English translations (required)
            'en.title' => 'required|string|max:255',
            'en.slug' => 'required|string|max:255|unique:page_translations,slug',
            'en.content' => 'required|string',
            'en.excerpt' => 'nullable|string|max:500',
            'en.meta_title' => 'required|string|max:255',
            'en.meta_description' => 'required|string|max:500',
            'en.meta_keywords' => 'nullable|string|max:500',
        ]);

        $page = Page::create([
            'page_type' => $request->page_type,
            'country_id' => $request->country_id,
            'template' => $request->template,
            'is_published' => $request->boolean('is_published'),
            'published_at' => $request->boolean('is_published') ? now() : null,
            'sort_order' => $request->sort_order ?? 0,
            'schema_markup' => $request->schema_markup ? json_decode($request->schema_markup, true) : null,
        ]);

        // Save English translation
        $page->translateOrNew('en')->fill([
            'title' => $request->input('en.title'),
            'slug' => Str::slug($request->input('en.slug')),
            'content' => $request->input('en.content'),
            'excerpt' => $request->input('en.excerpt'),
            'meta_title' => $request->input('en.meta_title'),
            'meta_description' => $request->input('en.meta_description'),
            'meta_keywords' => $request->input('en.meta_keywords'),
            'canonical_url' => $this->generateCanonicalUrl($request->input('en.slug')),
            'og_title' => $request->input('en.meta_title'),
            'og_description' => $request->input('en.meta_description'),
        ]);

        $page->save();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page created successfully.');
    }

    public function show(Page $page)
    {
        $page->load(['country', 'translations']);
        return view('admin.pages.show', compact('page'));
    }

    public function edit(Page $page)
    {
        $countries = Country::active()->ordered()->get();
        $pageTypes = Page::getPageTypes();
        $templates = $this->getAvailableTemplates();
        $page->load(['country', 'translations']);

        return view('admin.pages.edit', compact('page', 'countries', 'pageTypes', 'templates'));
    }

    public function update(Request $request, Page $page)
    {
        $request->validate([
            'page_type' => 'required|string|max:50',
            'country_id' => 'nullable|exists:countries,id',
            'template' => 'required|string|max:100',
            'is_published' => 'boolean',
            'sort_order' => 'integer|min:0',
            'schema_markup' => 'nullable|json',
            
            // English translations (required)
            'en.title' => 'required|string|max:255',
            'en.slug' => 'required|string|max:255|unique:page_translations,slug,' . $page->translate('en')->id,
            'en.content' => 'required|string',
            'en.excerpt' => 'nullable|string|max:500',
            'en.meta_title' => 'required|string|max:255',
            'en.meta_description' => 'required|string|max:500',
            'en.meta_keywords' => 'nullable|string|max:500',
        ]);

        $page->update([
            'page_type' => $request->page_type,
            'country_id' => $request->country_id,
            'template' => $request->template,
            'is_published' => $request->boolean('is_published'),
            'published_at' => $request->boolean('is_published') ? ($page->published_at ?? now()) : null,
            'sort_order' => $request->sort_order ?? 0,
            'schema_markup' => $request->schema_markup ? json_decode($request->schema_markup, true) : null,
        ]);

        // Update English translation
        $page->translateOrNew('en')->fill([
            'title' => $request->input('en.title'),
            'slug' => Str::slug($request->input('en.slug')),
            'content' => $request->input('en.content'),
            'excerpt' => $request->input('en.excerpt'),
            'meta_title' => $request->input('en.meta_title'),
            'meta_description' => $request->input('en.meta_description'),
            'meta_keywords' => $request->input('en.meta_keywords'),
            'canonical_url' => $this->generateCanonicalUrl($request->input('en.slug')),
            'og_title' => $request->input('en.meta_title'),
            'og_description' => $request->input('en.meta_description'),
        ]);

        $page->save();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page updated successfully.');
    }

    public function destroy(Page $page)
    {
        $page->delete();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }

    public function togglePublished(Page $page)
    {
        $page->update([
            'is_published' => !$page->is_published,
            'published_at' => !$page->is_published ? now() : null,
        ]);

        return response()->json([
            'success' => true,
            'is_published' => $page->is_published,
            'message' => $page->is_published ? 'Page published' : 'Page unpublished'
        ]);
    }

    private function getAvailableTemplates()
    {
        return [
            'default' => 'Default Template',
            'country' => 'Country Page Template',
            'buy' => 'Buy Page Template',
            'requirements' => 'Requirements Template',
            'process' => 'Process Template',
            'faq' => 'FAQ Template',
            'contact' => 'Contact Template',
            'about' => 'About Template',
            'legal' => 'Legal Template',
        ];
    }

    private function generateCanonicalUrl($slug)
    {
        return config('app.url') . '/' . $slug;
    }

    public static function getPageTypes()
    {
        return [
            'home' => 'Homepage',
            'country' => 'Country Page',
            'buy' => 'Buy Page',
            'requirements' => 'Requirements Page',
            'process' => 'Process Page',
            'faq' => 'FAQ Page',
            'contact' => 'Contact Page',
            'about' => 'About Page',
            'apply' => 'Application Page',
            'blog' => 'Blog Page',
            'legal' => 'Legal Page'
        ];
    }
}
