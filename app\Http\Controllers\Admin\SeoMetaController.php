<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SeoMeta;
use App\Models\Country;
use Illuminate\Http\Request;

class SeoMetaController extends Controller
{
    public function index()
    {
        $seoMetas = SeoMeta::with(['country', 'translations'])
            ->byPriority()
            ->paginate(20);

        return view('admin.seo-metas.index', compact('seoMetas'));
    }

    public function create()
    {
        $countries = Country::active()->ordered()->get();
        $pageTypes = SeoMeta::getPageTypes();
        $schemaTypes = SeoMeta::getSchemaTypes();

        return view('admin.seo-metas.create', compact('countries', 'pageTypes', 'schemaTypes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'page_type' => 'required|string|max:50',
            'country_id' => 'nullable|exists:countries,id',
            'route_name' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'priority' => 'integer|min:0|max:100',
            'schema_type' => 'required|string|max:50',
            'custom_schema' => 'nullable|json',
            
            // English translations (required)
            'en.meta_title' => 'required|string|max:255',
            'en.meta_description' => 'required|string|max:500',
            'en.meta_keywords' => 'nullable|string|max:500',
            'en.og_title' => 'nullable|string|max:255',
            'en.og_description' => 'nullable|string|max:500',
            'en.og_image' => 'nullable|url|max:500',
            'en.twitter_title' => 'nullable|string|max:255',
            'en.twitter_description' => 'nullable|string|max:500',
            'en.twitter_image' => 'nullable|url|max:500',
            'en.canonical_url' => 'nullable|url|max:500',
        ]);

        $seoMeta = SeoMeta::create([
            'page_type' => $request->page_type,
            'country_id' => $request->country_id,
            'route_name' => $request->route_name,
            'is_active' => $request->boolean('is_active', true),
            'priority' => $request->priority ?? 0,
            'schema_type' => $request->schema_type,
            'custom_schema' => $request->custom_schema ? json_decode($request->custom_schema, true) : null,
        ]);

        // Save English translation
        $seoMeta->translateOrNew('en')->fill([
            'meta_title' => $request->input('en.meta_title'),
            'meta_description' => $request->input('en.meta_description'),
            'meta_keywords' => $request->input('en.meta_keywords'),
            'og_title' => $request->input('en.og_title') ?: $request->input('en.meta_title'),
            'og_description' => $request->input('en.og_description') ?: $request->input('en.meta_description'),
            'og_image' => $request->input('en.og_image'),
            'twitter_title' => $request->input('en.twitter_title') ?: $request->input('en.meta_title'),
            'twitter_description' => $request->input('en.twitter_description') ?: $request->input('en.meta_description'),
            'twitter_image' => $request->input('en.twitter_image') ?: $request->input('en.og_image'),
            'canonical_url' => $request->input('en.canonical_url'),
        ]);

        $seoMeta->save();

        return redirect()->route('admin.seo-metas.index')
            ->with('success', 'SEO Meta created successfully.');
    }

    public function show(SeoMeta $seoMeta)
    {
        $seoMeta->load(['country', 'translations']);
        return view('admin.seo-metas.show', compact('seoMeta'));
    }

    public function edit(SeoMeta $seoMeta)
    {
        $countries = Country::active()->ordered()->get();
        $pageTypes = SeoMeta::getPageTypes();
        $schemaTypes = SeoMeta::getSchemaTypes();
        $seoMeta->load(['country', 'translations']);

        return view('admin.seo-metas.edit', compact('seoMeta', 'countries', 'pageTypes', 'schemaTypes'));
    }

    public function update(Request $request, SeoMeta $seoMeta)
    {
        $request->validate([
            'page_type' => 'required|string|max:50',
            'country_id' => 'nullable|exists:countries,id',
            'route_name' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'priority' => 'integer|min:0|max:100',
            'schema_type' => 'required|string|max:50',
            'custom_schema' => 'nullable|json',
            
            // English translations (required)
            'en.meta_title' => 'required|string|max:255',
            'en.meta_description' => 'required|string|max:500',
            'en.meta_keywords' => 'nullable|string|max:500',
            'en.og_title' => 'nullable|string|max:255',
            'en.og_description' => 'nullable|string|max:500',
            'en.og_image' => 'nullable|url|max:500',
            'en.twitter_title' => 'nullable|string|max:255',
            'en.twitter_description' => 'nullable|string|max:500',
            'en.twitter_image' => 'nullable|url|max:500',
            'en.canonical_url' => 'nullable|url|max:500',
        ]);

        $seoMeta->update([
            'page_type' => $request->page_type,
            'country_id' => $request->country_id,
            'route_name' => $request->route_name,
            'is_active' => $request->boolean('is_active', true),
            'priority' => $request->priority ?? 0,
            'schema_type' => $request->schema_type,
            'custom_schema' => $request->custom_schema ? json_decode($request->custom_schema, true) : null,
        ]);

        // Update English translation
        $seoMeta->translateOrNew('en')->fill([
            'meta_title' => $request->input('en.meta_title'),
            'meta_description' => $request->input('en.meta_description'),
            'meta_keywords' => $request->input('en.meta_keywords'),
            'og_title' => $request->input('en.og_title') ?: $request->input('en.meta_title'),
            'og_description' => $request->input('en.og_description') ?: $request->input('en.meta_description'),
            'og_image' => $request->input('en.og_image'),
            'twitter_title' => $request->input('en.twitter_title') ?: $request->input('en.meta_title'),
            'twitter_description' => $request->input('en.twitter_description') ?: $request->input('en.meta_description'),
            'twitter_image' => $request->input('en.twitter_image') ?: $request->input('en.og_image'),
            'canonical_url' => $request->input('en.canonical_url'),
        ]);

        $seoMeta->save();

        return redirect()->route('admin.seo-metas.index')
            ->with('success', 'SEO Meta updated successfully.');
    }

    public function destroy(SeoMeta $seoMeta)
    {
        $seoMeta->delete();

        return redirect()->route('admin.seo-metas.index')
            ->with('success', 'SEO Meta deleted successfully.');
    }

    public function toggleActive(SeoMeta $seoMeta)
    {
        $seoMeta->update([
            'is_active' => !$seoMeta->is_active,
        ]);

        return response()->json([
            'success' => true,
            'is_active' => $seoMeta->is_active,
            'message' => $seoMeta->is_active ? 'SEO Meta activated' : 'SEO Meta deactivated'
        ]);
    }

    public function preview(SeoMeta $seoMeta)
    {
        $seoMeta->load(['country', 'translations']);
        $seoData = $seoMeta->generateSeoData('en');

        return view('admin.seo-metas.preview', compact('seoMeta', 'seoData'));
    }

    public function bulk(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'ids' => 'required|array',
            'ids.*' => 'exists:seo_metas,id'
        ]);

        $seoMetas = SeoMeta::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $seoMetas->update(['is_active' => true]);
                $message = 'SEO Metas activated successfully.';
                break;
            case 'deactivate':
                $seoMetas->update(['is_active' => false]);
                $message = 'SEO Metas deactivated successfully.';
                break;
            case 'delete':
                $seoMetas->delete();
                $message = 'SEO Metas deleted successfully.';
                break;
        }

        return redirect()->route('admin.seo-metas.index')
            ->with('success', $message);
    }
}
