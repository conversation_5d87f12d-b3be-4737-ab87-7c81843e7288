<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Country;
use App\Services\SeoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApplicationController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Show the application form for a specific country
     */
    public function create(Country $country)
    {
        return view('applications.create', compact('country'));
    }

    /**
     * Start multi-step application process (publicly accessible)
     */
    public function startApplication(Request $request, SeoService $seoService)
    {
        // Get country from request or default to first available
        $countryCode = $request->get('country');
        $country = null;

        if ($countryCode) {
            $country = Country::where('code', $countryCode)->first();
        }

        if (!$country) {
            $country = Country::where('is_active', true)->first();
        }

        if (!$country) {
            return redirect()->route('home')->with('error', 'No countries are currently available for applications.');
        }

        // Create a temporary application for anonymous users
        $application = new Application();
        $application->application_number = Application::generateApplicationNumber();
        $application->country_id = $country->id;
        $application->user_id = null; // Allow null for anonymous applications
        $application->application_status = Application::STATUS_DRAFT;
        $application->session_id = session()->getId(); // Track by session for anonymous users
        $application->save();

        // Redirect to step 1 of the multi-step form
        return redirect()->route('applications.step', ['application' => $application, 'step' => 1]);
    }

    /**
     * Show specific step of multi-step form
     */
    public function showStep(Application $application, $step)
    {
        // Validate step number
        if (!in_array($step, [1, 2, 3, 4])) {
            return redirect()->route('applications.step', ['application' => $application, 'step' => 1]);
        }

        // Check if user can access this application
        if (!$this->canAccessApplication($application)) {
            abort(403);
        }

        $stepData = $this->getStepData($step);

        return view('applications.multi-step', compact('application', 'step', 'stepData'));
    }

    /**
     * Save step data and proceed to next step
     */
    public function saveStep(Request $request, Application $application, $step)
    {
        // Debug logging
        Log::info('SaveStep called', [
            'step' => $step,
            'application_id' => $application->id,
            'user_id' => Auth::id(),
            'request_data' => $request->all()
        ]);

        // Check if user can edit this application
        if (!$this->canAccessApplication($application)) {
            Log::warning('Access denied for saveStep', [
                'user_id' => Auth::id(),
                'application_user_id' => $application->user_id,
                'is_guest' => Auth::guest(),
                'session_id' => session()->getId(),
                'application_session_id' => $application->session_id
            ]);
            abort(403);
        }

        // Validate based on current step
        $validator = $this->getStepValidator($request, $step);

        if ($validator->fails()) {
            Log::warning('Validation failed for step', [
                'step' => $step,
                'errors' => $validator->errors()->toArray()
            ]);
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Save step data
            $this->saveStepData($application, $request, $step);

            DB::commit();

            $nextStep = $step + 1;
            $redirectUrl = $nextStep <= 4
                ? route('applications.step', ['application' => $application, 'step' => $nextStep])
                : route('applications.review', $application);

            return response()->json([
                'success' => true,
                'message' => 'Step saved successfully',
                'redirect_url' => $redirectUrl
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'There was an error saving your data. Please try again.'
            ], 500);
        }
    }

    /**
     * Show review page before final submission
     */
    public function showReview(Application $application)
    {
        // Check if user can view this application
        if (!$this->canAccessApplication($application)) {
            abort(403);
        }

        return view('applications.review', compact('application'));
    }

    /**
     * Handle file uploads for documents
     */
    public function uploadDocument(Request $request, Application $application)
    {
        // Check if user can edit this application
        if (!$this->canAccessApplication($application)) {
            abort(403);
        }

        $request->validate([
            'document' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'document_type' => 'required|in:signature,id_photo,passport_photo,additional'
        ]);

        try {
            $file = $request->file('document');
            $documentType = $request->input('document_type');

            // Generate unique filename
            $filename = $documentType . '_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();

            // Store file
            $path = $file->storeAs('applications/' . $application->id . '/documents', $filename, 'private');

            // Update application documents
            $documents = $application->documents_uploaded ?? [];
            $documents[$documentType] = [
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'path' => $path,
                'uploaded_at' => now()->toISOString()
            ];

            $application->documents_uploaded = $documents;
            $application->save();

            return response()->json([
                'success' => true,
                'message' => 'Document uploaded successfully',
                'document' => $documents[$documentType]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'There was an error uploading your document. Please try again.'
            ], 500);
        }
    }

    /**
     * Store a new application
     */
    public function store(Request $request, Country $country)
    {
        $validator = Validator::make($request->all(), [
            // Personal Information
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'date_of_birth' => 'required|date|before:today',
            'place_of_birth' => 'required|string|max:255',
            'nationality' => 'required|string|max:255',

            // Address Information
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',

            // License Information
            'license_category' => 'required|in:A1,A2,A,B,C1,C,D1,D',
            'previous_license_number' => 'nullable|string|max:50',
            'previous_license_country' => 'nullable|string|max:255',

            // Emergency Contact
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',

            // Delivery Information
            'delivery_address_line_1' => 'required|string|max:255',
            'delivery_address_line_2' => 'nullable|string|max:255',
            'delivery_city' => 'required|string|max:255',
            'delivery_postal_code' => 'required|string|max:20',
            'delivery_country' => 'required|string|max:255',

            // Package Options
            'package_type' => 'required|in:standard,express,premium',
            'processing_speed' => 'required|in:standard,express,urgent',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Create the application
            $application = new Application();
            $application->application_number = Application::generateApplicationNumber();
            $application->country_id = $country->id;
            $application->user_id = Auth::id();

            // Fill application data
            $application->fill($request->all());

            // Calculate total amount
            $application->total_amount = $this->calculateTotalAmount($country, $request->package_type, $request->processing_speed);

            $application->save();

            DB::commit();

            return redirect()->route('applications.show', $application)
                ->with('success', 'Application submitted successfully! Your application number is: ' . $application->application_number);

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'There was an error submitting your application. Please try again.')
                ->withInput();
        }
    }

    /**
     * Show a specific application
     */
    public function show(Application $application)
    {
        // Check if user can view this application
        if (Auth::guest() || (Auth::user()->id !== $application->user_id && !Auth::user()->is_admin)) {
            abort(403);
        }

        return view('applications.show', compact('application'));
    }

    /**
     * Show the edit form for an application
     */
    public function edit(Application $application)
    {
        // Check if user can edit this application
        if (Auth::guest() || (Auth::user()->id !== $application->user_id && !Auth::user()->is_admin)) {
            abort(403);
        }

        if (!$application->canBeEdited()) {
            return redirect()->route('applications.show', $application)
                ->with('error', 'This application cannot be edited in its current status.');
        }

        return view('applications.edit', compact('application'));
    }

    /**
     * Update an application
     */
    public function update(Request $request, Application $application)
    {
        // Check if user can edit this application
        if (Auth::guest() || (Auth::user()->id !== $application->user_id && !Auth::user()->is_admin)) {
            abort(403);
        }

        if (!$application->canBeEdited()) {
            return redirect()->route('applications.show', $application)
                ->with('error', 'This application cannot be edited in its current status.');
        }

        $validator = Validator::make($request->all(), [
            // Same validation rules as store method
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'date_of_birth' => 'required|date|before:today',
            'place_of_birth' => 'required|string|max:255',
            'nationality' => 'required|string|max:255',
            'address_line_1' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'license_category' => 'required|in:A1,A2,A,B,C1,C,D1,D',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'delivery_address_line_1' => 'required|string|max:255',
            'delivery_city' => 'required|string|max:255',
            'delivery_postal_code' => 'required|string|max:20',
            'delivery_country' => 'required|string|max:255',
            'package_type' => 'required|in:standard,express,premium',
            'processing_speed' => 'required|in:standard,express,urgent',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Update application data
            $application->fill($request->all());

            // Recalculate total amount
            $application->total_amount = $this->calculateTotalAmount($application->country, $request->package_type, $request->processing_speed);

            $application->save();

            DB::commit();

            return redirect()->route('applications.show', $application)
                ->with('success', 'Application updated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                ->with('error', 'There was an error updating your application. Please try again.')
                ->withInput();
        }
    }

    /**
     * Track an application by application number
     */
    public function track(Request $request)
    {
        if ($request->has('application_number')) {
            $application = Application::where('application_number', $request->application_number)->first();

            if ($application) {
                return view('applications.track', compact('application'));
            } else {
                return view('applications.track')
                    ->with('error', 'Application not found. Please check your application number.');
            }
        }

        return view('applications.track');
    }

    /**
     * Submit application for processing
     */
    public function submit(Application $application)
    {
        // Check if user can submit this application
        if (Auth::guest() || (Auth::user()->id !== $application->user_id && !Auth::user()->is_admin)) {
            abort(403);
        }

        if ($application->application_status !== Application::STATUS_DRAFT) {
            return redirect()->route('applications.show', $application)
                ->with('error', 'This application has already been submitted.');
        }

        try {
            $application->updateStatus(Application::STATUS_SUBMITTED, 'Application submitted by user');

            return redirect()->route('applications.show', $application)
                ->with('success', 'Application submitted successfully! You will receive updates via email.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'There was an error submitting your application. Please try again.');
        }
    }

    /**
     * Cancel an application
     */
    public function cancel(Application $application)
    {
        // Check if user can cancel this application
        if (Auth::guest() || (Auth::user()->id !== $application->user_id && !Auth::user()->is_admin)) {
            abort(403);
        }

        if (!$application->canBeCancelled()) {
            return redirect()->route('applications.show', $application)
                ->with('error', 'This application cannot be cancelled in its current status.');
        }

        try {
            $application->updateStatus(Application::STATUS_CANCELLED, 'Application cancelled by user');

            return redirect()->route('applications.show', $application)
                ->with('success', 'Application cancelled successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'There was an error cancelling your application. Please try again.');
        }
    }

    /**
     * Check if current user/session can access the application
     */
    private function canAccessApplication(Application $application)
    {
        // Admin can access any application
        if (Auth::check() && Auth::user()->is_admin) {
            return true;
        }

        // If user is logged in and owns the application
        if (Auth::check() && Auth::user()->id === $application->user_id) {
            return true;
        }

        // If anonymous user and session matches
        if (Auth::guest() && $application->user_id === null && $application->session_id === session()->getId()) {
            return true;
        }

        return false;
    }

    /**
     * Get or create draft application for user
     */
    private function getOrCreateDraftApplication(Country $country)
    {
        // Check if user has existing draft application for this country
        $application = Application::where('user_id', Auth::id())
            ->where('country_id', $country->id)
            ->where('application_status', Application::STATUS_DRAFT)
            ->first();

        if (!$application) {
            $application = new Application();
            $application->application_number = Application::generateApplicationNumber();
            $application->country_id = $country->id;
            $application->user_id = Auth::id();
            $application->application_status = Application::STATUS_DRAFT;
            $application->save();
        }

        return $application;
    }

    /**
     * Get step configuration data
     */
    private function getStepData($step)
    {
        $steps = [
            1 => [
                'title' => __('messages.apply.step1_title'),
                'description' => __('messages.apply.step1_description'),
                'fields' => ['first_name', 'last_name', 'email', 'phone', 'date_of_birth', 'place_of_birth', 'nationality', 'has_existing_license', 'previous_license_number', 'previous_license_country']
            ],
            2 => [
                'title' => __('messages.apply.step2_title'),
                'description' => __('messages.apply.step2_description'),
                'fields' => ['signature', 'id_photo', 'passport_photo', 'old_license_front', 'old_license_back']
            ],
            3 => [
                'title' => __('messages.apply.step3_title'),
                'description' => __('messages.apply.step3_description'),
                'fields' => ['license_category', 'package_type', 'processing_speed', 'special_requirements']
            ],
            4 => [
                'title' => __('messages.apply.step4_title'),
                'description' => __('messages.apply.step4_description'),
                'fields' => []
            ]
        ];

        return $steps[$step] ?? $steps[1];
    }

    /**
     * Get validator for specific step
     */
    private function getStepValidator(Request $request, $step)
    {
        $rules = [];

        switch ($step) {
            case 1:
                $rules = [
                    'first_name' => 'required|string|max:255',
                    'last_name' => 'required|string|max:255',
                    'email' => 'required|email|max:255',
                    'phone' => 'required|string|max:20',
                    'date_of_birth' => 'required|date|before:today',
                    'place_of_birth' => 'required|string|max:255',
                    'nationality' => 'required|string|max:255',
                    'has_existing_license' => 'nullable|boolean',
                    'previous_license_number' => 'required_if:has_existing_license,1|string|max:255',
                    'previous_license_country' => 'required_if:has_existing_license,1|string|max:255',
                ];
                break;
            case 2:
                // Document uploads are handled separately
                $rules = [];
                break;
            case 3:
                $rules = [
                    'license_category' => 'required|in:A1,A2,A,B,C1,C,D1,D',
                    'package_type' => 'required|in:standard,express,premium',
                    'processing_speed' => 'required|in:standard,express,urgent',
                    'special_requirements' => 'nullable|string|max:1000',
                ];
                break;
            case 4:
                // Review step - no additional validation
                $rules = [];
                break;
        }

        return Validator::make($request->all(), $rules);
    }

    /**
     * Save step data to application
     */
    private function saveStepData(Application $application, Request $request, $step)
    {
        switch ($step) {
            case 1:
                $application->fill($request->only([
                    'first_name', 'last_name', 'email', 'phone',
                    'date_of_birth', 'place_of_birth', 'nationality'
                ]));

                // Handle existing license checkbox
                if ($request->has('has_existing_license') && $request->has_existing_license) {
                    $application->previous_license_number = $request->previous_license_number;
                    $application->previous_license_country = $request->previous_license_country;
                } else {
                    $application->previous_license_number = null;
                    $application->previous_license_country = null;
                }
                break;
            case 2:
                // Documents are handled via separate upload endpoint
                break;
            case 3:
                $application->fill($request->only([
                    'license_category', 'package_type', 'processing_speed', 'special_requirements'
                ]));
                break;
            case 4:
                // Final review - mark as ready for submission
                $application->application_status = Application::STATUS_PENDING_REVIEW;
                break;
        }

        $application->save();
    }

    /**
     * Calculate total amount based on package and processing options
     */
    private function calculateTotalAmount(Country $country, $packageType, $processingSpeed)
    {
        $basePrice = $country->base_price;

        // Package type multipliers
        $packageMultiplier = match($packageType) {
            'express' => 1.2,
            'premium' => 1.5,
            default => 1.0,
        };

        // Processing speed additions
        $speedAddition = match($processingSpeed) {
            'express' => 50,
            'urgent' => 100,
            default => 0,
        };

        return ($basePrice * $packageMultiplier) + $speedAddition;
    }
}
