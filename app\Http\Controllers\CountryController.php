<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Country;
use App\Services\SeoService;

class CountryController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    public function show(Country $country)
    {
        return view('countries.show', compact('country'));
    }

    public function buy(Country $country)
    {
        return view('countries.buy', compact('country'));
    }

    public function requirements(Country $country)
    {
        return view('countries.requirements', compact('country'));
    }

    public function process(Country $country)
    {
        return view('countries.process', compact('country'));
    }

    public function faq(Country $country)
    {
        return view('countries.faq', compact('country'));
    }
}
