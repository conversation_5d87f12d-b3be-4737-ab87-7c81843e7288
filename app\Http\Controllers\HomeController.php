<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Country;
use App\Models\Testimonial;
use App\Services\SeoService;

class HomeController extends Controller
{
    public function index()
    {
        $countries = Country::active()->ordered()->get();
        $testimonials = Testimonial::active()->ordered()->get();

        $seoService = new SeoService();
        $seoData = $seoService->generateMetaTags('home');

        return view('home.index', compact('countries', 'testimonials', 'seoData'));
    }

    public function howItWorks()
    {
        $seoData = [
            'title' => 'How It Works - EU Driving License Online Process',
            'description' => 'Learn how to get your EU driving license online in 5 simple steps. Fast, legal, and secure process with money-back guarantee.',
            'keywords' => 'how to get eu driving license, driving license process, online driving license steps',
            'canonical' => url('/how-it-works')
        ];

        return view('home.how-it-works', compact('seoData'));
    }

    public function pricing()
    {
        $countries = Country::active()->ordered()->get();

        $seoData = [
            'title' => 'EU Driving License Prices - Transparent Pricing',
            'description' => 'View our transparent pricing for EU driving licenses. No hidden fees, money-back guarantee. Compare prices for all EU countries.',
            'keywords' => 'eu driving license price, driving license cost, european license pricing',
            'canonical' => url('/pricing')
        ];

        return view('home.pricing', compact('countries', 'seoData'));
    }

    public function contact()
    {
        $seoService = new SeoService();
        $seoData = $seoService->generateMetaTags('contact');

        return view('home.contact', compact('seoData'));
    }

    /**
     * Handle contact form submission
     */
    public function sendContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email or save to database
        // For now, we'll just return a success response

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => __('messages.contact.success_message')
            ]);
        }

        return redirect()->back()->with('success', __('messages.contact.success_message'));
    }

    public function about()
    {
        $seoData = [
            'title' => __('messages.about.page_title'),
            'description' => __('messages.about.meta_description'),
            'keywords' => __('messages.about.meta_keywords'),
            'canonical' => localized_route('about'),
            'og_image' => asset('images/about/about-og-image.webp'),
            'structured_data' => [
                '@context' => 'https://schema.org',
                '@type' => 'Organization',
                'name' => config('app.name'),
                'description' => __('messages.about.company_description'),
                'url' => url('/'),
                'logo' => asset('images/logo.webp'),
                'foundingDate' => '2020',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressCountry' => 'EU'
                ],
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => __('messages.contact.phone'),
                    'contactType' => 'customer service',
                    'email' => __('messages.contact.email')
                ],
                'aggregateRating' => [
                    '@type' => 'AggregateRating',
                    'ratingValue' => '4.9',
                    'reviewCount' => '15000',
                    'bestRating' => '5'
                ],
                'sameAs' => [
                    'https://facebook.com/' . config('app.name'),
                    'https://twitter.com/' . config('app.name'),
                    'https://linkedin.com/company/' . config('app.name')
                ]
            ]
        ];

        return view('home.about', compact('seoData'));
    }

    public function privacy()
    {
        $seoData = [
            'title' => 'Privacy Policy - EU Driving License Service',
            'description' => 'Read our privacy policy for EU driving license service. We protect your personal data and ensure GDPR compliance.',
            'keywords' => 'privacy policy, data protection, gdpr compliance',
            'canonical' => url('/privacy-policy')
        ];

        return view('home.privacy', compact('seoData'));
    }

    public function terms()
    {
        $seoData = [
            'title' => 'Terms of Service - EU Driving License',
            'description' => 'Read our terms of service for EU driving license. Clear terms, fair policies, legal compliance guaranteed.',
            'keywords' => 'terms of service, legal terms, service agreement',
            'canonical' => url('/terms-of-service')
        ];

        return view('home.terms', compact('seoData'));
    }
}
