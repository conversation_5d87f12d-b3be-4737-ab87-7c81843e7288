<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Country;
use App\Models\Page;
use Carbon\Carbon;

class SitemapController extends Controller
{
    /**
     * Generate and return the sitemap
     */
    public function index()
    {
        $sitemap = Sitemap::create();
        
        // Add homepage for all languages
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        
        foreach ($supportedLocales as $locale) {
            $url = $locale === 'en' ? config('app.url') : config('app.url') . '/' . $locale;
            
            $sitemap->add(
                Url::create($url)
                    ->setLastModificationDate(Carbon::now())
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                    ->setPriority(1.0)
            );
        }
        
        // Add country pages
        $countries = Country::active()->get();
        
        foreach ($countries as $country) {
            foreach ($supportedLocales as $locale) {
                $baseUrl = $locale === 'en' ? config('app.url') : config('app.url') . '/' . $locale;
                
                // Country main page
                $sitemap->add(
                    Url::create($baseUrl . '/driving-license/' . $country->code)
                        ->setLastModificationDate($country->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority(0.9)
                );
                
                // Buy page
                $sitemap->add(
                    Url::create($baseUrl . '/buy-' . $country->code . '-driving-licence-online')
                        ->setLastModificationDate($country->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority(0.8)
                );
                
                // Requirements page
                $sitemap->add(
                    Url::create($baseUrl . '/' . $country->code . '-driving-license-requirements')
                        ->setLastModificationDate($country->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority(0.7)
                );
                
                // Process page
                $sitemap->add(
                    Url::create($baseUrl . '/' . $country->code . '-driving-license-process')
                        ->setLastModificationDate($country->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority(0.7)
                );
                
                // FAQ page
                $sitemap->add(
                    Url::create($baseUrl . '/' . $country->code . '-driving-license-faq')
                        ->setLastModificationDate($country->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority(0.6)
                );
            }
        }
        
        // Add static pages
        $staticPages = [
            '/contact' => 0.6,
            '/about' => 0.5,
            '/how-it-works' => 0.7,
            '/apply' => 0.8,
        ];
        
        foreach ($staticPages as $page => $priority) {
            foreach ($supportedLocales as $locale) {
                $url = $locale === 'en' ? config('app.url') . $page : config('app.url') . '/' . $locale . $page;
                
                $sitemap->add(
                    Url::create($url)
                        ->setLastModificationDate(Carbon::now())
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority($priority)
                );
            }
        }
        
        // Add dynamic pages if they exist
        $pages = Page::published()->get();
        foreach ($pages as $page) {
            foreach ($supportedLocales as $locale) {
                $url = $locale === 'en' ? config('app.url') : config('app.url') . '/' . $locale;
                $url .= '/' . $page->translate($locale)->slug;
                
                $sitemap->add(
                    Url::create($url)
                        ->setLastModificationDate($page->updated_at)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                        ->setPriority(0.5)
                );
            }
        }
        
        return $sitemap->toResponse(request());
    }
    
    /**
     * Generate sitemap and save to file
     */
    public function generate()
    {
        $sitemap = $this->index();
        $sitemap->writeToFile(public_path('sitemap.xml'));
        
        return response()->json(['message' => 'Sitemap generated successfully']);
    }
}
