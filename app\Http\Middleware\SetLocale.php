<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Get the locale from the URL segment
        $locale = $request->segment(1);

        // Define supported locales
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl', 'ie', 'ga'];

        // Check if the locale is supported
        if (in_array($locale, $supportedLocales)) {
            // Set the application locale
            App::setLocale($locale);

            // Store in session for persistence
            Session::put('locale', $locale);

            // Also set the config for this request
            config(['app.locale' => $locale]);
        } else {
            // Default to English if locale is not supported
            App::setLocale('en');
            Session::put('locale', 'en');
            config(['app.locale' => 'en']);
        }

        return $next($request);
    }
}
