<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Application extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'application_number',
        'country_id',
        'user_id',
        'session_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'place_of_birth',
        'nationality',
        'address_line_1',
        'address_line_2',
        'city',
        'postal_code',
        'country',
        'license_category',
        'previous_license_number',
        'previous_license_country',
        'medical_conditions',
        'emergency_contact_name',
        'emergency_contact_phone',
        'delivery_address_line_1',
        'delivery_address_line_2',
        'delivery_city',
        'delivery_postal_code',
        'delivery_country',
        'package_type',
        'processing_speed',
        'special_requirements',
        'total_amount',
        'payment_status',
        'payment_method',
        'payment_reference',
        'application_status',
        'documents_uploaded',
        'verification_status',
        'notes',
        'submitted_at',
        'processed_at',
        'completed_at',
        'tracking_number',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'submitted_at' => 'datetime',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime',
        'documents_uploaded' => 'boolean',
        'total_amount' => 'decimal:2',
    ];

    protected $attributes = [
        'documents_uploaded' => false,
        'application_status' => 'draft',
        'payment_status' => 'pending',
        'verification_status' => 'pending',
    ];

    // Application statuses
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING_REVIEW = 'pending_review';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';
    const STATUS_DOCUMENTS_REQUIRED = 'documents_required';
    const STATUS_PAYMENT_PENDING = 'payment_pending';
    const STATUS_PAYMENT_CONFIRMED = 'payment_confirmed';
    const STATUS_PROCESSING = 'processing';
    const STATUS_READY_FOR_DELIVERY = 'ready_for_delivery';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REJECTED = 'rejected';

    // Payment statuses
    const PAYMENT_PENDING = 'pending';
    const PAYMENT_COMPLETED = 'completed';
    const PAYMENT_FAILED = 'failed';
    const PAYMENT_REFUNDED = 'refunded';

    // Package types
    const PACKAGE_STANDARD = 'standard';
    const PACKAGE_EXPRESS = 'express';
    const PACKAGE_PREMIUM = 'premium';

    // Processing speeds
    const SPEED_STANDARD = 'standard';
    const SPEED_EXPRESS = 'express';
    const SPEED_URGENT = 'urgent';

    // License categories
    const CATEGORY_A1 = 'A1'; // Light motorcycles
    const CATEGORY_A2 = 'A2'; // Medium motorcycles
    const CATEGORY_A = 'A';   // Heavy motorcycles
    const CATEGORY_B = 'B';   // Cars
    const CATEGORY_C1 = 'C1'; // Light trucks
    const CATEGORY_C = 'C';   // Heavy trucks
    const CATEGORY_D1 = 'D1'; // Minibuses
    const CATEGORY_D = 'D';   // Buses

    /**
     * Get the country that owns the application
     */
    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the user that owns the application
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the documents for the application
     */
    // public function documents()
    // {
    //     return $this->hasMany(ApplicationDocument::class);
    // }

    /**
     * Get the payments for the application
     */
    // public function payments()
    // {
    //     return $this->hasMany(Payment::class);
    // }

    /**
     * Get the status history for the application
     */
    // public function statusHistory()
    // {
    //     return $this->hasMany(ApplicationStatusHistory::class);
    // }

    /**
     * Generate a unique application number
     */
    public static function generateApplicationNumber()
    {
        $prefix = 'EU';
        $year = date('Y');
        $month = date('m');

        // Get the last application number for this month
        $lastApplication = self::where('application_number', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('application_number', 'desc')
            ->first();

        if ($lastApplication) {
            $lastNumber = (int) substr($lastApplication->application_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate a tracking number
     */
    public function generateTrackingNumber()
    {
        $prefix = 'TRK';
        $countryCode = strtoupper($this->country->code);
        $random = strtoupper(substr(md5(uniqid()), 0, 8));

        return $prefix . $countryCode . $random;
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->application_status) {
            self::STATUS_DRAFT => 'gray',
            self::STATUS_PENDING_REVIEW => 'yellow',
            self::STATUS_SUBMITTED => 'blue',
            self::STATUS_UNDER_REVIEW => 'yellow',
            self::STATUS_DOCUMENTS_REQUIRED => 'orange',
            self::STATUS_PAYMENT_PENDING => 'purple',
            self::STATUS_PAYMENT_CONFIRMED => 'indigo',
            self::STATUS_PROCESSING => 'blue',
            self::STATUS_READY_FOR_DELIVERY => 'green',
            self::STATUS_DELIVERED => 'green',
            self::STATUS_CANCELLED => 'red',
            self::STATUS_REJECTED => 'red',
            default => 'gray',
        };
    }

    /**
     * Get human readable status
     */
    public function getStatusLabelAttribute()
    {
        return match($this->application_status) {
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_PENDING_REVIEW => 'Pending Review',
            self::STATUS_SUBMITTED => 'Submitted',
            self::STATUS_UNDER_REVIEW => 'Under Review',
            self::STATUS_DOCUMENTS_REQUIRED => 'Documents Required',
            self::STATUS_PAYMENT_PENDING => 'Payment Pending',
            self::STATUS_PAYMENT_CONFIRMED => 'Payment Confirmed',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_READY_FOR_DELIVERY => 'Ready for Delivery',
            self::STATUS_DELIVERED => 'Delivered',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_REJECTED => 'Rejected',
            default => 'Unknown',
        };
    }

    /**
     * Get package price based on type and speed
     */
    public function calculateTotalAmount()
    {
        $basePrice = $this->country->base_price;

        // Package type multipliers
        $packageMultiplier = match($this->package_type) {
            self::PACKAGE_EXPRESS => 1.2,
            self::PACKAGE_PREMIUM => 1.5,
            default => 1.0,
        };

        // Processing speed additions
        $speedAddition = match($this->processing_speed) {
            self::SPEED_EXPRESS => 50,
            self::SPEED_URGENT => 100,
            default => 0,
        };

        return ($basePrice * $packageMultiplier) + $speedAddition;
    }

    /**
     * Check if application can be edited
     */
    public function canBeEdited()
    {
        return in_array($this->application_status, [
            self::STATUS_DRAFT,
            self::STATUS_DOCUMENTS_REQUIRED,
        ]);
    }

    /**
     * Check if application can be cancelled
     */
    public function canBeCancelled()
    {
        return !in_array($this->application_status, [
            self::STATUS_PROCESSING,
            self::STATUS_READY_FOR_DELIVERY,
            self::STATUS_DELIVERED,
            self::STATUS_CANCELLED,
            self::STATUS_REJECTED,
        ]);
    }

    /**
     * Update application status
     */
    public function updateStatus($newStatus, $notes = null)
    {
        $oldStatus = $this->application_status;
        $this->application_status = $newStatus;

        // Set timestamps based on status
        switch ($newStatus) {
            case self::STATUS_SUBMITTED:
                $this->submitted_at = now();
                break;
            case self::STATUS_PROCESSING:
                $this->processed_at = now();
                break;
            case self::STATUS_DELIVERED:
                $this->completed_at = now();
                break;
        }

        $this->save();

        // Create status history record
        // TODO: Implement when ApplicationStatusHistory model is created
        // $this->statusHistory()->create([
        //     'old_status' => $oldStatus,
        //     'new_status' => $newStatus,
        //     'notes' => $notes,
        //     'changed_by' => auth()->id(),
        //     'changed_at' => now(),
        // ]);

        return $this;
    }

    /**
     * Scope for filtering by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('application_status', $status);
    }

    /**
     * Scope for filtering by country
     */
    public function scopeByCountry($query, $countryId)
    {
        return $query->where('country_id', $countryId);
    }

    /**
     * Scope for recent applications
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
