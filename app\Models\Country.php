<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class Country extends Model implements TranslatableContract
{
    use HasFactory, Translatable;

    protected $fillable = [
        'code',
        'flag_url',
        'is_active',
        'sort_order',
        'base_price',
        'processing_days_min',
        'processing_days_max'
    ];

    public $translatedAttributes = [
        'name',
        'slug',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'content',
        'excerpt',
        'requirements',
        'process_steps',
        'faq'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'base_price' => 'decimal:2',
        'requirements' => 'array',
        'process_steps' => 'array',
        'faq' => 'array'
    ];

    protected $with = ['translations'];

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function resolveRouteBinding($value, $field = null)
    {
        // Always resolve using English slug for consistent URLs
        return $this->whereTranslation('slug', $value, 'en')->first();
    }

    public function pages()
    {
        return $this->hasMany(Page::class);
    }

    public function applications()
    {
        return $this->hasMany(Application::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function getProcessingTimeAttribute()
    {
        if ($this->processing_days_min === $this->processing_days_max) {
            return $this->processing_days_min . ' days';
        }

        return $this->processing_days_min . '-' . $this->processing_days_max . ' days';
    }

    public function getNameAttribute()
    {
        // Get the name from the current translation, fallback to English
        $translation = $this->translate(app()->getLocale()) ?? $this->translate('en');
        return $translation ? $translation->name : 'Unknown Country';
    }

    public function getSlugAttribute()
    {
        // Always use English slug for consistent URLs across locales
        $translation = $this->translate('en');
        return $translation ? $translation->slug : 'unknown-country';
    }

    public function getRouteKey()
    {
        return $this->slug;
    }
}
