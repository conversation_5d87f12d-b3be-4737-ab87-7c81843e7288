<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CountryTranslation extends Model
{
    protected $fillable = [
        'locale',
        'name',
        'slug',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'content',
        'excerpt',
        'requirements',
        'process_steps',
        'faq'
    ];

    protected $casts = [
        'requirements' => 'array',
        'process_steps' => 'array',
        'faq' => 'array'
    ];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
