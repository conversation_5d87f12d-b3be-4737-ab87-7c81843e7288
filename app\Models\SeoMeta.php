<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class SeoMeta extends Model implements TranslatableContract
{
    use HasFactory, Translatable;

    protected $table = 'seo_metas';

    protected $fillable = [
        'page_type',
        'country_id',
        'route_name',
        'is_active',
        'priority',
        'schema_type',
        'custom_schema'
    ];

    public $translatedAttributes = [
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'canonical_url',
        'custom_meta_tags'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'priority' => 'integer',
        'custom_schema' => 'array',
        'custom_meta_tags' => 'array'
    ];

    protected $with = ['translations'];

    /**
     * Get the country associated with this SEO meta
     */
    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Scope for active SEO metas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific page type
     */
    public function scopeForPageType($query, $pageType)
    {
        return $query->where('page_type', $pageType);
    }

    /**
     * Scope for specific country
     */
    public function scopeForCountry($query, $countryId)
    {
        return $query->where('country_id', $countryId);
    }

    /**
     * Scope for specific route
     */
    public function scopeForRoute($query, $routeName)
    {
        return $query->where('route_name', $routeName);
    }

    /**
     * Get SEO meta by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Get the best matching SEO meta for given parameters
     */
    public static function getBestMatch($pageType, $countryId = null, $routeName = null)
    {
        $query = static::active()->forPageType($pageType)->byPriority();

        // Try to find exact match with country and route
        if ($countryId && $routeName) {
            $exact = $query->forCountry($countryId)->forRoute($routeName)->first();
            if ($exact) return $exact;
        }

        // Try to find match with country only
        if ($countryId) {
            $countryMatch = $query->forCountry($countryId)->whereNull('route_name')->first();
            if ($countryMatch) return $countryMatch;
        }

        // Try to find match with route only
        if ($routeName) {
            $routeMatch = $query->forRoute($routeName)->whereNull('country_id')->first();
            if ($routeMatch) return $routeMatch;
        }

        // Return generic match for page type
        return $query->whereNull('country_id')->whereNull('route_name')->first();
    }

    /**
     * Generate complete SEO data array
     */
    public function generateSeoData($locale = null)
    {
        $locale = $locale ?? app()->getLocale();

        return [
            'title' => $this->translate($locale)->meta_title,
            'description' => $this->translate($locale)->meta_description,
            'keywords' => $this->translate($locale)->meta_keywords,
            'canonical' => $this->translate($locale)->canonical_url,
            'og_title' => $this->translate($locale)->og_title ?: $this->translate($locale)->meta_title,
            'og_description' => $this->translate($locale)->og_description ?: $this->translate($locale)->meta_description,
            'og_image' => $this->translate($locale)->og_image,
            'twitter_title' => $this->translate($locale)->twitter_title ?: $this->translate($locale)->meta_title,
            'twitter_description' => $this->translate($locale)->twitter_description ?: $this->translate($locale)->meta_description,
            'twitter_image' => $this->translate($locale)->twitter_image ?: $this->translate($locale)->og_image,
            'schema_type' => $this->schema_type,
            'custom_schema' => $this->custom_schema,
            'custom_meta_tags' => $this->translate($locale)->custom_meta_tags
        ];
    }

    /**
     * Get available page types
     */
    public static function getPageTypes()
    {
        return [
            'home' => 'Homepage',
            'country' => 'Country Page',
            'buy' => 'Buy Page',
            'requirements' => 'Requirements Page',
            'process' => 'Process Page',
            'faq' => 'FAQ Page',
            'contact' => 'Contact Page',
            'about' => 'About Page',
            'apply' => 'Application Page',
            'blog' => 'Blog Page',
            'blog_post' => 'Blog Post',
            'legal' => 'Legal Page'
        ];
    }

    /**
     * Get available schema types
     */
    public static function getSchemaTypes()
    {
        return [
            'WebPage' => 'Web Page',
            'Organization' => 'Organization',
            'Service' => 'Service',
            'Product' => 'Product',
            'FAQPage' => 'FAQ Page',
            'ContactPage' => 'Contact Page',
            'AboutPage' => 'About Page',
            'BlogPosting' => 'Blog Post',
            'Article' => 'Article'
        ];
    }
}
