<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SeoMetaTranslation extends Model
{
    protected $fillable = [
        'seo_meta_id',
        'locale',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'canonical_url',
        'custom_meta_tags'
    ];

    protected $casts = [
        'custom_meta_tags' => 'array'
    ];

    public function seoMeta()
    {
        return $this->belongsTo(SeoMeta::class);
    }
}
