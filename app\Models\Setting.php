<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description'
    ];

    protected $casts = [
        'value' => 'json'
    ];

    /**
     * Get a setting value by key
     */
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }

    /**
     * Set a setting value by key
     */
    public static function set($key, $value, $type = 'string', $group = 'general', $description = null)
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'description' => $description
            ]
        );
    }

    /**
     * Get all settings for a specific group
     */
    public static function getGroup($group)
    {
        return static::where('group', $group)->pluck('value', 'key');
    }

    /**
     * Get site logo settings
     */
    public static function getLogos()
    {
        return [
            'main_logo' => static::get('main_logo', 'images/logo.webp'),
            'footer_logo' => static::get('footer_logo', 'images/logo.webp'),
            'mobile_logo' => static::get('mobile_logo', 'images/logo.webp'),
            'sticky_logo' => static::get('sticky_logo', 'images/sticky-logo-2.png'),
            'favicon' => static::get('favicon', 'images/favicon-2.png')
        ];
    }

    /**
     * Get social media links
     */
    public static function getSocialLinks()
    {
        return [
            'facebook' => static::get('social_facebook', 'https://facebook.com'),
            'twitter' => static::get('social_twitter', 'https://twitter.com'),
            'instagram' => static::get('social_instagram', 'https://instagram.com'),
            'linkedin' => static::get('social_linkedin', 'https://linkedin.com'),
            'youtube' => static::get('social_youtube', 'https://youtube.com')
        ];
    }

    /**
     * Get site information
     */
    public static function getSiteInfo()
    {
        return [
            'site_name' => static::get('site_name', config('app.name')),
            'site_description' => static::get('site_description', 'EU Driving License Online'),
            'contact_email' => static::get('contact_email', '<EMAIL>'),
            'contact_phone' => static::get('contact_phone', '+1 (234) 567-890'),
            'address' => static::get('address', 'Europe')
        ];
    }

    /**
     * Scope for specific group
     */
    public function scopeGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Scope for specific type
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }
}
