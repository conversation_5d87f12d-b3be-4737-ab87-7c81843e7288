<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Helpers\LocalizationHelper;

class LocalizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register global helper functions
        if (!function_exists('localized_route')) {
            function localized_route($name, $parameters = [], $locale = null) {
                return LocalizationHelper::route($name, $parameters, $locale);
            }
        }
        
        if (!function_exists('language_switcher_urls')) {
            function language_switcher_urls($currentRoute = null, $parameters = []) {
                return LocalizationHelper::getLanguageSwitcherUrls($currentRoute, $parameters);
            }
        }
        
        if (!function_exists('hreflang_urls')) {
            function hreflang_urls($currentRoute = null, $parameters = []) {
                return LocalizationHelper::getHreflangUrls($currentRoute, $parameters);
            }
        }
        
        if (!function_exists('supported_locales')) {
            function supported_locales() {
                return LocalizationHelper::getSupportedLocales();
            }
        }
    }
}
