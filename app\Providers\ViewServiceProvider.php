<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Country;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share countries with all views for navigation
        View::composer('*', function ($view) {
            $countries = Country::active()->ordered()->get();
            $view->with('countries', $countries);
        });
    }
}
