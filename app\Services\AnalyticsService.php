<?php

namespace App\Services;

class AnalyticsService
{
    /**
     * Generate Google Analytics 4 tracking code
     */
    public function getGA4TrackingCode($measurementId = null)
    {
        $measurementId = $measurementId ?? config('services.google_analytics.measurement_id');
        
        if (!$measurementId) {
            return '';
        }
        
        return "
        <!-- Google tag (gtag.js) -->
        <script async src=\"https://www.googletagmanager.com/gtag/js?id={$measurementId}\"></script>
        <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '{$measurementId}', {
            page_title: document.title,
            page_location: window.location.href,
            language: document.documentElement.lang || 'en'
          });
        </script>
        ";
    }
    
    /**
     * Generate Google Tag Manager code
     */
    public function getGTMCode($containerId = null)
    {
        $containerId = $containerId ?? config('services.google_tag_manager.container_id');
        
        if (!$containerId) {
            return '';
        }
        
        return [
            'head' => "
            <!-- Google Tag Manager -->
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','{$containerId}');</script>
            <!-- End Google Tag Manager -->
            ",
            'body' => "
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src=\"https://www.googletagmanager.com/ns.html?id={$containerId}\"
            height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe></noscript>
            <!-- End Google Tag Manager (noscript) -->
            "
        ];
    }
    
    /**
     * Generate Facebook Pixel code
     */
    public function getFacebookPixelCode($pixelId = null)
    {
        $pixelId = $pixelId ?? config('services.facebook_pixel.pixel_id');
        
        if (!$pixelId) {
            return '';
        }
        
        return "
        <!-- Facebook Pixel Code -->
        <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{$pixelId}');
        fbq('track', 'PageView');
        </script>
        <noscript><img height=\"1\" width=\"1\" style=\"display:none\"
        src=\"https://www.facebook.com/tr?id={$pixelId}&ev=PageView&noscript=1\"
        /></noscript>
        <!-- End Facebook Pixel Code -->
        ";
    }
    
    /**
     * Generate conversion tracking events
     */
    public function getConversionTrackingCode($event, $data = [])
    {
        $events = [];
        
        // Google Analytics 4 event
        if (config('services.google_analytics.measurement_id')) {
            $events[] = "gtag('event', '{$event}', " . json_encode($data) . ");";
        }
        
        // Facebook Pixel event
        if (config('services.facebook_pixel.pixel_id')) {
            $events[] = "fbq('track', '{$event}', " . json_encode($data) . ");";
        }
        
        if (empty($events)) {
            return '';
        }
        
        return "<script>" . implode("\n", $events) . "</script>";
    }
    
    /**
     * Track application submission
     */
    public function trackApplicationSubmission($country, $packageType, $amount)
    {
        return $this->getConversionTrackingCode('Purchase', [
            'currency' => 'EUR',
            'value' => $amount,
            'country' => $country,
            'package_type' => $packageType,
            'event_category' => 'Application',
            'event_label' => $country . '_' . $packageType
        ]);
    }
    
    /**
     * Track page views with enhanced data
     */
    public function trackPageView($page, $country = null, $locale = null)
    {
        $data = [
            'page_title' => $page,
            'page_location' => request()->url(),
            'language' => $locale ?? app()->getLocale(),
        ];
        
        if ($country) {
            $data['country'] = $country;
        }
        
        return $this->getConversionTrackingCode('page_view', $data);
    }
    
    /**
     * Generate structured data for analytics
     */
    public function getStructuredAnalyticsData($page, $country = null)
    {
        return [
            'page_type' => $page,
            'country' => $country,
            'locale' => app()->getLocale(),
            'timestamp' => now()->toISOString(),
            'user_agent' => request()->userAgent(),
            'referrer' => request()->header('referer'),
            'ip' => request()->ip(),
        ];
    }
}
