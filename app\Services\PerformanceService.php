<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Response;

class PerformanceService
{
    /**
     * Cache duration in minutes
     */
    const CACHE_DURATION = 60;
    const LONG_CACHE_DURATION = 1440; // 24 hours

    /**
     * Generate optimized response with caching headers
     */
    public function optimizeResponse($content, $cacheKey = null, $duration = self::CACHE_DURATION)
    {
        if ($cacheKey && Cache::has($cacheKey)) {
            $content = Cache::get($cacheKey);
        } elseif ($cacheKey) {
            Cache::put($cacheKey, $content, $duration);
        }

        return Response::make($content)
            ->header('Cache-Control', 'public, max-age=' . ($duration * 60))
            ->header('Expires', gmdate('D, d M Y H:i:s \G\M\T', time() + ($duration * 60)))
            ->header('Last-Modified', gmdate('D, d M Y H:i:s \G\M\T', time()))
            ->header('ETag', md5($content));
    }

    /**
     * Optimize images for web delivery
     */
    public function optimizeImageAttributes($src, $alt, $options = [])
    {
        $defaults = [
            'loading' => 'lazy',
            'decoding' => 'async',
            'fetchpriority' => 'auto'
        ];

        $attributes = array_merge($defaults, $options);
        
        // Add responsive image attributes if sizes are provided
        if (isset($options['srcset'])) {
            $attributes['srcset'] = $options['srcset'];
        }
        
        if (isset($options['sizes'])) {
            $attributes['sizes'] = $options['sizes'];
        }

        return [
            'src' => $src,
            'alt' => $alt,
            'attributes' => $attributes
        ];
    }

    /**
     * Generate critical CSS for above-the-fold content
     */
    public function getCriticalCSS($page = 'default')
    {
        $criticalCSS = [
            'default' => '
                /* Critical CSS for all pages */
                body { font-family: Inter, sans-serif; margin: 0; }
                .hero-eu { background: linear-gradient(135deg, #003399 0%, #002266 100%); color: white; }
                .btn-eu-primary { background-color: #003399; color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; }
                .container-eu { max-width: 1280px; margin: 0 auto; padding: 0 1rem; }
                .text-responsive-3xl { font-size: clamp(1.875rem, 6vw, 2.25rem); }
                .section-padding-eu { padding: 4rem 0; }
            ',
            'home' => '
                /* Additional critical CSS for homepage */
                .grid { display: grid; }
                .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
                .gap-8 { gap: 2rem; }
                .text-center { text-align: center; }
                .font-bold { font-weight: 700; }
                .text-white { color: white; }
                .bg-gray-50 { background-color: #f9fafb; }
                .card-eu { background: white; border-radius: 0.75rem; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1); }
                .card-eu-body { padding: 1.5rem; }
            ',
            'apply' => '
                /* Critical CSS for application page */
                .form-input-eu { width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; }
                .form-label-eu { display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem; }
                .card-eu-header { padding: 1.5rem; border-bottom: 1px solid #f3f4f6; }
                .bg-eu-blue { background-color: #003399; }
                .text-eu-blue { color: #003399; }
            '
        ];

        $baseCritical = $criticalCSS['default'];
        $pageCritical = $criticalCSS[$page] ?? '';

        return $baseCritical . $pageCritical;
    }

    /**
     * Preload critical resources
     */
    public function getCriticalResourcePreloads($page = 'default')
    {
        $preloads = [
            'default' => [
                [
                    'href' => asset('css/app.css'),
                    'as' => 'style',
                    'type' => 'text/css'
                ],
                [
                    'href' => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
                    'as' => 'style',
                    'type' => 'text/css',
                    'crossorigin' => 'anonymous'
                ]
            ],
            'home' => [
                [
                    'href' => asset('images/hero-driving-license.webp'),
                    'as' => 'image',
                    'type' => 'image/webp'
                ]
            ],
            'apply' => [
                [
                    'href' => asset('js/app.js'),
                    'as' => 'script',
                    'type' => 'text/javascript'
                ]
            ]
        ];

        $defaultPreloads = $preloads['default'];
        $pagePreloads = $preloads[$page] ?? [];

        return array_merge($defaultPreloads, $pagePreloads);
    }

    /**
     * Generate DNS prefetch hints
     */
    public function getDNSPrefetchHints()
    {
        return [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://www.google-analytics.com',
            'https://www.googletagmanager.com',
            'https://connect.facebook.net',
            'https://cdn.jsdelivr.net'
        ];
    }

    /**
     * Optimize database queries with caching
     */
    public function cacheQuery($key, $callback, $duration = self::CACHE_DURATION)
    {
        return Cache::remember($key, $duration, $callback);
    }

    /**
     * Get performance metrics configuration
     */
    public function getPerformanceConfig()
    {
        return [
            'enable_compression' => true,
            'enable_browser_caching' => true,
            'enable_cdn' => config('app.env') === 'production',
            'minify_html' => config('app.env') === 'production',
            'lazy_load_images' => true,
            'preload_critical_resources' => true,
            'enable_service_worker' => config('app.env') === 'production'
        ];
    }

    /**
     * Generate service worker for offline functionality
     */
    public function generateServiceWorker()
    {
        return "
const CACHE_NAME = 'eu-driving-license-v1';
const urlsToCache = [
    '/',
    '/css/app.css',
    '/js/app.js',
    '/images/logo.png',
    '/offline.html'
];

self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});
        ";
    }

    /**
     * Minify HTML output
     */
    public function minifyHTML($html)
    {
        if (config('app.env') !== 'production') {
            return $html;
        }

        // Remove comments
        $html = preg_replace('/<!--(?!<!)[^\[>].*?-->/s', '', $html);
        
        // Remove extra whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        
        // Remove whitespace around tags
        $html = preg_replace('/>\s+</', '><', $html);
        
        return trim($html);
    }

    /**
     * Generate WebP image sources for modern browsers
     */
    public function generateWebPSources($imagePath)
    {
        $pathInfo = pathinfo($imagePath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
        
        return [
            'webp' => $webpPath,
            'fallback' => $imagePath,
            'picture_element' => "
                <picture>
                    <source srcset=\"{$webpPath}\" type=\"image/webp\">
                    <img src=\"{$imagePath}\" alt=\"\" loading=\"lazy\">
                </picture>
            "
        ];
    }

    /**
     * Get Core Web Vitals monitoring script
     */
    public function getCoreWebVitalsScript()
    {
        return "
<script>
// Core Web Vitals monitoring
function sendToAnalytics(metric) {
    if (typeof gtag !== 'undefined') {
        gtag('event', metric.name, {
            event_category: 'Web Vitals',
            event_label: metric.id,
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            non_interaction: true,
        });
    }
}

// Load web-vitals library
import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({getCLS, getFID, getFCP, getLCP, getTTFB}) => {
    getCLS(sendToAnalytics);
    getFID(sendToAnalytics);
    getFCP(sendToAnalytics);
    getLCP(sendToAnalytics);
    getTTFB(sendToAnalytics);
});
</script>
        ";
    }

    /**
     * Generate performance budget alerts
     */
    public function checkPerformanceBudget($metrics)
    {
        $budgets = [
            'LCP' => 2500, // Largest Contentful Paint (ms)
            'FID' => 100,  // First Input Delay (ms)
            'CLS' => 0.1,  // Cumulative Layout Shift
            'FCP' => 1800, // First Contentful Paint (ms)
            'TTFB' => 600  // Time to First Byte (ms)
        ];

        $alerts = [];
        foreach ($budgets as $metric => $budget) {
            if (isset($metrics[$metric]) && $metrics[$metric] > $budget) {
                $alerts[] = [
                    'metric' => $metric,
                    'value' => $metrics[$metric],
                    'budget' => $budget,
                    'status' => 'exceeded'
                ];
            }
        }

        return $alerts;
    }
}
