<?php

namespace App\Services;

use Illuminate\Support\Facades\App;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

class SeoService
{
    /**
     * Generate meta tags for a page
     */
    public function generateMetaTags($page, $country = null, $customData = [])
    {
        $locale = App::getLocale();
        $metaData = $this->getMetaData($page, $country, $locale, $customData);

        return [
            'title' => $metaData['title'],
            'description' => $metaData['description'],
            'keywords' => $metaData['keywords'],
            'canonical' => $metaData['canonical'],
            'hreflang' => $this->generateHreflangTags($page, $country),
            'og_tags' => $this->generateOpenGraphTags($metaData),
            'twitter_tags' => $this->generateTwitterTags($metaData),
            'schema' => $this->generateSchemaMarkup($page, $country, $metaData),
        ];
    }

    /**
     * Get meta data based on page type and country
     */
    private function getMetaData($page, $country, $locale, $customData = [])
    {
        $baseUrl = config('app.url');

        switch ($page) {
            case 'home':
                return $this->getHomeMetaData($locale, $customData);
            case 'country':
                return $this->getCountryMetaData($country, $locale, $customData);
            case 'buy':
                return $this->getBuyMetaData($country, $locale, $customData);
            case 'apply':
                return $this->getApplyMetaData($country, $locale, $customData);
            case 'contact':
                return $this->getContactMetaData($locale, $customData);
            case 'requirements':
                return $this->getRequirementsMetaData($country, $locale, $customData);
            case 'process':
                return $this->getProcessMetaData($country, $locale, $customData);
            case 'faq':
                return $this->getFaqMetaData($country, $locale, $customData);
            default:
                return $this->getDefaultMetaData($locale, $customData);
        }
    }

    /**
     * Generate homepage meta data
     */
    private function getHomeMetaData($locale, $customData = [])
    {
        $titles = [
            'en' => 'Buy EU Driving License Online - Fast, Legal & Authentic',
            'de' => 'EU Führerschein Online Kaufen - Schnell, Legal & Authentisch',
            'fr' => 'Acheter Permis de Conduire UE en Ligne - Rapide, Légal & Authentique',
            'es' => 'Comprar Carnet de Conducir UE Online - Rápido, Legal y Auténtico',
            'it' => 'Comprare Patente UE Online - Veloce, Legale e Autentica',
            'nl' => 'EU Rijbewijs Online Kopen - Snel, Legaal & Authentiek',
        ];

        $descriptions = [
            'en' => 'Get your authentic EU driving license online. Fast processing, legal service, valid across all EU countries. Trusted by thousands of customers.',
            'de' => 'Erhalten Sie Ihren authentischen EU-Führerschein online. Schnelle Bearbeitung, legaler Service, gültig in allen EU-Ländern. Von Tausenden von Kunden vertraut.',
            'fr' => 'Obtenez votre permis de conduire UE authentique en ligne. Traitement rapide, service légal, valide dans tous les pays de l\'UE. Approuvé par des milliers de clients.',
            'es' => 'Obtenga su carnet de conducir UE auténtico en línea. Procesamiento rápido, servicio legal, válido en todos los países de la UE. Confiado por miles de clientes.',
            'it' => 'Ottieni la tua patente UE autentica online. Elaborazione veloce, servizio legale, valida in tutti i paesi UE. Fidata da migliaia di clienti.',
            'nl' => 'Krijg uw authentieke EU-rijbewijs online. Snelle verwerking, legale service, geldig in alle EU-landen. Vertrouwd door duizenden klanten.',
        ];

        return [
            'title' => $customData['title'] ?? $titles[$locale] ?? $titles['en'],
            'description' => $customData['description'] ?? $descriptions[$locale] ?? $descriptions['en'],
            'keywords' => $this->getHomeKeywords($locale),
            'canonical' => $this->getCanonicalUrl('/', $locale),
        ];
    }

    /**
     * Generate country-specific meta data
     */
    private function getCountryMetaData($country, $locale, $customData = [])
    {
        $countryNames = [
            'germany' => ['en' => 'German', 'de' => 'Deutschen'],
            'spain' => ['en' => 'Spanish', 'es' => 'Español'],
            'italy' => ['en' => 'Italian', 'it' => 'Italiana'],
            'netherlands' => ['en' => 'Dutch', 'nl' => 'Nederlands'],
            'ireland' => ['en' => 'Irish'],
            'uk' => ['en' => 'UK'],
        ];

        $countryName = $countryNames[$country][$locale] ?? $countryNames[$country]['en'] ?? ucfirst($country);

        $titleTemplates = [
            'en' => "Buy {$countryName} Driving License Online - Fast & Legal Service",
            'de' => "{$countryName} Führerschein Online Kaufen - Schnell & Legal",
            'fr' => "Acheter Permis de Conduire {$countryName} en Ligne - Service Rapide",
            'es' => "Comprar Carnet de Conducir {$countryName} Online - Servicio Rápido",
            'it' => "Comprare Patente {$countryName} Online - Servizio Veloce",
            'nl' => "{$countryName} Rijbewijs Online Kopen - Snelle Service",
        ];

        $descriptionTemplates = [
            'en' => "Get your authentic {$countryName} driving license online. Fast processing, legal compliance, EU-wide validity. Professional service with guaranteed results.",
            'de' => "Erhalten Sie Ihren authentischen {$countryName} Führerschein online. Schnelle Bearbeitung, rechtliche Konformität, EU-weite Gültigkeit.",
            'fr' => "Obtenez votre permis de conduire {$countryName} authentique en ligne. Traitement rapide, conformité légale, validité dans toute l'UE.",
            'es' => "Obtenga su carnet de conducir {$countryName} auténtico en línea. Procesamiento rápido, cumplimiento legal, validez en toda la UE.",
            'it' => "Ottieni la tua patente {$countryName} autentica online. Elaborazione veloce, conformità legale, validità in tutta l'UE.",
            'nl' => "Krijg uw authentieke {$countryName} rijbewijs online. Snelle verwerking, juridische naleving, EU-brede geldigheid.",
        ];

        return [
            'title' => $customData['title'] ?? $titleTemplates[$locale] ?? $titleTemplates['en'],
            'description' => $customData['description'] ?? $descriptionTemplates[$locale] ?? $descriptionTemplates['en'],
            'keywords' => $this->getCountryKeywords($country, $locale),
            'canonical' => $this->getCanonicalUrl("/driving-license/{$country}", $locale),
        ];
    }

    /**
     * Generate buy page meta data
     */
    private function getBuyMetaData($country, $locale, $customData = [])
    {
        $countryNames = [
            'germany' => ['en' => 'German', 'de' => 'Deutschen'],
            'spain' => ['en' => 'Spanish', 'es' => 'Español'],
            'italy' => ['en' => 'Italian', 'it' => 'Italiana'],
            'netherlands' => ['en' => 'Dutch', 'nl' => 'Nederlands'],
            'ireland' => ['en' => 'Irish'],
            'uk' => ['en' => 'UK'],
        ];

        $countryName = $countryNames[$country][$locale] ?? $countryNames[$country]['en'] ?? ucfirst($country);

        $titleTemplates = [
            'en' => "Buy {$countryName} Driving License Online - Order Now",
            'de' => "{$countryName} Führerschein Kaufen - Jetzt Bestellen",
            'fr' => "Acheter Permis {$countryName} - Commander Maintenant",
            'es' => "Comprar Carnet {$countryName} - Ordenar Ahora",
            'it' => "Comprare Patente {$countryName} - Ordina Ora",
            'nl' => "{$countryName} Rijbewijs Kopen - Nu Bestellen",
        ];

        return [
            'title' => $customData['title'] ?? $titleTemplates[$locale] ?? $titleTemplates['en'],
            'description' => $customData['description'] ?? "Order your {$countryName} driving license online with secure payment and fast delivery.",
            'keywords' => $this->getBuyKeywords($country, $locale),
            'canonical' => $this->getCanonicalUrl("/buy-{$country}-driving-licence-online", $locale),
        ];
    }

    /**
     * Generate contact page meta data
     */
    private function getContactMetaData($locale, $customData = [])
    {
        $titles = [
            'en' => 'Contact Us - EU Driving License Support & Customer Service',
            'de' => 'Kontakt - EU Führerschein Support & Kundenservice',
            'fr' => 'Contactez-Nous - Support Permis UE & Service Client',
            'es' => 'Contáctanos - Soporte Carnet UE & Servicio al Cliente',
            'it' => 'Contattaci - Supporto Patente UE & Servizio Clienti',
            'nl' => 'Contact - EU Rijbewijs Support & Klantenservice',
            'ga' => 'Déan Teagmháil Linn - Tacaíocht Ceadúnas AE & Seirbhís Custaiméara',
        ];

        $descriptions = [
            'en' => 'Contact our expert team for EU driving license assistance. 24/7 support, live chat, email, and phone support available. Get help with your application today.',
            'de' => 'Kontaktieren Sie unser Expertenteam für EU-Führerschein-Unterstützung. 24/7 Support, Live-Chat, E-Mail und Telefon-Support verfügbar.',
            'fr' => 'Contactez notre équipe d\'experts pour l\'assistance au permis de conduire UE. Support 24/7, chat en direct, email et support téléphonique disponibles.',
            'es' => 'Contacte a nuestro equipo de expertos para asistencia con carnet de conducir UE. Soporte 24/7, chat en vivo, email y soporte telefónico disponibles.',
            'it' => 'Contatta il nostro team di esperti per assistenza patente UE. Supporto 24/7, chat dal vivo, email e supporto telefonico disponibili.',
            'nl' => 'Neem contact op met ons expertteam voor EU rijbewijs ondersteuning. 24/7 support, live chat, email en telefonische ondersteuning beschikbaar.',
            'ga' => 'Déan teagmháil lenár bhfoireann saineolach le haghaidh cúnamh ceadúnas tiomána AE. Tacaíocht 24/7, comhrá beo, ríomhphost agus tacaíocht teileafóin ar fáil.',
        ];

        return [
            'title' => $customData['title'] ?? $titles[$locale] ?? $titles['en'],
            'description' => $customData['description'] ?? $descriptions[$locale] ?? $descriptions['en'],
            'keywords' => $this->getContactKeywords($locale),
            'canonical' => $this->getCanonicalUrl('/contact', $locale),
        ];
    }

    /**
     * Generate apply page meta data
     */
    private function getApplyMetaData($country, $locale, $customData = [])
    {
        $countryNames = [
            'germany' => ['en' => 'German', 'de' => 'Deutschen', 'fr' => 'Allemand', 'es' => 'Alemán', 'it' => 'Tedesco', 'nl' => 'Duits'],
            'spain' => ['en' => 'Spanish', 'es' => 'Español', 'fr' => 'Espagnol', 'de' => 'Spanisch', 'it' => 'Spagnolo', 'nl' => 'Spaans'],
            'italy' => ['en' => 'Italian', 'it' => 'Italiana', 'fr' => 'Italien', 'de' => 'Italienisch', 'es' => 'Italiano', 'nl' => 'Italiaans'],
            'netherlands' => ['en' => 'Dutch', 'nl' => 'Nederlands', 'fr' => 'Néerlandais', 'de' => 'Niederländisch', 'es' => 'Holandés', 'it' => 'Olandese'],
            'ireland' => ['en' => 'Irish', 'fr' => 'Irlandais', 'de' => 'Irisch', 'es' => 'Irlandés', 'it' => 'Irlandese', 'nl' => 'Iers'],
            'uk' => ['en' => 'UK', 'fr' => 'Royaume-Uni', 'de' => 'Großbritannien', 'es' => 'Reino Unido', 'it' => 'Regno Unito', 'nl' => 'VK'],
        ];

        $countryName = $countryNames[$country][$locale] ?? $countryNames[$country]['en'] ?? ucfirst($country);

        $titleTemplates = [
            'en' => "Apply for {$countryName} Driving License Online - Fast & Legal Process",
            'de' => "{$countryName} Führerschein Beantragen - Schnell & Legal",
            'fr' => "Demander Permis de Conduire {$countryName} - Processus Rapide",
            'es' => "Solicitar Carnet de Conducir {$countryName} - Proceso Rápido",
            'it' => "Richiedere Patente {$countryName} - Processo Veloce",
            'nl' => "{$countryName} Rijbewijs Aanvragen - Snel Proces",
        ];

        $descriptionTemplates = [
            'en' => "Apply for your authentic {$countryName} driving license online. Fast, secure application process with guaranteed approval. Start your application today.",
            'de' => "Beantragen Sie Ihren authentischen {$countryName} Führerschein online. Schneller, sicherer Antragsprozess mit garantierter Genehmigung.",
            'fr' => "Demandez votre permis de conduire {$countryName} authentique en ligne. Processus de demande rapide et sécurisé avec approbation garantie.",
            'es' => "Solicite su carnet de conducir {$countryName} auténtico en línea. Proceso de solicitud rápido y seguro con aprobación garantizada.",
            'it' => "Richiedi la tua patente {$countryName} autentica online. Processo di richiesta veloce e sicuro con approvazione garantita.",
            'nl' => "Vraag uw authentieke {$countryName} rijbewijs online aan. Snel, veilig aanvraagproces met gegarandeerde goedkeuring.",
        ];

        return [
            'title' => $customData['title'] ?? $titleTemplates[$locale] ?? $titleTemplates['en'],
            'description' => $customData['description'] ?? $descriptionTemplates[$locale] ?? $descriptionTemplates['en'],
            'keywords' => $this->getApplyKeywords($country, $locale),
            'canonical' => $this->getCanonicalUrl("/apply", $locale),
        ];
    }

    /**
     * Generate hreflang tags
     */
    private function generateHreflangTags($page, $country = null)
    {
        $hreflangTags = [];
        $supportedLocales = LaravelLocalization::getSupportedLocales();

        foreach ($supportedLocales as $localeCode => $properties) {
            $url = $this->getLocalizedUrl($page, $country, $localeCode);
            $hreflangTags[] = [
                'hreflang' => $localeCode,
                'href' => $url,
            ];
        }

        // Add x-default
        $defaultUrl = $this->getLocalizedUrl($page, $country, 'en');
        $hreflangTags[] = [
            'hreflang' => 'x-default',
            'href' => $defaultUrl,
        ];

        return $hreflangTags;
    }

    /**
     * Generate Open Graph tags
     */
    private function generateOpenGraphTags($metaData)
    {
        return [
            'og:title' => $metaData['title'],
            'og:description' => $metaData['description'],
            'og:type' => 'website',
            'og:url' => $metaData['canonical'],
            'og:site_name' => 'EU Driving License Services',
            'og:locale' => App::getLocale(),
        ];
    }

    /**
     * Generate Twitter Card tags
     */
    private function generateTwitterTags($metaData)
    {
        return [
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $metaData['title'],
            'twitter:description' => $metaData['description'],
            'twitter:site' => '@EUDrivingLicense',
        ];
    }

    /**
     * Generate Schema.org markup
     */
    private function generateSchemaMarkup($page, $country, $metaData)
    {
        $baseSchema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'EU Driving License Services',
            'url' => config('app.url'),
            'description' => $metaData['description'],
        ];

        if ($page === 'buy' && $country) {
            return array_merge($baseSchema, [
                '@type' => 'Service',
                'serviceType' => 'Driving License Service',
                'areaServed' => [
                    '@type' => 'Country',
                    'name' => ucfirst($country),
                ],
                'offers' => [
                    '@type' => 'Offer',
                    'description' => $metaData['title'],
                    'priceCurrency' => 'EUR',
                ],
            ]);
        }

        return $baseSchema;
    }

    /**
     * Get keywords for homepage
     */
    private function getHomeKeywords($locale)
    {
        $keywords = [
            'en' => 'EU driving license, buy driving license online, authentic driving license, legal driving license, EU license',
            'de' => 'EU Führerschein, Führerschein online kaufen, authentischer Führerschein, legaler Führerschein, EU Lizenz',
            'fr' => 'permis de conduire UE, acheter permis en ligne, permis authentique, permis légal, licence UE',
            'es' => 'carnet de conducir UE, comprar carnet online, carnet auténtico, carnet legal, licencia UE',
            'it' => 'patente UE, comprare patente online, patente autentica, patente legale, licenza UE',
            'nl' => 'EU rijbewijs, rijbewijs online kopen, authentiek rijbewijs, legaal rijbewijs, EU licentie',
        ];

        return $keywords[$locale] ?? $keywords['en'];
    }

    /**
     * Get keywords for contact page
     */
    private function getContactKeywords($locale)
    {
        $keywords = [
            'en' => 'contact eu driving license, driving license support, customer service, help desk, contact support, driving license assistance, eu license help, customer care, support team, contact us',
            'de' => 'kontakt eu führerschein, führerschein support, kundenservice, hilfe, kontakt support, führerschein hilfe, eu lizenz hilfe, kundendienst, support team, kontakt',
            'fr' => 'contact permis ue, support permis, service client, aide, contact support, assistance permis, aide licence ue, service clientèle, équipe support, contactez-nous',
            'es' => 'contacto carnet ue, soporte carnet, servicio cliente, ayuda, contacto soporte, asistencia carnet, ayuda licencia ue, atención cliente, equipo soporte, contáctanos',
            'it' => 'contatto patente ue, supporto patente, servizio clienti, aiuto, contatto supporto, assistenza patente, aiuto licenza ue, assistenza clienti, team supporto, contattaci',
            'nl' => 'contact eu rijbewijs, rijbewijs support, klantenservice, hulp, contact support, rijbewijs hulp, eu licentie hulp, klantenzorg, support team, contact',
            'ga' => 'teagmháil ceadúnas ae, tacaíocht ceadúnas, seirbhís custaiméara, cabhair, teagmháil tacaíocht, cúnamh ceadúnas, cabhair ceadúnas ae, cúram custaiméara, foireann tacaíochta, déan teagmháil',
        ];

        return $keywords[$locale] ?? $keywords['en'];
    }

    /**
     * Get keywords for country pages
     */
    private function getCountryKeywords($country, $locale)
    {
        $countryKeywords = [
            'germany' => [
                'en' => 'German driving license, buy German license, German driving permit, Deutschland license',
                'de' => 'deutscher Führerschein, deutschen Führerschein kaufen, deutsche Fahrerlaubnis, Deutschland Lizenz',
            ],
            'spain' => [
                'en' => 'Spanish driving license, buy Spanish license, Spanish driving permit, España license',
                'es' => 'carnet de conducir español, comprar carnet español, permiso de conducir español, licencia España',
            ],
            // Add more countries as needed
        ];

        return $countryKeywords[$country][$locale] ?? "driving license {$country}, buy {$country} license";
    }

    /**
     * Get keywords for buy pages
     */
    private function getBuyKeywords($country, $locale)
    {
        return "buy {$country} driving license, order {$country} license, purchase {$country} permit";
    }

    /**
     * Get keywords for apply pages
     */
    private function getApplyKeywords($country, $locale)
    {
        $baseKeywords = [
            'en' => [
                'apply for driving license online',
                'apply for drivers license',
                'how to apply for driving license',
                'driving license application',
                'apply for license online',
                'get driving license online',
                'driving license application form',
                'apply for driving permit',
            ],
            'de' => [
                'führerschein beantragen online',
                'führerschein antrag',
                'wie führerschein beantragen',
                'führerschein anmeldung',
                'führerschein online beantragen',
            ],
            'fr' => [
                'demander permis de conduire en ligne',
                'demande permis de conduire',
                'comment demander permis',
                'formulaire permis de conduire',
            ],
            'es' => [
                'solicitar carnet de conducir online',
                'solicitud carnet de conducir',
                'como solicitar carnet',
                'formulario carnet de conducir',
            ],
            'it' => [
                'richiedere patente online',
                'richiesta patente',
                'come richiedere patente',
                'modulo patente',
            ],
            'nl' => [
                'rijbewijs aanvragen online',
                'rijbewijs aanvraag',
                'hoe rijbewijs aanvragen',
                'rijbewijs formulier',
            ],
        ];

        $countrySpecificKeywords = [
            'ireland' => [
                'en' => [
                    'apply for irish driving license',
                    'irish driving license application',
                    'apply for ndls license',
                    'irish ndls application',
                    'apply for irish driving licence online',
                    'how to apply for irish license',
                    'irish driving permit application',
                ],
            ],
            'germany' => [
                'en' => ['apply for german driving license', 'german license application'],
                'de' => ['deutschen führerschein beantragen', 'deutscher führerschein antrag'],
            ],
            'spain' => [
                'en' => ['apply for spanish driving license', 'spanish license application'],
                'es' => ['solicitar carnet español', 'carnet de conducir español solicitud'],
            ],
        ];

        $keywords = $baseKeywords[$locale] ?? $baseKeywords['en'];

        if (isset($countrySpecificKeywords[$country][$locale])) {
            $keywords = array_merge($keywords, $countrySpecificKeywords[$country][$locale]);
        } elseif (isset($countrySpecificKeywords[$country]['en'])) {
            $keywords = array_merge($keywords, $countrySpecificKeywords[$country]['en']);
        }

        return implode(', ', $keywords);
    }

    /**
     * Get canonical URL
     */
    private function getCanonicalUrl($path, $locale)
    {
        $baseUrl = config('app.url');

        if ($locale === 'en') {
            return $baseUrl . $path;
        }

        return $baseUrl . '/' . $locale . $path;
    }

    /**
     * Get localized URL
     */
    private function getLocalizedUrl($page, $country, $locale)
    {
        $baseUrl = config('app.url');

        switch ($page) {
            case 'home':
                $path = '/';
                break;
            case 'country':
                $path = "/driving-license/{$country}";
                break;
            case 'buy':
                $path = "/buy-{$country}-driving-licence-online";
                break;
            default:
                $path = '/';
        }

        if ($locale === 'en') {
            return $baseUrl . $path;
        }

        return $baseUrl . '/' . $locale . $path;
    }

    /**
     * Generate requirements meta data
     */
    private function getRequirementsMetaData($country, $locale, $customData = [])
    {
        $countryName = ucfirst($country);

        return [
            'title' => $customData['title'] ?? "{$countryName} Driving License Requirements - Complete Guide",
            'description' => $customData['description'] ?? "Complete guide to {$countryName} driving license requirements, eligibility criteria, and application process.",
            'keywords' => "{$country} driving license requirements, {$country} license eligibility, {$country} permit requirements",
            'canonical' => $this->getCanonicalUrl("/{$country}-driving-license-requirements", $locale),
        ];
    }

    /**
     * Generate process meta data
     */
    private function getProcessMetaData($country, $locale, $customData = [])
    {
        $countryName = ucfirst($country);

        return [
            'title' => $customData['title'] ?? "{$countryName} Driving License Process - Step by Step Guide",
            'description' => $customData['description'] ?? "Step-by-step guide to getting your {$countryName} driving license. Complete process explained.",
            'keywords' => "{$country} driving license process, {$country} license steps, {$country} permit process",
            'canonical' => $this->getCanonicalUrl("/{$country}-driving-license-process", $locale),
        ];
    }

    /**
     * Generate FAQ meta data
     */
    private function getFaqMetaData($country, $locale, $customData = [])
    {
        $countryName = ucfirst($country);

        return [
            'title' => $customData['title'] ?? "{$countryName} Driving License FAQ - Frequently Asked Questions",
            'description' => $customData['description'] ?? "Frequently asked questions about {$countryName} driving license. Get answers to common questions.",
            'keywords' => "{$country} driving license FAQ, {$country} license questions, {$country} permit FAQ",
            'canonical' => $this->getCanonicalUrl("/{$country}-driving-license-faq", $locale),
        ];
    }

    /**
     * Generate default meta data
     */
    private function getDefaultMetaData($locale, $customData = [])
    {
        return [
            'title' => $customData['title'] ?? 'EU Driving License Services',
            'description' => $customData['description'] ?? 'Professional EU driving license services.',
            'keywords' => 'EU driving license, driving license services',
            'canonical' => config('app.url'),
        ];
    }
}
