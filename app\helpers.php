<?php

if (!function_exists('localized_route')) {
    /**
     * Generate a localized route URL
     */
    function localized_route($name, $parameters = [], $locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $routeName = $name . '.' . $locale;

        try {
            return route($routeName, $parameters);
        } catch (\Exception $e) {
            // Fallback to English if route doesn't exist
            try {
                return route($name . '.en', $parameters);
            } catch (\Exception $e) {
                // Last fallback - try the original route name
                try {
                    return route($name, $parameters);
                } catch (\Exception $e) {
                    // If all else fails, return a basic URL
                    return '/' . $locale;
                }
            }
        }
    }
}

if (!function_exists('language_switcher_urls')) {
    /**
     * Generate language switcher URLs
     */
    function language_switcher_urls($currentRoute = null, $parameters = [])
    {
        $supportedLocales = [
            'en' => ['name' => 'English', 'native' => 'English'],
            'de' => ['name' => 'German', 'native' => 'Deutsch'],
            'fr' => ['name' => 'French', 'native' => 'Français'],
            'es' => ['name' => 'Spanish', 'native' => 'Español'],
            'it' => ['name' => 'Italian', 'native' => 'Italiano'],
            'nl' => ['name' => 'Dutch', 'native' => 'Nederlands'],
        ];

        $urls = [];
        $currentRoute = $currentRoute ?: (request()->route() ? request()->route()->getName() : 'home');

        // Remove locale suffix from current route
        $baseRoute = preg_replace('/\.(en|de|fr|es|it|nl|ie|ga)$/', '', $currentRoute);

        foreach ($supportedLocales as $locale => $data) {
            try {
                $urls[$locale] = [
                    'url' => localized_route($baseRoute, $parameters, $locale),
                    'name' => $data['name'],
                    'native' => $data['native'],
                    'active' => app()->getLocale() === $locale
                ];
            } catch (\Exception $e) {
                // Fallback to homepage for that locale
                $urls[$locale] = [
                    'url' => '/' . $locale,
                    'name' => $data['name'],
                    'native' => $data['native'],
                    'active' => app()->getLocale() === $locale
                ];
            }
        }

        return $urls;
    }
}

if (!function_exists('hreflang_urls')) {
    /**
     * Generate hreflang attributes for SEO
     */
    function hreflang_urls($currentRoute = null, $parameters = [])
    {
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl', 'ie', 'ga'];
        $urls = [];
        $currentRoute = $currentRoute ?: (request()->route() ? request()->route()->getName() : 'home');

        // Remove locale suffix from current route
        $baseRoute = preg_replace('/\.(en|de|fr|es|it|nl|ie|ga)$/', '', $currentRoute);

        foreach ($supportedLocales as $locale) {
            try {
                $urls[$locale] = localized_route($baseRoute, $parameters, $locale);
            } catch (\Exception $e) {
                $urls[$locale] = '/' . $locale;
            }
        }

        return $urls;
    }
}

if (!function_exists('supported_locales')) {
    /**
     * Get all supported locales
     */
    function supported_locales()
    {
        return [
            'en' => ['name' => 'English', 'native' => 'English'],
            'de' => ['name' => 'German', 'native' => 'Deutsch'],
            'fr' => ['name' => 'French', 'native' => 'Français'],
            'es' => ['name' => 'Spanish', 'native' => 'Español'],
            'it' => ['name' => 'Italian', 'native' => 'Italiano'],
            'nl' => ['name' => 'Dutch', 'native' => 'Nederlands'],
            'ie' => ['name' => 'Irish (English)', 'native' => 'Irish English'],
            'ga' => ['name' => 'Irish', 'native' => 'Gaeilge'],
        ];
    }
}
