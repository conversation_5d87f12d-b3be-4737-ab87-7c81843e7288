<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Country;
use Illuminate\Support\Facades\DB;

echo "🔍 Checking Countries Database\n";
echo str_repeat("=", 40) . "\n";

$countryCount = Country::count();
$translationCount = DB::table('country_translations')->count();

echo "Countries: {$countryCount}\n";
echo "Translations: {$translationCount}\n\n";

if ($countryCount > 0) {
    echo "📋 Countries List:\n";
    $countries = Country::with('translations')->get();
    
    foreach ($countries as $country) {
        echo "- ID: {$country->id}, Code: {$country->code}\n";
        
        foreach ($country->translations as $translation) {
            echo "  └─ {$translation->locale}: {$translation->name} (slug: {$translation->slug})\n";
        }
        echo "\n";
    }
} else {
    echo "❌ No countries found in database!\n";
    echo "💡 You need to run the seeder or create countries manually.\n";
}
