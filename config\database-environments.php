<?php

/**
 * Database Environment Configuration Guide
 * 
 * This file contains configuration examples for different environments
 * Copy the appropriate configuration to your .env file
 */

return [
    
    /*
    |--------------------------------------------------------------------------
    | Development Environment (MySQL)
    |--------------------------------------------------------------------------
    */
    'development' => [
        'DB_CONNECTION' => 'mysql',
        'DB_HOST' => '127.0.0.1',
        'DB_PORT' => '3306',
        'DB_DATABASE' => 'eu_license',
        'DB_USERNAME' => 'root',
        'DB_PASSWORD' => '',
    ],

    /*
    |--------------------------------------------------------------------------
    | Production Environment (MySQL)
    |--------------------------------------------------------------------------
    */
    'production' => [
        'DB_CONNECTION' => 'mysql',
        'DB_HOST' => 'your-production-host',
        'DB_PORT' => '3306',
        'DB_DATABASE' => 'eu_license_prod',
        'DB_USERNAME' => 'your-prod-user',
        'DB_PASSWORD' => 'your-secure-password',
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing Environment (SQLite)
    |--------------------------------------------------------------------------
    */
    'testing' => [
        'DB_CONNECTION' => 'sqlite',
        'DB_DATABASE' => ':memory:', // or database/testing.sqlite
    ],

    /*
    |--------------------------------------------------------------------------
    | Local Development with Docker
    |--------------------------------------------------------------------------
    */
    'docker' => [
        'DB_CONNECTION' => 'mysql',
        'DB_HOST' => 'mysql', // Docker service name
        'DB_PORT' => '3306',
        'DB_DATABASE' => 'eu_license',
        'DB_USERNAME' => 'sail',
        'DB_PASSWORD' => 'password',
    ],

];
