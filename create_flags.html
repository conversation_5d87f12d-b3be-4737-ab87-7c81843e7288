<!DOCTYPE html>
<html>
<head>
    <title>Flag Generator</title>
</head>
<body>
    <canvas id="canvas" width="120" height="80"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Create flag placeholders
        const flags = [
            {code: 'de', colors: ['#000000', '#DD0000', '#FFCE00']}, // Germany
            {code: 'es', colors: ['#AA151B', '#F1BF00', '#AA151B']}, // Spain  
            {code: 'it', colors: ['#009246', '#FFFFFF', '#CE2B37']}, // Italy
            {code: 'nl', colors: ['#21468B', '#FFFFFF', '#AE1C28']}, // Netherlands
            {code: 'ie', colors: ['#169B62', '#FFFFFF', '#FF883E']}, // Ireland
            {code: 'gb', colors: ['#012169', '#FFFFFF', '#C8102E']}  // UK
        ];
        
        flags.forEach(flag => {
            ctx.clearRect(0, 0, 120, 80);
            
            // Draw horizontal stripes
            flag.colors.forEach((color, index) => {
                ctx.fillStyle = color;
                ctx.fillRect(0, index * (80/3), 120, 80/3);
            });
            
            // Download as WebP (simulated)
            console.log(`Created ${flag.code}.webp`);
        });
    </script>
</body>
</html>
