<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

class CountryFactory extends Factory
{
    protected $model = Country::class;

    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->randomElement(['DE', 'FR', 'ES', 'IT', 'NL', 'IE']),
            'flag_url' => null,
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 10),
            'base_price' => $this->faker->numberBetween(200, 800),
            'processing_days_min' => $this->faker->numberBetween(5, 7),
            'processing_days_max' => $this->faker->numberBetween(10, 14),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (Country $country) {
            // Create English translation
            $country->translateOrNew('en')->name = $this->getCountryName($country->code);
            $country->translateOrNew('en')->slug = strtolower($this->getCountryName($country->code));
            $country->translateOrNew('en')->meta_title = "Get {$this->getCountryName($country->code)} Driving License Online";
            $country->translateOrNew('en')->meta_description = "Get your {$this->getCountryName($country->code)} driving license quickly and legally.";
            $country->translateOrNew('en')->meta_keywords = "buy {$this->getCountryName($country->code)} driving license online";
            $country->translateOrNew('en')->content = $this->faker->paragraphs(3, true);
            $country->translateOrNew('en')->excerpt = $this->faker->paragraph();
            $country->translateOrNew('en')->requirements = [
                'Valid passport or ID',
                'Proof of residency',
                'Medical certificate',
                'Passport photos'
            ];
            $country->translateOrNew('en')->process_steps = [
                'Step 1: Submit application',
                'Step 2: Document verification',
                'Step 3: Processing',
                'Step 4: Delivery'
            ];
            $country->translateOrNew('en')->faq = [
                [
                    'question' => 'How long does it take?',
                    'answer' => 'Processing typically takes 5-10 business days.'
                ],
                [
                    'question' => 'What documents do I need?',
                    'answer' => 'You need a valid ID and proof of residency.'
                ]
            ];
            $country->save();
        });
    }

    private function getCountryName($code)
    {
        $countries = [
            'DE' => 'Germany',
            'FR' => 'France',
            'ES' => 'Spain',
            'IT' => 'Italy',
            'NL' => 'Netherlands',
            'IE' => 'Ireland',
        ];

        return $countries[$code] ?? 'Unknown';
    }

    public function inactive(): static
    {
        return $this->state(fn () => [
            'is_active' => false,
        ]);
    }

    public function germany(): static
    {
        return $this->state(fn () => [
            'code' => 'DE',
        ]);
    }

    public function france(): static
    {
        return $this->state(fn () => [
            'code' => 'FR',
        ]);
    }

    public function spain(): static
    {
        return $this->state(fn () => [
            'code' => 'ES',
        ]);
    }
}
