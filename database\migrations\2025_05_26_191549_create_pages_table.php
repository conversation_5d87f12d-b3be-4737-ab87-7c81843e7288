<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('page_type', 50); // 'country', 'buy', 'requirements', 'process', 'faq', 'general'
            $table->foreignId('country_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('template', 50)->default('default'); // Template to use for rendering
            $table->boolean('is_published')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->integer('sort_order')->default(0);
            $table->json('schema_markup')->nullable(); // JSON-LD schema markup
            $table->timestamps();

            $table->index(['page_type', 'is_published']);
            $table->index(['country_id', 'page_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
