<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applications', function (Blueprint $table) {
            $table->id();
            $table->string('application_number')->unique();
            $table->foreignId('country_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            
            // Personal Information
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email');
            $table->string('phone');
            $table->date('date_of_birth');
            $table->string('place_of_birth');
            $table->string('nationality');
            
            // Address Information
            $table->string('address_line_1');
            $table->string('address_line_2')->nullable();
            $table->string('city');
            $table->string('postal_code');
            $table->string('country');
            
            // License Information
            $table->string('license_category')->default('B');
            $table->string('previous_license_number')->nullable();
            $table->string('previous_license_country')->nullable();
            $table->json('medical_conditions')->nullable();
            
            // Emergency Contact
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone');
            
            // Delivery Information
            $table->string('delivery_address_line_1');
            $table->string('delivery_address_line_2')->nullable();
            $table->string('delivery_city');
            $table->string('delivery_postal_code');
            $table->string('delivery_country');
            
            // Package and Processing
            $table->string('package_type')->default('standard');
            $table->string('processing_speed')->default('standard');
            $table->decimal('total_amount', 10, 2);
            
            // Payment Information
            $table->string('payment_status')->default('pending');
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            
            // Application Status
            $table->string('application_status')->default('draft');
            $table->json('documents_uploaded')->nullable();
            $table->string('verification_status')->default('pending');
            $table->text('notes')->nullable();
            
            // Timestamps
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->string('tracking_number')->nullable()->unique();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['application_status', 'created_at']);
            $table->index(['country_id', 'application_status']);
            $table->index(['email', 'application_status']);
            $table->index('tracking_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applications');
    }
};
