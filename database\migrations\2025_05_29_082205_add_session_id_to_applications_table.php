<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->string('session_id')->nullable()->after('user_id');
            $table->text('special_requirements')->nullable()->after('processing_speed');

            // Make user_id nullable for anonymous applications
            $table->unsignedBigInteger('user_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->dropColumn(['session_id', 'special_requirements']);

            // Revert user_id to not nullable (this might fail if there are null values)
            // $table->unsignedBigInteger('user_id')->nullable(false)->change();
        });
    }
};
