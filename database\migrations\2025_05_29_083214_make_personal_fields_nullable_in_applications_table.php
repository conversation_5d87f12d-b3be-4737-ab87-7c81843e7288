<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Make personal information fields nullable for draft applications
            $table->string('first_name')->nullable()->change();
            $table->string('last_name')->nullable()->change();
            $table->string('email')->nullable()->change();
            $table->string('phone')->nullable()->change();
            $table->date('date_of_birth')->nullable()->change();
            $table->string('place_of_birth')->nullable()->change();
            $table->string('nationality')->nullable()->change();
            $table->string('license_category')->nullable()->change();
            $table->string('package_type')->nullable()->change();
            $table->string('processing_speed')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Revert fields to not nullable (this might fail if there are null values)
            // $table->string('first_name')->nullable(false)->change();
            // $table->string('last_name')->nullable(false)->change();
            // $table->string('email')->nullable(false)->change();
            // $table->string('phone')->nullable(false)->change();
            // $table->date('date_of_birth')->nullable(false)->change();
            // $table->string('place_of_birth')->nullable(false)->change();
            // $table->string('nationality')->nullable(false)->change();
            // $table->string('license_category')->nullable(false)->change();
            // $table->string('package_type')->nullable(false)->change();
            // $table->string('processing_speed')->nullable(false)->change();
        });
    }
};
