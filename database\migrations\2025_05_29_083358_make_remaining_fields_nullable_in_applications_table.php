<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Make address and contact fields nullable for draft applications
            $table->string('address_line_1')->nullable()->change();
            $table->string('address_line_2')->nullable()->change();
            $table->string('city')->nullable()->change();
            $table->string('postal_code')->nullable()->change();
            $table->string('country')->nullable()->change();

            // Make license-related fields nullable
            $table->string('previous_license_number')->nullable()->change();
            $table->string('previous_license_country')->nullable()->change();
            $table->text('medical_conditions')->nullable()->change();

            // Make emergency contact fields nullable
            $table->string('emergency_contact_name')->nullable()->change();
            $table->string('emergency_contact_phone')->nullable()->change();

            // Make delivery address fields nullable
            $table->string('delivery_address_line_1')->nullable()->change();
            $table->string('delivery_address_line_2')->nullable()->change();
            $table->string('delivery_city')->nullable()->change();
            $table->string('delivery_postal_code')->nullable()->change();
            $table->string('delivery_country')->nullable()->change();

            // Make payment and processing fields nullable
            $table->decimal('total_amount', 10, 2)->nullable()->change();
            $table->string('payment_status')->nullable()->change();
            $table->string('payment_method')->nullable()->change();
            $table->string('payment_reference')->nullable()->change();
            // Skip documents_uploaded for now due to existing data issues
            // $table->boolean('documents_uploaded')->nullable()->change();
            $table->string('verification_status')->nullable()->change();
            $table->text('notes')->nullable()->change();
            $table->timestamp('submitted_at')->nullable()->change();
            $table->timestamp('processed_at')->nullable()->change();
            $table->timestamp('completed_at')->nullable()->change();
            $table->string('tracking_number')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Revert fields to not nullable (this might fail if there are null values)
            // Commented out to prevent issues during rollback
        });
    }
};
