<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if it doesn't exist
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create a test admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Admin users created successfully!');
        $this->command->info('Admin credentials:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: admin123');
        $this->command->info('');
        $this->command->info('Test Admin credentials:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password');
    }
}
