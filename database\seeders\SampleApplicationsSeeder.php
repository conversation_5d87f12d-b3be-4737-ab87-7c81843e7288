<?php

namespace Database\Seeders;

use App\Models\Application;
use App\Models\Country;
use App\Models\User;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;

class SampleApplicationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();
        $countries = Country::all();

        if ($countries->isEmpty()) {
            $this->command->error('No countries found. Please run CountrySeeder first.');
            return;
        }

        // Create some sample users
        $users = [];
        for ($i = 0; $i < 10; $i++) {
            $users[] = User::create([
                'name' => $faker->name,
                'email' => $faker->unique()->safeEmail,
                'password' => bcrypt('password'),
                'is_admin' => false,
                'email_verified_at' => now(),
            ]);
        }

        // Application statuses
        $statuses = [
            'submitted',
            'under_review',
            'documents_required',
            'payment_pending',
            'processing',
            'completed',
            'delivered',
            'rejected'
        ];

        $paymentStatuses = ['pending', 'completed', 'failed', 'refunded'];
        $licenseCategories = ['A', 'A1', 'A2', 'B', 'B1', 'BE', 'C', 'C1', 'CE', 'C1E', 'D', 'D1', 'DE', 'D1E'];

        // Get the highest existing application number to avoid duplicates
        $lastApp = Application::orderBy('id', 'desc')->first();
        $startNumber = $lastApp ? (int)substr($lastApp->application_number, -6) + 1 : 1;

        // Create 50 sample applications
        for ($i = 0; $i < 50; $i++) {
            $country = $countries->random();
            $user = $faker->randomElement($users);
            $status = $faker->randomElement($statuses);
            $paymentStatus = $faker->randomElement($paymentStatuses);

            // Generate unique application number
            $appNumber = 'APP-' . date('Y') . '-' . str_pad($startNumber + $i, 6, '0', STR_PAD_LEFT);

            Application::create([
                'application_number' => $appNumber,
                'country_id' => $country->id,
                'user_id' => $user->id,
                'first_name' => $faker->firstName,
                'last_name' => $faker->lastName,
                'email' => $user->email,
                'phone' => $faker->phoneNumber,
                'date_of_birth' => $faker->date('Y-m-d', '-18 years'),
                'place_of_birth' => $faker->city,
                'nationality' => $faker->country,
                'address_line_1' => $faker->streetAddress,
                'address_line_2' => $faker->optional()->secondaryAddress,
                'city' => $faker->city,
                'postal_code' => $faker->postcode,
                'country' => $country->code,
                'license_category' => $faker->randomElement($licenseCategories),
                'previous_license_number' => $faker->optional()->bothify('??######'),
                'previous_license_country' => $faker->optional()->countryCode,
                'medical_conditions' => $faker->optional()->sentence,
                'emergency_contact_name' => $faker->name,
                'emergency_contact_phone' => $faker->phoneNumber,
                'delivery_address_line_1' => $faker->streetAddress,
                'delivery_address_line_2' => $faker->optional()->secondaryAddress,
                'delivery_city' => $faker->city,
                'delivery_postal_code' => $faker->postcode,
                'delivery_country' => $country->code,
                'package_type' => $faker->randomElement(['standard', 'express', 'premium']),
                'processing_speed' => $faker->randomElement(['standard', 'fast', 'express']),
                'total_amount' => $faker->randomFloat(2, 299, 899),
                'payment_status' => $paymentStatus,
                'payment_method' => $faker->randomElement(['credit_card', 'bank_transfer', 'paypal']),
                'payment_reference' => $faker->optional()->bothify('PAY-########'),
                'application_status' => $status,
                'documents_uploaded' => $faker->boolean(70),
                'verification_status' => $faker->randomElement(['pending', 'verified', 'rejected']),
                'notes' => $faker->optional()->paragraph,
                'submitted_at' => $faker->dateTimeBetween('-3 months', 'now'),
                'processed_at' => $status === 'processing' ? $faker->dateTimeBetween('-1 month', 'now') : null,
                'completed_at' => in_array($status, ['completed', 'delivered']) ? $faker->dateTimeBetween('-2 weeks', 'now') : null,
                'tracking_number' => in_array($status, ['completed', 'delivered']) ? $faker->bothify('TRK-########') : null,
                'created_at' => $faker->dateTimeBetween('-3 months', 'now'),
                'updated_at' => $faker->dateTimeBetween('-1 month', 'now'),
            ]);
        }

        $this->command->info('Sample applications created successfully!');
        $this->command->info('Created 50 sample applications with various statuses.');
    }
}
