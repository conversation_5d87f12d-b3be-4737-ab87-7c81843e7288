-- MySQL Database Setup for EU Driving License Application
-- Run this script to create the database and user

-- Create database
CREATE DATABASE IF NOT EXISTS eu_license CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional - you can use root)
-- CREATE USER IF NOT EXISTS 'eu_license_user'@'localhost' IDENTIFIED BY 'secure_password';
-- GRANT ALL PRIVILEGES ON eu_license.* TO 'eu_license_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Use the database
USE eu_license;

-- Show that database is ready
SELECT 'Database eu_license is ready for Laravel migrations!' as status;
