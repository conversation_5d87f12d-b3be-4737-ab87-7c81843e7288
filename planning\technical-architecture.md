# Technical Architecture - Lara<PERSON> 10.38 SEO-Optimized Website

## System Architecture Overview

### Application Structure
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── HomeController.php
│   │   ├── CountryController.php
│   │   ├── BlogController.php
│   │   └── SeoController.php
│   ├── Middleware/
│   │   ├── SeoMiddleware.php
│   │   └── CanonicalUrlMiddleware.php
│   └── Requests/
├── Models/
│   ├── Country.php
│   ├── Page.php
│   ├── BlogPost.php
│   └── SeoMeta.php
├── Services/
│   ├── SeoService.php
│   ├── SchemaService.php
│   └── SitemapService.php
└── Providers/
    └── SeoServiceProvider.php
```

### Database Schema

#### Countries Table
```sql
CREATE TABLE countries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(2) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    flag_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### Pages Table
```sql
CREATE TABLE pages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    canonical_url VARCHAR(255),
    schema_markup JSON,
    is_published BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### SEO Meta Table
```sql
CREATE TABLE seo_metas (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    url VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(255),
    description TEXT,
    keywords TEXT,
    og_title VARCHAR(255),
    og_description TEXT,
    og_image VARCHAR(255),
    twitter_title VARCHAR(255),
    twitter_description TEXT,
    twitter_image VARCHAR(255),
    canonical_url VARCHAR(255),
    robots VARCHAR(100) DEFAULT 'index,follow',
    schema_markup JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### Blog Posts Table
```sql
CREATE TABLE blog_posts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT,
    excerpt TEXT,
    featured_image VARCHAR(255),
    author_id BIGINT,
    category_id BIGINT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## Laravel SEO Package Configuration

### Required Packages
```json
{
    "require": {
        "spatie/laravel-sitemap": "^7.0",
        "spatie/laravel-seo": "^1.0",
        "spatie/laravel-schemaorg": "^3.0",
        "spatie/laravel-robots-txt": "^2.0",
        "intervention/image": "^2.7",
        "league/commonmark": "^2.4"
    }
}
```

### SEO Service Implementation
```php
<?php

namespace App\Services;

use Spatie\SchemaOrg\Schema;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class SeoService
{
    public function generateMetaTags($page, $country = null)
    {
        $title = $this->generateTitle($page, $country);
        $description = $this->generateDescription($page, $country);
        
        return [
            'title' => $title,
            'description' => $description,
            'canonical' => $this->generateCanonicalUrl($page, $country),
            'og_title' => $title,
            'og_description' => $description,
            'og_image' => $this->generateOgImage($page, $country),
        ];
    }
    
    public function generateSchema($type, $data)
    {
        switch ($type) {
            case 'organization':
                return Schema::organization()
                    ->name(config('app.name'))
                    ->url(config('app.url'))
                    ->logo(asset('images/logo.png'));
                    
            case 'service':
                return Schema::service()
                    ->name($data['name'])
                    ->description($data['description'])
                    ->provider(Schema::organization()->name(config('app.name')));
                    
            case 'faq':
                return Schema::fAQPage()
                    ->mainEntity($this->generateFaqSchema($data['questions']));
        }
    }
}
```

## Performance Optimization

### Caching Strategy
```php
// config/cache.php
'stores' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
    ],
],

// Cache implementation
Cache::remember('country_pages', 3600, function () {
    return Country::with('pages')->get();
});
```

### Image Optimization
```php
// Image processing service
use Intervention\Image\Facades\Image;

class ImageOptimizationService
{
    public function optimizeAndResize($image, $sizes = [])
    {
        $optimized = [];
        
        foreach ($sizes as $size) {
            $resized = Image::make($image)
                ->resize($size['width'], $size['height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                })
                ->encode('webp', 85);
                
            $optimized[$size['name']] = $resized;
        }
        
        return $optimized;
    }
}
```

### Route Optimization
```php
// routes/web.php with SEO-friendly URLs
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/driving-license/{country:slug}', [CountryController::class, 'show'])->name('country.show');
Route::get('/requirements/{country:slug}', [CountryController::class, 'requirements'])->name('country.requirements');
Route::get('/process/{country:slug}', [CountryController::class, 'process'])->name('country.process');
Route::get('/faq/{country:slug}', [CountryController::class, 'faq'])->name('country.faq');
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{post:slug}', [BlogController::class, 'show'])->name('blog.show');
```

## SEO Middleware Implementation

### Canonical URL Middleware
```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CanonicalUrlMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        
        // Force HTTPS and www
        $canonicalUrl = 'https://www.' . config('app.domain') . $request->getPathInfo();
        
        if ($request->getQueryString()) {
            $canonicalUrl .= '?' . $request->getQueryString();
        }
        
        view()->share('canonical_url', $canonicalUrl);
        
        return $response;
    }
}
```

### SEO Data Middleware
```php
<?php

namespace App\Http\Middleware;

use App\Services\SeoService;
use Closure;

class SeoMiddleware
{
    protected $seoService;
    
    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }
    
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        
        // Generate SEO data for current route
        $seoData = $this->seoService->generateMetaTags(
            $request->route()->getName(),
            $request->route('country')
        );
        
        view()->share('seo', $seoData);
        
        return $response;
    }
}
```

## Sitemap Generation

### Automated Sitemap Service
```php
<?php

namespace App\Services;

use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Country;
use App\Models\BlogPost;

class SitemapService
{
    public function generate()
    {
        $sitemap = Sitemap::create();
        
        // Add homepage
        $sitemap->add(Url::create('/')
            ->setLastModificationDate(now())
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
            ->setPriority(1.0));
        
        // Add country pages
        Country::active()->each(function ($country) use ($sitemap) {
            $sitemap->add(Url::create("/driving-license/{$country->slug}")
                ->setLastModificationDate($country->updated_at)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(0.9));
                
            $sitemap->add(Url::create("/requirements/{$country->slug}")
                ->setLastModificationDate($country->updated_at)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.8));
        });
        
        // Add blog posts
        BlogPost::published()->each(function ($post) use ($sitemap) {
            $sitemap->add(Url::create("/blog/{$post->slug}")
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.7));
        });
        
        $sitemap->writeToFile(public_path('sitemap.xml'));
    }
}
```

## Frontend SEO Implementation

### Blade Template Structure
```php
<!-- resources/views/layouts/app.blade.php -->
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- SEO Meta Tags -->
    <title>{{ $seo['title'] ?? config('app.name') }}</title>
    <meta name="description" content="{{ $seo['description'] ?? '' }}">
    <link rel="canonical" href="{{ $canonical_url }}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{{ $seo['og_title'] ?? $seo['title'] }}">
    <meta property="og:description" content="{{ $seo['og_description'] ?? $seo['description'] }}">
    <meta property="og:image" content="{{ $seo['og_image'] ?? asset('images/default-og.jpg') }}">
    <meta property="og:url" content="{{ $canonical_url }}">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $seo['twitter_title'] ?? $seo['title'] }}">
    <meta name="twitter:description" content="{{ $seo['twitter_description'] ?? $seo['description'] }}">
    <meta name="twitter:image" content="{{ $seo['twitter_image'] ?? $seo['og_image'] }}">
    
    <!-- Schema.org JSON-LD -->
    @if(isset($schema))
        <script type="application/ld+json">
            {!! json_encode($schema) !!}
        </script>
    @endif
    
    <!-- Preload critical resources -->
    <link rel="preload" href="{{ asset('css/app.css') }}" as="style">
    <link rel="preload" href="{{ asset('fonts/main.woff2') }}" as="font" type="font/woff2" crossorigin>
    
    <!-- Stylesheets -->
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
</head>
```

## Monitoring and Analytics

### Performance Monitoring
```php
// Custom middleware for performance tracking
class PerformanceMiddleware
{
    public function handle($request, Closure $next)
    {
        $start = microtime(true);
        
        $response = $next($request);
        
        $duration = microtime(true) - $start;
        
        // Log slow requests
        if ($duration > 2.0) {
            Log::warning('Slow request detected', [
                'url' => $request->fullUrl(),
                'duration' => $duration,
                'memory' => memory_get_peak_usage(true)
            ]);
        }
        
        return $response;
    }
}
```
