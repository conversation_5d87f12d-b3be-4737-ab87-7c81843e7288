/* EU Driving License Custom Styles - European Union Blue Theme */

/* EU Blue Color Variables */
:root {
    --eu-blue: #003399;
    --eu-blue-light: #0066cc;
    --eu-blue-dark: #002266;
    --eu-gold: #FFCC00;
}

/* Override template colors with EU Blue - Primary Elements */
.feature-block-one .icon,
.counter-section,
.banner-carousel .slide-item:before,
.service-block-one .icon,
.author-info .designation,
.cta-section .wrapper-box:before,
.main-footer .footer-bottom:before,
.btn-style-one .btn-title,
.main-header .header-upper .inner-container,
.main-header.header-style-two .header-upper .logo-box .logo,
.testimonial-block .quote,
.sec-title .sub-title:before,
.sec-title .sub-title:after,
.preloader-close,
.scroll-to-top {
    background: var(--eu-blue) !important;
    background-image: linear-gradient(0deg, var(--eu-blue) 0%, var(--eu-blue-light) 100%) !important;
}

/* Override gradient backgrounds specifically */
.banner-section.style-two .slide-item:before,
.counter-section:before,
.cta-section .wrapper-box:before {
    background: linear-gradient(135deg, var(--eu-blue) 0%, var(--eu-blue-light) 50%, var(--eu-blue-dark) 100%) !important;
}

/* Navigation and interactive elements */
.main-menu .navigation > li.current > a,
.main-menu .navigation > li:hover > a,
.sticky-header .main-menu .navigation > li.current-menu-item > a,
.sticky-header .main-menu .navigation > li:hover > a {
    color: var(--eu-blue) !important;
}

/* Feature Section Icon Enhancements - Ultimate Override */
.feature-section .feature-block-one .icon {
    width: 70px !important;
    height: 70px !important;
    background: var(--eu-blue) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 20px !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 5px 15px rgba(0, 51, 153, 0.2) !important;
    font-size: 24px !important;
    line-height: 24px !important;
}

/* Force FontAwesome font family and visibility */
.feature-section .feature-block-one .icon i[class*="fa"] {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.feature-section .feature-block-one .icon:before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, var(--eu-blue-light) 0%, var(--eu-blue) 100%) !important;
    border-radius: 50% !important;
    z-index: 1 !important;
}

.feature-section .feature-block-one .icon span,
.feature-section .feature-block-one .icon i {
    position: relative !important;
    z-index: 2 !important;
    color: #ffffff !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.feature-section .feature-block-one:hover .icon {
    transform: translateY(-5px) scale(1.05) !important;
    box-shadow: 0 10px 25px rgba(0, 51, 153, 0.3) !important;
}

.feature-section .feature-block-one:hover .icon:before {
    background: linear-gradient(135deg, var(--eu-blue) 0%, var(--eu-blue-dark) 100%) !important;
}

.feature-section .feature-block-one:hover .icon span,
.feature-section .feature-block-one:hover .icon i {
    transform: scale(1.1) !important;
}

/* Feature block text styling */
.feature-section .feature-block-one h5 {
    color: #333333 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    text-align: center !important;
    margin: 0 !important;
    transition: color 0.3s ease !important;
}

.feature-section .feature-block-one:hover h5 {
    color: var(--eu-blue) !important;
}

/* Feature block container styling */
.feature-section .feature-block-one .inner-box {
    padding: 25px 15px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
}

.feature-section .feature-block-one:hover .inner-box {
    background: rgba(0, 51, 153, 0.02) !important;
    transform: translateY(-3px) !important;
}

/* Icon-specific animations for FontAwesome */
.feature-section .feature-block-one:hover .icon .fa-balance-scale {
    animation: scaleBalance 0.6s ease-in-out !important;
}

.feature-section .feature-block-one:hover .icon .fa-tachometer-alt {
    animation: speedometer 0.8s ease-in-out !important;
}

.feature-section .feature-block-one:hover .icon .fa-globe-europe {
    animation: globeRotate 0.6s ease-in-out !important;
}

.feature-section .feature-block-one:hover .icon .fa-headset {
    animation: headsetBounce 0.5s ease-in-out !important;
}

.feature-section .feature-block-one:hover .icon .fa-shield-alt {
    animation: shieldPulse 0.7s ease-in-out !important;
}

.feature-section .feature-block-one:hover .icon .fa-file-alt {
    animation: documentFlip 0.6s ease-in-out !important;
}

/* Icon-specific keyframe animations */
@keyframes scaleBalance {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

@keyframes speedometer {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.15) rotate(10deg); }
}

@keyframes globeRotate {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(90deg); }
    50% { transform: scale(1.1) rotate(180deg); }
    75% { transform: scale(1.1) rotate(270deg); }
}

@keyframes headsetBounce {
    0%, 100% { transform: scale(1) translateY(0); }
    50% { transform: scale(1.1) translateY(-3px); }
}

@keyframes shieldPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

@keyframes documentFlip {
    0%, 100% { transform: scale(1) rotateY(0deg); }
    50% { transform: scale(1.1) rotateY(15deg); }
}

/* Counter Section Icon Enhancements */
.counter-block-one .icon {
    width: 80px;
    height: 80px;
    background: var(--eu-blue) !important;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 51, 153, 0.25);
}

.counter-block-one .icon:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--eu-blue-light) 0%, var(--eu-blue) 100%);
    border-radius: 50%;
    z-index: 1;
}

.counter-block-one .icon span {
    position: relative;
    z-index: 2;
    color: #ffffff;
    font-size: 32px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.counter-block-one:hover .icon {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 51, 153, 0.35);
}

.counter-block-one:hover .icon:before {
    background: linear-gradient(135deg, var(--eu-blue) 0%, var(--eu-blue-dark) 100%);
}

.counter-block-one:hover .icon span {
    transform: scale(1.1);
}

/* Counter section text styling */
.counter-block-one h3 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-top: 15px;
    transition: color 0.3s ease;
}

.counter-block-one .count-outer {
    margin-bottom: 10px;
}

.counter-block-one .count-text {
    color: #ffffff;
    font-size: 48px;
    font-weight: 700;
    line-height: 1;
}

/* Service blocks and feature elements */
.service-block-one .icon {
    background: var(--eu-blue) !important;
}

.service-block-one .icon .round-shape:before,
.service-block-one .icon .round-shape:after {
    background: var(--eu-blue-light) !important;
}

/* Text colors */
.default-video-box a:hover,
.single-sidebar-box .categories li:hover a,
.banner-section.style-three .btn-style-one .btn-title:hover,
.banner-section.style-three .btn-style-two:hover .btn-title,
.banner-section.style-four .btn-style-one .btn-title:hover,
.banner-section.style-four .btn-style-two:hover .btn-title,
.insurance-calculate .btn-style-one .btn-title:hover,
.insurance-calculate .btn-style-two:hover .btn-title,
.header-top-two .top-right .social-links li a:hover,
.sidebar-wrapper .products-post li .title-holder .post-title a:hover,
.main-menu .navigation > li > ul > li > ul > li:hover > a,
.main-menu .navigation > li > ul > li:hover > a,
.sidebar-widget.about-me .social-links li a:hover,
.blog-single-post .post-share .social-links li a:hover,
.comments-area .comment-box .reply-btn:hover,
.news-block-two .read-more a,
.news-block-two h2 a:hover,
.news-block-one h3 a:hover,
.hidden-sidebar .news-widget h4 a:hover,
.sticky-header .main-menu .navigation > li:hover > a,
.sticky-header .main-menu .navigation > li.current > a,
.main-footer .links-widget ul li a:hover,
.testimonial-section.style-two .owl-theme .owl-nav .owl-prev:hover:before,
.testimonial-section.style-two .owl-theme .owl-nav .owl-next:hover:before,
.welcome-block-one:nth-child(1) .inner-box .read-more a:hover,
.welcome-block-one:nth-child(3) .inner-box .read-more a:hover {
    color: var(--eu-blue) !important;
}

/* Button styles */
.theme-btn.btn-style-one {
    background: linear-gradient(0deg, var(--eu-blue) 0%, var(--eu-blue-light) 100%) !important;
    border-color: var(--eu-blue) !important;
}

.theme-btn.btn-style-one:hover {
    background: var(--eu-blue-dark) !important;
    border-color: var(--eu-blue-dark) !important;
}

.theme-btn.btn-style-two {
    border-color: var(--eu-blue) !important;
    color: var(--eu-blue) !important;
}

.theme-btn.btn-style-two:hover {
    background: var(--eu-blue) !important;
    border-color: var(--eu-blue) !important;
}

/* Header customizations */
.main-header.header-style-two .header-top-two {
    background: var(--eu-blue-dark) !important;
}

.main-header.header-style-two .header-upper {
    background: #ffffff !important;
}

/* Reset navigation background to neutral */
.main-header .header-upper .inner-container {
    background: transparent !important;
}

.main-header.header-style-two .header-upper .logo-box .logo {
    background: transparent !important;
}

/* Ensure proper text contrast on EU blue backgrounds */
.counter-section,
.banner-carousel .slide-item,
.cta-section .wrapper-box,
.testimonial-section.style-two,
.feature-block-one .icon,
.service-block-one .icon {
    color: #ffffff !important;
}

.counter-section h3,
.counter-section .count-text,
.banner-section .content-box h2,
.banner-section .content-box h3,
.cta-section h2,
.cta-section .text,
.testimonial-section h2,
.testimonial-section .text {
    color: #ffffff !important;
}

/* Navigation hover effects */
.main-menu .navigation > li:hover > a,
.main-menu .navigation > li.current > a {
    color: var(--eu-blue) !important;
}

/* Country-specific customizations for driving license content */
.country-card {
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.country-card:hover {
    border-color: var(--eu-blue);
    box-shadow: 0 10px 25px rgba(0, 51, 153, 0.1);
}

.country-flag {
    border: 2px solid var(--eu-blue);
    border-radius: 4px;
}

.price-tag {
    background: linear-gradient(135deg, var(--eu-blue) 0%, var(--eu-blue-light) 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
}

.processing-time {
    background: var(--eu-gold);
    color: var(--eu-blue-dark);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Trust indicators */
.trust-indicator {
    background: rgba(0, 51, 153, 0.05);
    border: 1px solid rgba(0, 51, 153, 0.1);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.trust-indicator .icon {
    width: 60px;
    height: 60px;
    background: var(--eu-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 24px;
}

/* EU-specific styling */
.eu-badge {
    background: linear-gradient(135deg, var(--eu-blue) 0%, var(--eu-blue-light) 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.legal-notice {
    background: #f8f9fa;
    border-left: 4px solid var(--eu-blue);
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
}

/* Form styling */
.form-control:focus {
    border-color: var(--eu-blue) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 51, 153, 0.25) !important;
}

/* Custom animations */
@keyframes euPulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 51, 153, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 51, 153, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 51, 153, 0); }
}

.eu-pulse {
    animation: euPulse 2s infinite;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .country-card {
        margin-bottom: 20px;
    }

    .trust-indicator {
        padding: 15px;
    }

    .trust-indicator .icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Enhanced UI/UX Styling */

/* Testimonial Section Enhancements */
.testimonial-section.style-two {
    background: linear-gradient(135deg, var(--eu-blue-dark) 0%, var(--eu-blue) 100%);
    position: relative;
    overflow: hidden;
}

.testimonial-section.style-two:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 51, 153, 0.8);
    z-index: 1;
}

.testimonial-section .auto-container {
    position: relative;
    z-index: 2;
}

.testimonial-block .inner-box {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 51, 153, 0.1);
}

.testimonial-block .inner-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    background: #ffffff;
}

.testimonial-block .rating {
    margin-bottom: 15px;
}

.testimonial-block .rating i {
    color: var(--eu-gold);
    font-size: 14px;
    margin-right: 2px;
}

.testimonial-block .license-type {
    color: var(--eu-blue);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 5px;
}

/* News Section Enhancements */
.news-section {
    background: #f8f9fa;
    padding: 100px 0;
}

.news-block-one .inner-box {
    background: #ffffff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.news-block-one .inner-box:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: var(--eu-blue);
}

.news-block-one .image {
    position: relative;
    overflow: hidden;
}

.news-block-one .image img {
    transition: transform 0.3s ease;
}

.news-block-one .inner-box:hover .image img {
    transform: scale(1.05);
}

.news-block-one .category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--eu-blue);
    color: #ffffff;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
}

.news-block-one .lower-content {
    padding: 25px;
}

.news-block-one .post-meta ul {
    margin-bottom: 15px;
}

.news-block-one .post-meta li {
    color: #6c757d;
    font-size: 13px;
    margin-right: 20px;
}

.news-block-one .post-meta i {
    color: var(--eu-blue);
    margin-right: 5px;
}

.news-block-one h3 a {
    color: #212529;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.news-block-one h3 a:hover {
    color: var(--eu-blue);
}

.news-block-one .text {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.6;
    margin: 15px 0 20px;
}

.news-block-one .link-btn a {
    color: var(--eu-blue);
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.news-block-one .link-btn a:hover {
    color: var(--eu-blue-dark);
}

.news-block-one .link-btn i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.news-block-one .inner-box:hover .link-btn i {
    transform: translateX(3px);
}

/* CTA Section Fix */
.cta-section {
    position: relative;
    z-index: 1;
    margin-bottom: 0;
    padding: 80px 0;
}

.cta-section .wrapper-box {
    position: relative;
    z-index: 2;
    border-radius: 10px;
    padding: 60px 40px;
    margin: 0 auto;
    max-width: 1200px;
}

/* Enhanced Button Styling */
.theme-btn.btn-style-one {
    background: linear-gradient(135deg, var(--eu-blue) 0%, var(--eu-blue-light) 100%);
    border: 2px solid var(--eu-blue);
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 5px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-btn.btn-style-one:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.theme-btn.btn-style-one:hover:before {
    left: 100%;
}

.theme-btn.btn-style-one:hover {
    background: var(--eu-blue-dark);
    border-color: var(--eu-blue-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 51, 153, 0.3);
}

/* Service Block Enhancements */
.service-block-one .inner-box {
    background: #ffffff;
    border-radius: 10px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    height: 100%;
}

.service-block-one .inner-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--eu-blue);
}

.service-block-one .icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.service-block-one .icon img {
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.service-block-one .inner-box:hover .icon img {
    transform: scale(1.1);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--eu-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 51, 153, 0.3);
}

.scroll-to-top.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: var(--eu-blue-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 51, 153, 0.4);
}

.scroll-to-top span {
    font-weight: 600;
    transition: transform 0.3s ease;
}

.scroll-to-top:hover span {
    transform: translateY(-2px);
}

/* Pulse animation for scroll to top */
@keyframes scrollPulse {
    0% {
        box-shadow: 0 5px 20px rgba(0, 51, 153, 0.3), 0 0 0 0 rgba(0, 51, 153, 0.7);
    }
    70% {
        box-shadow: 0 5px 20px rgba(0, 51, 153, 0.3), 0 0 0 10px rgba(0, 51, 153, 0);
    }
    100% {
        box-shadow: 0 5px 20px rgba(0, 51, 153, 0.3), 0 0 0 0 rgba(0, 51, 153, 0);
    }
}

.scroll-to-top.pulse {
    animation: scrollPulse 2s infinite;
}

/* Mobile-First Responsive Design */

/* Mobile Menu Enhancements */
.mobile-menu-visible {
    overflow: hidden !important;
}

.mobile-menu-visible .mobile-menu {
    visibility: visible;
    opacity: 1;
}

.mobile-nav-toggler {
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-nav-toggler:hover {
    transform: scale(1.1);
}

.mobile-nav-toggler .icon i {
    font-size: 20px;
    color: var(--eu-blue);
    transition: all 0.3s ease;
}

.mobile-nav-toggler:hover .icon i {
    color: var(--eu-blue-dark);
}

/* Mobile Menu Close Button */
.mobile-menu .close-btn {
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-menu .close-btn:hover {
    transform: scale(1.1);
}

.mobile-menu .close-btn .icon i {
    font-size: 18px;
    color: #ffffff;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    /* Scroll to top mobile optimization */
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 18px;
    }

    /* Feature section mobile optimization */
    .feature-block-one .icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 15px;
    }

    .feature-block-one .icon i {
        font-size: 20px;
    }

    .feature-block-one h5 {
        font-size: 13px;
    }

    .feature-section .wrapper-box {
        padding: 40px 15px;
    }

    /* Counter section mobile optimization */
    .counter-block-one .icon {
        width: 70px;
        height: 70px;
        margin: 0 auto 20px;
    }

    .counter-block-one .icon span {
        font-size: 28px;
    }

    .counter-block-one h3 {
        font-size: 16px;
    }

    .counter-block-one .count-text {
        font-size: 40px;
    }

    /* Header mobile optimization */
    .header-top-two {
        display: none; /* Hide top header on mobile */
    }

    .header-upper {
        padding: 15px 0;
    }

    .logo-box .logo img {
        max-height: 40px;
        width: auto;
    }

    /* Main menu hidden on mobile */
    .main-menu {
        display: none;
    }

    .link-btn {
        display: none; /* Hide CTA button on mobile to save space */
    }

    /* Mobile navigation toggler visible */
    .mobile-nav-toggler {
        display: block;
        position: relative;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 51, 153, 0.1);
        border-radius: 6px;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
    /* Feature section extra small mobile */
    .feature-block-one .icon {
        width: 50px;
        height: 50px;
        margin: 0 auto 12px;
    }

    .feature-block-one .icon i {
        font-size: 18px;
    }

    .feature-block-one h5 {
        font-size: 12px;
        line-height: 1.3;
    }

    /* Header extra small mobile */
    .logo-box .logo img {
        max-height: 35px;
    }

    .mobile-nav-toggler {
        width: 40px;
        height: 40px;
    }

    .mobile-nav-toggler .icon i {
        font-size: 18px;
    }

    /* Footer mobile optimization */
    .footer-widget h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .footer-widget .text {
        font-size: 14px;
        line-height: 1.5;
    }

    /* Counter section extra small mobile */
    .counter-block-one .icon {
        width: 60px;
        height: 60px;
        margin: 0 auto 15px;
    }

    .counter-block-one .icon span {
        font-size: 24px;
    }

    .counter-block-one h3 {
        font-size: 14px;
    }

    .counter-block-one .count-text {
        font-size: 36px;
    }

    /* Country cards mobile optimization */
    .service-block-one .inner-box {
        padding: 20px 15px;
    }

    .service-block-one h3 {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .service-block-one .price-tag {
        font-size: 16px;
        padding: 8px 15px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .feature-block-one:hover .inner-box {
        transform: none;
        background: transparent;
    }

    .feature-block-one:hover .icon {
        transform: none;
        box-shadow: 0 5px 15px rgba(0, 51, 153, 0.2);
    }

    /* Increase touch targets */
    .mobile-nav-toggler {
        min-width: 48px;
        min-height: 48px;
    }

    .scroll-to-top {
        min-width: 48px;
        min-height: 48px;
    }

    /* Improve button touch targets */
    .theme-btn {
        min-height: 48px;
        padding: 12px 25px;
    }
}

/* Enhanced Accessibility */
.theme-btn:focus,
.news-block-one .link-btn a:focus,
.service-block-one h3 a:focus,
.scroll-to-top:focus {
    outline: 2px solid var(--eu-blue);
    outline-offset: 2px;
}

/* Loading States */
.lazy-image {
    background: #f8f9fa;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lazy-image:before {
    content: 'Loading...';
    color: #6c757d;
    font-size: 14px;
}

/* Page Loading States */
body:not(.page-loaded) .scroll-to-top {
    display: none;
}

.page-loaded .scroll-to-top {
    display: flex;
}

/* Enhanced hover effects for country cards */
.service-block-one .inner-box .price-tag.eu-pulse {
    animation: euPulse 1.5s infinite;
}

/* Smooth transitions for all interactive elements */
.service-block-one .inner-box,
.news-block-one .inner-box,
.testimonial-block .inner-box,
.theme-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced mobile menu visibility */
.mobile-menu-visible .mobile-menu {
    visibility: visible;
    opacity: 1;
}

/* Form field enhancements */
.form-control.has-value {
    border-color: var(--eu-blue);
    background-color: rgba(0, 51, 153, 0.02);
}

/* Image loading states */
img.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

img:not(.loaded) {
    opacity: 0.7;
}

/* Touching state for mobile carousels */
.testimonial-carousel.touching {
    cursor: grabbing;
}

.testimonial-carousel.touching .testimonial-block {
    pointer-events: none;
}

/* Print styles */
@media print {
    .theme-btn,
    .social-links,
    .mobile-nav-toggler,
    .testimonial-carousel,
    .news-section,
    .scroll-to-top {
        display: none !important;
    }

    .testimonial-block .inner-box,
    .news-block-one .inner-box {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .scroll-to-top {
        display: none !important;
    }
}

/* Mobile Menu - Template Exact Match */
.mobile-menu .menu-backdrop {
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: rgba(0, 0, 0, 0.7);
    -webkit-transform: translateX(101%);
    -ms-transform: translateX(101%);
    transform: translateX(101%);
    transition: all 900ms ease;
    -moz-transition: all 900ms ease;
    -webkit-transition: all 900ms ease;
    -ms-transition: all 900ms ease;
    -o-transition: all 900ms ease;
}

.mobile-menu-visible .mobile-menu .menu-backdrop {
    opacity: 0.70;
    visibility: visible;
    -webkit-transition: all 0.7s ease;
    -o-transition: all 0.7s ease;
    transition: all 0.7s ease;
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
}

.mobile-menu .menu-box {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
    background: #202020;
    padding: 0px 0px;
    z-index: 5;
    opacity: 0;
    visibility: hidden;
    border-radius: 0px;
    -webkit-transform: translateX(101%);
    -ms-transform: translateX(101%);
    transform: translateX(101%);
}

.mobile-menu-visible .mobile-menu .menu-box {
    opacity: 1;
    visibility: visible;
    -webkit-transition: all 0.7s ease;
    -o-transition: all 0.7s ease;
    transition: all 0.7s ease;
    -webkit-transform: translateX(0%);
    -ms-transform: translateX(0%);
    transform: translateX(0%);
}

/* Force mobile nav toggler visibility */
@media (max-width: 991px) {
    .mobile-nav-toggler {
        display: block !important;
        cursor: pointer !important;
    }

    .main-menu {
        display: none !important;
    }
}
