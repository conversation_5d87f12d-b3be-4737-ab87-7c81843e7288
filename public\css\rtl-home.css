.rtl-home .banner-carousel .content-box {
    text-align: right;
    margin-right: 0;
}

.rtl-home .main-header .header-top .inner {
    flex-direction: row-reverse;
}

.rtl-home .sec-title {
    text-align: right;
}

.rtl-home .about-section .content-box .info-column {
    text-align: right;
}

.rtl-home .about-section .content-box .info-column:first-child:before {
    right: -15px;
}

.rtl-home .about-section .content-box .info-column:first-child:after {
    right: -30px;
}

.rtl-home .services-section .icon-box {
    text-align: right;
    padding-left: 0;
    padding-right: 110px;
}

.rtl-home .services-section .icon-box .icon {
    left: auto;
    right: 0;
}

.rtl-home .services-section .author-box {
    flex-direction: row-reverse;
    text-align: right;
}

.rtl-home .services-section .author-box .author-info {
    padding-left: 0;
    padding-right: 180px;
}

.rtl-home .services-section .author-box .author-thumb {
    left: auto;
    right: 0;
}

.rtl-home .gallery-block-one .caption-title {
    left: auto;
    right: 0;
    text-align: right;
}

.rtl-home .gallery-block-one .caption-title h5 {
    padding-right: 40px;
    padding-left: 0;
}

.rtl-home .gallery-block-one .caption-title h5:before {
    left: auto;
    right: 0;
}

.rtl-home .gallery-block-one .caption-title .view-project {
    left: 0;
    right: auto;
}

.rtl-home .why-chooseus-section .content-box .text p {
    text-align: right;
}

.rtl-home .why-chooseus-section .content-box .image {
    float: right;
    margin-right: 0;
    margin-left: 20px;
}

.rtl-home .tab-btn-style-one {
    justify-content: flex-end;
}

.rtl-home .contact-form label {
    text-align: right;
    display: block;
}

.rtl-home .contact-form .form-group input[type="text"], .contact-form .form-group input[type="email"], .contact-form .form-group textarea, .contact-form .form-group select {
    text-align: right;
}

.rtl-home .contact-form i {
    left: 30px;
    right: auto;
}

.rtl-home .contact-form .form-group input[type="submit"], .contact-form .form-group button {
    text-align: right;
    float: right;
}

.rtl-home .news-block-one .lower-content {
    text-align: right;
}

.rtl-home .news-block-one .post-meta {
    justify-content: flex-end;
}

.rtl-home .news-block-one .category {
    margin-left: 0;
}

.rtl-home .main-footer .footer-widget {
    text-align: right;
}

.rtl-home .instagram-widget .wrapper-box {justify-content: flex-end;}

.rtl-home .banner-carousel .content-box:before {
    right: -220px;
    left: auto;
}

.rtl-home .main-header .nav-outer .main-menu {
    float: right;
    text-align: right;
    margin-right: 20px;
}

.rtl-home .main-header .header-upper .social-links {
    float: left;
}

.rtl-home .main-header .header-upper .social-links li:first-child {
    border: 0;
}

.rtl-home .main-menu .navigation > li > ul {
    text-align: right;
}

.rtl-home .main-menu .navigation > li > ul li a {
    text-align: right;
}

.rtl-home .main-menu .navigation > li > ul {
    left: auto;
    right: 0;
}

.rtl-home .main-menu .navigation > li > ul > li > ul {
    left: auto;
    right: 100%;
}






