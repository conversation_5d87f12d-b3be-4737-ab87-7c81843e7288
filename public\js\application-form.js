/**
 * Multi-step Application Form Handler
 */
class ApplicationForm {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        this.formData = {};
        this.countryCode = document.getElementById('application-form').dataset.country;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStepDisplay();
        this.loadFormData();
    }

    bindEvents() {
        // Next button
        document.getElementById('next-btn').addEventListener('click', () => {
            this.nextStep();
        });

        // Previous button
        document.getElementById('prev-btn').addEventListener('click', () => {
            this.prevStep();
        });

        // Form submission
        document.getElementById('application-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitApplication();
        });

        // Auto-save on input change
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormData();
            });
        });
    }

    nextStep() {
        if (this.validateCurrentStep()) {
            this.saveFormData();
            
            if (this.currentStep < this.totalSteps) {
                this.currentStep++;
                this.showStep(this.currentStep);
                this.updateStepDisplay();
            }
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.showStep(this.currentStep);
            this.updateStepDisplay();
        }
    }

    showStep(step) {
        // Hide all step contents
        document.querySelectorAll('.step-content').forEach(content => {
            content.style.display = 'none';
        });

        // Show current step content
        const currentContent = document.getElementById(`step-${step}`);
        if (currentContent) {
            currentContent.style.display = 'block';
        } else {
            // Create step content dynamically
            this.createStepContent(step);
        }

        // Update card header
        this.updateCardHeader(step);
    }

    createStepContent(step) {
        const form = document.getElementById('application-form');
        const existingContent = document.querySelector('.step-content');
        
        let stepContent = '';
        
        switch(step) {
            case 2:
                stepContent = this.getStep2Content();
                break;
            case 3:
                stepContent = this.getStep3Content();
                break;
            case 4:
                stepContent = this.getStep4Content();
                break;
            case 5:
                stepContent = this.getStep5Content();
                break;
        }

        if (stepContent) {
            const stepDiv = document.createElement('div');
            stepDiv.className = 'step-content';
            stepDiv.id = `step-${step}`;
            stepDiv.innerHTML = stepContent;
            
            // Insert before navigation buttons
            const navigation = document.querySelector('.form-navigation');
            form.insertBefore(stepDiv, navigation);
        }
    }

    getStep2Content() {
        return `
            <h4 class="text-lg font-medium text-gray-900 mb-4">Address & Contact Information</h4>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label required">Email Address</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label required">Phone Number</label>
                    <input type="tel" class="form-control" id="phone" name="phone" required>
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="mb-3">
                <label for="address_line_1" class="form-label required">Address Line 1</label>
                <input type="text" class="form-control" id="address_line_1" name="address_line_1" required>
                <div class="invalid-feedback"></div>
            </div>
            <div class="mb-3">
                <label for="address_line_2" class="form-label">Address Line 2 (Optional)</label>
                <input type="text" class="form-control" id="address_line_2" name="address_line_2">
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="city" class="form-label required">City</label>
                    <input type="text" class="form-control" id="city" name="city" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="postal_code" class="form-label required">Postal Code</label>
                    <input type="text" class="form-control" id="postal_code" name="postal_code" required>
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="mb-3">
                <label for="country" class="form-label required">Country</label>
                <select class="form-select" id="country" name="country" required>
                    <option value="">Select Country</option>
                    <option value="Ireland">Ireland</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Germany">Germany</option>
                    <option value="France">France</option>
                    <option value="Spain">Spain</option>
                    <option value="Italy">Italy</option>
                    <option value="Netherlands">Netherlands</option>
                    <option value="Other">Other</option>
                </select>
                <div class="invalid-feedback"></div>
            </div>
        `;
    }

    getStep3Content() {
        return `
            <h4 class="text-lg font-medium text-gray-900 mb-4">License Details</h4>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="license_category" class="form-label required">License Category</label>
                    <select class="form-select" id="license_category" name="license_category" required>
                        <option value="">Select Category</option>
                        <option value="B">Category B (Car)</option>
                        <option value="A">Category A (Motorcycle)</option>
                        <option value="C">Category C (Truck)</option>
                        <option value="D">Category D (Bus)</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="package_type" class="form-label required">Package Type</label>
                    <select class="form-select" id="package_type" name="package_type" required>
                        <option value="">Select Package</option>
                        <option value="standard">Standard Package</option>
                        <option value="express">Express Package (+20%)</option>
                        <option value="premium">Premium Package (+50%)</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="processing_speed" class="form-label required">Processing Speed</label>
                    <select class="form-select" id="processing_speed" name="processing_speed" required>
                        <option value="">Select Speed</option>
                        <option value="standard">Standard (7-14 days)</option>
                        <option value="express">Express (+€50, 3-7 days)</option>
                        <option value="urgent">Urgent (+€100, 1-3 days)</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="existing_license" class="form-label">Do you have an existing license?</label>
                    <select class="form-select" id="existing_license" name="existing_license">
                        <option value="no">No</option>
                        <option value="yes">Yes</option>
                    </select>
                </div>
            </div>
            <div class="mb-3">
                <label for="special_requirements" class="form-label">Special Requirements (Optional)</label>
                <textarea class="form-control" id="special_requirements" name="special_requirements" rows="3" 
                          placeholder="Any special requirements or notes..."></textarea>
            </div>
        `;
    }

    getStep4Content() {
        return `
            <h4 class="text-lg font-medium text-gray-900 mb-4">Document Upload</h4>
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                Please upload clear, high-quality images of your documents. Accepted formats: JPG, PNG, PDF (max 5MB each).
            </div>
            
            <div class="mb-4">
                <label class="form-label required">Signature on White Paper</label>
                <div class="upload-area" data-upload="signature">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Click to upload or drag and drop</p>
                    <small>JPG, PNG, PDF up to 5MB</small>
                </div>
                <input type="file" id="signature" name="signature" accept=".jpg,.jpeg,.png,.pdf" style="display: none;" required>
            </div>
            
            <div class="mb-4">
                <label class="form-label required">ID/Passport Photo</label>
                <div class="upload-area" data-upload="id_photo">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Click to upload or drag and drop</p>
                    <small>JPG, PNG, PDF up to 5MB</small>
                </div>
                <input type="file" id="id_photo" name="id_photo" accept=".jpg,.jpeg,.png,.pdf" style="display: none;" required>
            </div>
            
            <div class="mb-4">
                <label class="form-label">Additional Documents (Optional)</label>
                <div class="upload-area" data-upload="additional_docs">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Click to upload or drag and drop</p>
                    <small>JPG, PNG, PDF up to 5MB</small>
                </div>
                <input type="file" id="additional_docs" name="additional_docs" accept=".jpg,.jpeg,.png,.pdf" style="display: none;">
            </div>
        `;
    }

    getStep5Content() {
        return `
            <h4 class="text-lg font-medium text-gray-900 mb-4">Review & Submit</h4>
            <div class="alert alert-warning mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Please review all information carefully before submitting. You will receive a confirmation email with your application number.
            </div>
            
            <div id="review-content">
                <!-- Review content will be populated dynamically -->
            </div>
            
            <div class="form-check mb-4">
                <input class="form-check-input" type="checkbox" id="terms_accepted" name="terms_accepted" required>
                <label class="form-check-label" for="terms_accepted">
                    I agree to the <a href="/terms-of-service" target="_blank">Terms of Service</a> and 
                    <a href="/privacy-policy" target="_blank">Privacy Policy</a>
                </label>
            </div>
        `;
    }

    updateCardHeader(step) {
        const stepTitles = {
            1: 'Personal Information',
            2: 'Address & Contact',
            3: 'License Details',
            4: 'Documents Upload',
            5: 'Review & Submit'
        };

        const cardTitle = document.querySelector('.card-title');
        if (cardTitle) {
            cardTitle.innerHTML = `
                <i class="fas fa-user-circle text-primary me-2"></i>
                Step ${step}: ${stepTitles[step]}
            `;
        }
    }

    updateStepDisplay() {
        // Update progress steps
        document.querySelectorAll('.step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });

        // Update navigation buttons
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const submitBtn = document.getElementById('submit-btn');

        prevBtn.style.display = this.currentStep > 1 ? 'block' : 'none';
        
        if (this.currentStep === this.totalSteps) {
            nextBtn.style.display = 'none';
            submitBtn.style.display = 'block';
        } else {
            nextBtn.style.display = 'block';
            submitBtn.style.display = 'none';
        }
    }

    validateCurrentStep() {
        const currentStepElement = document.getElementById(`step-${this.currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        return isValid;
    }

    showFieldError(field, message) {
        field.classList.add('is-invalid');
        const feedback = field.nextElementSibling;
        if (feedback && feedback.classList.contains('invalid-feedback')) {
            feedback.textContent = message;
        }
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const feedback = field.nextElementSibling;
        if (feedback && feedback.classList.contains('invalid-feedback')) {
            feedback.textContent = '';
        }
    }

    saveFormData() {
        const formData = new FormData(document.getElementById('application-form'));
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // Save to localStorage for auto-save functionality
        localStorage.setItem(`application_form_${this.countryCode}`, JSON.stringify(data));
    }

    loadFormData() {
        const savedData = localStorage.getItem(`application_form_${this.countryCode}`);
        if (savedData) {
            const data = JSON.parse(savedData);
            
            Object.keys(data).forEach(key => {
                const field = document.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = data[key];
                }
            });
        }
    }

    submitApplication() {
        if (!this.validateCurrentStep()) {
            return;
        }

        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        // Here you would normally submit to your backend
        // For now, we'll simulate a submission
        setTimeout(() => {
            loadingModal.hide();
            
            // Clear saved form data
            localStorage.removeItem(`application_form_${this.countryCode}`);
            
            // Redirect to success page or show success message
            alert('Application submitted successfully! You will receive a confirmation email shortly.');
            
            // Redirect to home or thank you page
            window.location.href = '/en';
        }, 3000);
    }
}

// Initialize the form when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('application-form')) {
        new ApplicationForm();
    }
});
