/**
 * EU Driving License Custom JavaScript
 * Enhanced scroll-to-top functionality and other custom features
 */

(function($) {
    'use strict';

    // Document Ready
    $(document).ready(function() {

        // Initialize scroll to top functionality
        initScrollToTop();

        // Initialize other custom features
        initCustomFeatures();
    });

    /**
     * Enhanced Scroll to Top Functionality
     */
    function initScrollToTop() {
        var scrollToTopBtn = $('.scroll-to-top');
        var scrollThreshold = 300; // Show button after scrolling 300px
        var pulseThreshold = 1000; // Add pulse animation after 1000px

        // Show/hide scroll to top button based on scroll position
        $(window).scroll(function() {
            var scrollTop = $(this).scrollTop();

            if (scrollTop > scrollThreshold) {
                scrollToTopBtn.addClass('active');

                // Add pulse animation for long scrolls
                if (scrollTop > pulseThreshold) {
                    scrollToTopBtn.addClass('pulse');
                } else {
                    scrollToTopBtn.removeClass('pulse');
                }
            } else {
                scrollToTopBtn.removeClass('active pulse');
            }
        });

        // Smooth scroll to top when button is clicked
        scrollToTopBtn.on('click', function(e) {
            e.preventDefault();

            // Remove pulse animation
            scrollToTopBtn.removeClass('pulse');

            // Smooth scroll to top
            $('html, body').animate({
                scrollTop: 0
            }, 800, 'easeInOutQuart');

            // Optional: Track scroll to top clicks for analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'scroll_to_top', {
                    'event_category': 'engagement',
                    'event_label': 'scroll_to_top_button'
                });
            }
        });

        // Keyboard accessibility
        scrollToTopBtn.on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).click();
            }
        });

        // Add ARIA attributes for accessibility
        scrollToTopBtn.attr({
            'role': 'button',
            'aria-label': 'Scroll to top of page',
            'tabindex': '0'
        });
    }

    /**
     * Initialize other custom features
     */
    function initCustomFeatures() {

        // Enhanced country card hover effects
        $('.service-block-one .inner-box').hover(
            function() {
                $(this).find('.price-tag').addClass('eu-pulse');
            },
            function() {
                $(this).find('.price-tag').removeClass('eu-pulse');
            }
        );

        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 100
                }, 800, 'easeInOutQuart');
            }
        });

        // Enhanced testimonial carousel touch support
        if ($('.testimonial-carousel').length) {
            $('.testimonial-carousel').on('touchstart', function(e) {
                $(this).addClass('touching');
            }).on('touchend', function(e) {
                $(this).removeClass('touching');
            });
        }

        // Add loading states for images
        $('img[data-src]').each(function() {
            var $img = $(this);
            var src = $img.attr('data-src');

            if (src) {
                var image = new Image();
                image.onload = function() {
                    $img.attr('src', src).addClass('loaded');
                };
                image.src = src;
            }
        });

        // Enhanced form validation feedback
        $('.form-control').on('blur', function() {
            var $field = $(this);
            if ($field.val().trim() !== '') {
                $field.addClass('has-value');
            } else {
                $field.removeClass('has-value');
            }
        });

        // Country selection tracking
        $('.service-block-one .theme-btn').on('click', function() {
            var countryName = $(this).closest('.service-block-one').find('h3 a').text();

            // Track country selection for analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'country_selection', {
                    'event_category': 'engagement',
                    'event_label': countryName,
                    'value': 1
                });
            }
        });

        // Enhanced mobile menu functionality
        $('.mobile-nav-toggler').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Mobile nav toggler clicked'); // Debug log
            $('body').toggleClass('mobile-menu-visible');
            console.log('Body classes:', document.body.className); // Debug log

            // Track mobile menu usage
            if (typeof gtag !== 'undefined') {
                gtag('event', 'mobile_menu_toggle', {
                    'event_category': 'navigation',
                    'event_label': 'mobile_menu_open'
                });
            }
        });

        // Close mobile menu with close button
        $('.mobile-menu .close-btn').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $('body').removeClass('mobile-menu-visible');
        });

        // Close mobile menu when clicking backdrop
        $('.mobile-menu .menu-backdrop').on('click', function(e) {
            e.preventDefault();
            $('body').removeClass('mobile-menu-visible');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.mobile-menu, .mobile-nav-toggler').length) {
                $('body').removeClass('mobile-menu-visible');
            }
        });

        // Close mobile menu on escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('body').hasClass('mobile-menu-visible')) {
                $('body').removeClass('mobile-menu-visible');
            }
        });

        // Prevent body scroll when mobile menu is open
        function toggleBodyScroll() {
            if ($('body').hasClass('mobile-menu-visible')) {
                $('body').css({
                    'overflow': 'hidden',
                    'position': 'fixed',
                    'width': '100%'
                });
            } else {
                $('body').css({
                    'overflow': '',
                    'position': '',
                    'width': ''
                });
            }
        }

        // Watch for mobile menu visibility changes
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    toggleBodyScroll();
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        // Add smooth transitions to page elements
        $('.wow').each(function() {
            $(this).addClass('animated');
        });
    }

    /**
     * Custom easing function for smooth animations
     */
    $.easing.easeInOutQuart = function(x, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t * t + b;
        return -c / 2 * ((t -= 2) * t * t * t - 2) + b;
    };

    /**
     * Utility function to check if element is in viewport
     */
    function isInViewport(element) {
        var rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Performance optimized scroll handler
     */
    var ticking = false;
    function updateScrollElements() {
        // Update scroll-dependent elements here
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollElements);
            ticking = true;
        }
    }

    // Optimized scroll event
    $(window).on('scroll', requestTick);

})(jQuery);

/**
 * Initialize when DOM is fully loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add loaded class to body for CSS transitions
    document.body.classList.add('page-loaded');

    // Debug mobile navigation elements
    console.log('EU Driving License website loaded successfully');
    console.log('Mobile nav toggler:', document.querySelector('.mobile-nav-toggler'));
    console.log('Mobile menu:', document.querySelector('.mobile-menu'));
    console.log('jQuery loaded:', typeof $ !== 'undefined');

    // Fallback mobile navigation if jQuery fails
    const mobileToggler = document.querySelector('.mobile-nav-toggler');
    const mobileMenu = document.querySelector('.mobile-menu');
    const closeBtn = document.querySelector('.mobile-menu .close-btn');
    const backdrop = document.querySelector('.mobile-menu .menu-backdrop');

    if (mobileToggler && mobileMenu) {
        // Vanilla JS fallback
        mobileToggler.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Vanilla JS mobile toggle clicked');
            document.body.classList.toggle('mobile-menu-visible');
        });

        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Vanilla JS close button clicked');
                document.body.classList.remove('mobile-menu-visible');
            });
        }

        if (backdrop) {
            backdrop.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Vanilla JS backdrop clicked');
                document.body.classList.remove('mobile-menu-visible');
            });
        }
    }
});
