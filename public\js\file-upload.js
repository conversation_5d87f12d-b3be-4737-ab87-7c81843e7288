// Simple File Upload Handler
console.log('File upload script loaded');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing file upload');
    
    // Find all upload areas
    const uploadAreas = document.querySelectorAll('.upload-area');
    console.log('Found upload areas:', uploadAreas.length);
    
    uploadAreas.forEach(function(area) {
        const input = area.querySelector('input[type="file"]');
        const clickZone = area.querySelector('.border-dashed');
        
        if (!input || !clickZone) {
            console.log('Missing input or click zone in area:', area);
            return;
        }
        
        console.log('Setting up upload area:', area.dataset.documentType);
        
        // Click to upload
        clickZone.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Upload area clicked');
            input.click();
        });
        
        // File selection
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                console.log('File selected:', file.name);
                handleFileUpload(file, area);
            }
        });
        
        // Drag and drop
        clickZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            clickZone.classList.add('border-blue-400', 'bg-blue-50');
        });
        
        clickZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            clickZone.classList.remove('border-blue-400', 'bg-blue-50');
        });
        
        clickZone.addEventListener('drop', function(e) {
            e.preventDefault();
            clickZone.classList.remove('border-blue-400', 'bg-blue-50');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                console.log('File dropped:', files[0].name);
                input.files = files;
                handleFileUpload(files[0], area);
            }
        });
    });
});

function handleFileUpload(file, area) {
    console.log('Handling file upload:', file.name);
    
    // Validate file
    if (!validateFile(file)) {
        return;
    }
    
    // Show success state
    const uploadContent = area.querySelector('.upload-content');
    const successContent = area.querySelector('.upload-success');
    
    if (uploadContent && successContent) {
        uploadContent.classList.add('hidden');
        successContent.classList.remove('hidden');
        
        // Update success message
        const message = successContent.querySelector('p');
        if (message) {
            message.textContent = file.name + ' uploaded successfully';
        }
        
        console.log('UI updated for successful upload');
    }
}

function validateFile(file) {
    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return false;
    }
    
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
        alert('Please upload only JPG, PNG, or PDF files');
        return false;
    }
    
    return true;
}
