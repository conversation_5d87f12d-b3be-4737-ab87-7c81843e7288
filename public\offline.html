<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - EU Driving License</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON>', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: #6c757d;
        }
        
        .retry-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .cached-pages {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        
        .cached-pages h3 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .cached-links {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .cached-link {
            background: #f8f9fa;
            color: #495057;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .cached-link:hover {
            background: #e9ecef;
            color: #212529;
        }
        
        .connection-status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .connection-status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .connection-status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .retry-button {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - you can still browse some of our cached pages while offline.
        </p>
        
        <div class="connection-status offline" id="connectionStatus">
            🔴 No internet connection
        </div>
        
        <button class="retry-button" onclick="retryConnection()">
            🔄 Try Again
        </button>
        
        <a href="/" class="retry-button">
            🏠 Go Home
        </a>
        
        <div class="cached-pages">
            <h3>Available Offline Pages</h3>
            <div class="cached-links">
                <a href="/" class="cached-link">Home</a>
                <a href="/en/about" class="cached-link">About Us</a>
                <a href="/en/contact" class="cached-link">Contact</a>
                <a href="/en/how-it-works" class="cached-link">How It Works</a>
                <a href="/en/pricing" class="cached-link">Pricing</a>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🟢 Back online!';
                statusElement.className = 'connection-status online';
                
                // Auto-reload after a short delay when back online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.textContent = '🔴 No internet connection';
                statusElement.className = 'connection-status offline';
            }
        }
        
        // Retry connection
        function retryConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                window.location.reload();
            } else {
                // Show feedback that we're still offline
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '❌ Still offline';
                
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);
        
        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                console.log('Service Worker is ready');
            });
        }
        
        // Add click tracking for offline analytics
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                // Store offline clicks for later analytics
                const clickData = {
                    url: e.target.href,
                    timestamp: Date.now(),
                    type: 'offline_click'
                };
                
                // Store in localStorage for later sync
                const offlineClicks = JSON.parse(localStorage.getItem('offlineClicks') || '[]');
                offlineClicks.push(clickData);
                localStorage.setItem('offlineClicks', JSON.stringify(offlineClicks));
            }
        });
    </script>
</body>
</html>
