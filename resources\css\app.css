@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    /* EU Driving License Brand Colors */
    --color-eu-blue: #003399;
    --color-eu-blue-light: #0066cc;
    --color-eu-blue-dark: #002266;
    --color-eu-gold: #ffcc00;
    --color-eu-gold-light: #ffdd33;
    --color-eu-gold-dark: #cc9900;

    /* Primary Brand Colors */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;
    --color-primary-950: #172554;

    /* Success Colors */
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    --color-success-200: #bbf7d0;
    --color-success-300: #86efac;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;
    --color-success-950: #052e16;

    /* Warning Colors */
    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    --color-warning-200: #fde68a;
    --color-warning-300: #fcd34d;
    --color-warning-400: #fbbf24;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #b45309;
    --color-warning-800: #92400e;
    --color-warning-900: #78350f;
    --color-warning-950: #451a03;

    /* Error Colors */
    --color-error-50: #fef2f2;
    --color-error-100: #fee2e2;
    --color-error-200: #fecaca;
    --color-error-300: #fca5a5;
    --color-error-400: #f87171;
    --color-error-500: #ef4444;
    --color-error-600: #dc2626;
    --color-error-700: #b91c1c;
    --color-error-800: #991b1b;
    --color-error-900: #7f1d1d;
    --color-error-950: #450a0a;

    /* Typography */
    --font-sans: 'Inter', 'Roboto', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: 'Merriweather', ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
    --font-mono: 'JetBrains Mono', ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;

    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    --font-size-7xl: 4.5rem;
    --font-size-8xl: 6rem;
    --font-size-9xl: 8rem;

    /* Spacing Scale */
    --spacing-px: 1px;
    --spacing-0: 0px;
    --spacing-0_5: 0.125rem;
    --spacing-1: 0.25rem;
    --spacing-1_5: 0.375rem;
    --spacing-2: 0.5rem;
    --spacing-2_5: 0.625rem;
    --spacing-3: 0.75rem;
    --spacing-3_5: 0.875rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-7: 1.75rem;
    --spacing-8: 2rem;
    --spacing-9: 2.25rem;
    --spacing-10: 2.5rem;
    --spacing-11: 2.75rem;
    --spacing-12: 3rem;
    --spacing-14: 3.5rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    --spacing-28: 7rem;
    --spacing-32: 8rem;
    --spacing-36: 9rem;
    --spacing-40: 10rem;
    --spacing-44: 11rem;
    --spacing-48: 12rem;
    --spacing-52: 13rem;
    --spacing-56: 14rem;
    --spacing-60: 15rem;
    --spacing-64: 16rem;
    --spacing-72: 18rem;
    --spacing-80: 20rem;
    --spacing-96: 24rem;

    /* Border Radius */
    --radius-none: 0px;
    --radius-sm: 0.125rem;
    --radius-DEFAULT: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-DEFAULT: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    /* Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* Custom Component Classes */
.btn-eu-primary {
    background-color: var(--color-eu-blue);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.btn-eu-primary:hover {
    background-color: var(--color-eu-blue-dark);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

.btn-eu-secondary {
    background-color: var(--color-eu-gold);
    color: var(--color-eu-blue);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.btn-eu-secondary:hover {
    background-color: var(--color-eu-gold-dark);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

.btn-eu-outline {
    border: 2px solid var(--color-eu-blue);
    color: var(--color-eu-blue);
    background-color: transparent;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-eu-outline:hover {
    background-color: var(--color-eu-blue);
    color: white;
}

/* Card Components */
.card-eu {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    transition: box-shadow 0.3s ease;
    border: 1px solid rgb(243 244 246);
}

.card-eu:hover {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

.card-eu-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgb(243 244 246);
}

.card-eu-body {
    padding: 1.5rem;
}

.card-eu-footer {
    padding: 1.5rem;
    border-top: 1px solid rgb(243 244 246);
    background-color: rgb(249 250 251);
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

/* Country Flag Cards */
.country-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgb(243 244 246);
    cursor: pointer;
}

.country-card:hover {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    transform: scale(1.05);
}

.country-flag {
    width: 4rem;
    height: 3rem;
    object-fit: cover;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

/* Form Components */
.form-input-eu {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid rgb(209 213 219);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.form-input-eu:focus {
    outline: none;
    ring: 2px;
    ring-color: var(--color-eu-blue);
    border-color: var(--color-eu-blue);
}

.form-label-eu {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(55 65 81);
    margin-bottom: 0.5rem;
}

.form-error-eu {
    font-size: 0.875rem;
    color: var(--color-error-600);
    margin-top: 0.25rem;
}

/* Navigation Components */
.nav-link-eu {
    color: rgb(55 65 81);
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link-eu:hover {
    color: var(--color-eu-blue);
}

.nav-link-active-eu {
    color: var(--color-eu-blue);
    font-weight: 600;
}

/* Hero Section */
.hero-eu {
    background: linear-gradient(135deg, var(--color-eu-blue) 0%, var(--color-eu-blue-dark) 100%);
    color: white;
}

.hero-title-eu {
    font-size: clamp(2.25rem, 7vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
}

.hero-subtitle-eu {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    color: rgb(219 234 254);
    margin-top: 1rem;
}

/* Feature Sections */
.feature-icon-eu {
    width: 3rem;
    height: 3rem;
    background-color: var(--color-eu-blue);
    color: white;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.feature-title-eu {
    font-size: 1.25rem;
    font-weight: 600;
    color: rgb(17 24 39);
    margin-top: 1rem;
}

.feature-description-eu {
    color: rgb(75 85 99);
    margin-top: 0.5rem;
    line-height: 1.6;
}

/* Testimonial Components */
.testimonial-card-eu {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    padding: 1.5rem;
    position: relative;
    border: 1px solid rgb(243 244 246);
}

.testimonial-quote-eu {
    color: rgb(75 85 99);
    font-style: italic;
    line-height: 1.6;
}

.testimonial-author-eu {
    display: flex;
    align-items: center;
    margin-top: 1rem;
}

.testimonial-avatar-eu {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    object-fit: cover;
}

/* Status Badges */
.badge-success-eu {
    background-color: var(--color-success-100);
    color: var(--color-success-800);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-warning-eu {
    background-color: var(--color-warning-100);
    color: var(--color-warning-800);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-error-eu {
    background-color: var(--color-error-100);
    color: var(--color-error-800);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Loading States */
.loading-spinner-eu {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    border: 2px solid transparent;
    border-bottom-color: var(--color-eu-blue);
}

/* Mobile Menu */
.mobile-menu-eu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    background-color: white;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
}

.mobile-menu-eu.open {
    transform: translateX(0);
}

.mobile-menu-overlay-eu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
}

/* Utility Classes */
@layer utilities {
    /* EU Brand Colors */
    .text-eu-blue { color: var(--color-eu-blue); }
    .text-eu-blue-light { color: var(--color-eu-blue-light); }
    .text-eu-blue-dark { color: var(--color-eu-blue-dark); }
    .text-eu-gold { color: var(--color-eu-gold); }
    .text-eu-gold-light { color: var(--color-eu-gold-light); }
    .text-eu-gold-dark { color: var(--color-eu-gold-dark); }

    .bg-eu-blue { background-color: var(--color-eu-blue); }
    .bg-eu-blue-light { background-color: var(--color-eu-blue-light); }
    .bg-eu-blue-dark { background-color: var(--color-eu-blue-dark); }
    .bg-eu-gold { background-color: var(--color-eu-gold); }
    .bg-eu-gold-light { background-color: var(--color-eu-gold-light); }
    .bg-eu-gold-dark { background-color: var(--color-eu-gold-dark); }

    .border-eu-blue { border-color: var(--color-eu-blue); }
    .border-eu-gold { border-color: var(--color-eu-gold); }

    /* Gradient Utilities */
    .gradient-eu-primary {
        background: linear-gradient(135deg, var(--color-eu-blue) 0%, var(--color-eu-blue-dark) 100%);
    }

    .gradient-eu-secondary {
        background: linear-gradient(135deg, var(--color-eu-gold) 0%, var(--color-eu-gold-dark) 100%);
    }

    .gradient-eu-hero {
        background: linear-gradient(135deg, var(--color-eu-blue) 0%, var(--color-eu-blue-dark) 50%, var(--color-primary-900) 100%);
    }

    /* Animation Utilities */
    .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    .animate-slide-up {
        animation: slideUp 0.6s ease-out;
    }

    .animate-scale-in {
        animation: scaleIn 0.4s ease-out;
    }

    /* Responsive Text Utilities */
    .text-responsive-xs { font-size: clamp(0.75rem, 2vw, 0.875rem); }
    .text-responsive-sm { font-size: clamp(0.875rem, 2.5vw, 1rem); }
    .text-responsive-base { font-size: clamp(1rem, 3vw, 1.125rem); }
    .text-responsive-lg { font-size: clamp(1.125rem, 3.5vw, 1.25rem); }
    .text-responsive-xl { font-size: clamp(1.25rem, 4vw, 1.5rem); }
    .text-responsive-2xl { font-size: clamp(1.5rem, 5vw, 1.875rem); }
    .text-responsive-3xl { font-size: clamp(1.875rem, 6vw, 2.25rem); }
    .text-responsive-4xl { font-size: clamp(2.25rem, 7vw, 3rem); }

    /* Container Utilities */
    .container-eu {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .container-eu-narrow {
        @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .container-eu-wide {
        @apply max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    /* Spacing Utilities */
    .section-padding-eu {
        @apply py-16 md:py-24 lg:py-32;
    }

    .section-padding-sm-eu {
        @apply py-8 md:py-12 lg:py-16;
    }

    .section-padding-lg-eu {
        @apply py-24 md:py-32 lg:py-40;
    }
}

/* Keyframe Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Print Styles */
@media print {
    .no-print { display: none !important; }
    .print-only { display: block !important; }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }

    .card-eu {
        border: 1px solid #ccc;
        box-shadow: none;
    }
}
