@extends('layouts.auth')

@section('head')
<title>Application Details - Admin Dashboard</title>
<meta name="description" content="View application details in admin dashboard">
<meta name="robots" content="noindex, nofollow">
@endsection

@section('content')
<div class="min-h-screen bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('admin.applications') }}" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i> Back to Applications
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Application Details</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-700">{{ auth()->user()->name }}</span>
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt mr-1"></i> Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        @if(isset($application))
            <!-- Application Info -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Application Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Basic Information</h4>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Application Number</dt>
                                    <dd class="text-sm text-gray-900">{{ $application->application_number }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $application->first_name }} {{ $application->last_name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="text-sm text-gray-900">{{ $application->email }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                    <dd class="text-sm text-gray-900">{{ $application->phone ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Country</dt>
                                    <dd class="text-sm text-gray-900">{{ $application->country->name ?? 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Status & Payment</h4>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {{ ucfirst(str_replace('_', ' ', $application->application_status)) }}
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                                    <dd class="text-sm text-gray-900">{{ ucfirst($application->payment_status ?? 'pending') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                                    <dd class="text-sm text-gray-900">€{{ number_format($application->total_amount ?? 0, 2) }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Package Type</dt>
                                    <dd class="text-sm text-gray-900">{{ ucfirst($application->package_type ?? 'standard') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Processing Speed</dt>
                                    <dd class="text-sm text-gray-900">{{ ucfirst($application->processing_speed ?? 'standard') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Update -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Update Status</h3>
                    <form method="POST" action="{{ route('admin.applications.status', $application) }}">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">New Status</label>
                                <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                    <option value="draft" {{ $application->application_status == 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="submitted" {{ $application->application_status == 'submitted' ? 'selected' : '' }}>Submitted</option>
                                    <option value="under_review" {{ $application->application_status == 'under_review' ? 'selected' : '' }}>Under Review</option>
                                    <option value="documents_required" {{ $application->application_status == 'documents_required' ? 'selected' : '' }}>Documents Required</option>
                                    <option value="payment_pending" {{ $application->application_status == 'payment_pending' ? 'selected' : '' }}>Payment Pending</option>
                                    <option value="payment_confirmed" {{ $application->application_status == 'payment_confirmed' ? 'selected' : '' }}>Payment Confirmed</option>
                                    <option value="processing" {{ $application->application_status == 'processing' ? 'selected' : '' }}>Processing</option>
                                    <option value="ready_for_delivery" {{ $application->application_status == 'ready_for_delivery' ? 'selected' : '' }}>Ready for Delivery</option>
                                    <option value="delivered" {{ $application->application_status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                                    <option value="cancelled" {{ $application->application_status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    <option value="rejected" {{ $application->application_status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                </select>
                            </div>
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                                <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" placeholder="Add notes about this status change..."></textarea>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-2"></i>Update Status
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        @else
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <p class="text-gray-500">Application not found.</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
