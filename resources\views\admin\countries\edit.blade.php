@extends('layouts.auth')

@section('head')
<title>Edit Country - Admin Dashboard</title>
<meta name="description" content="Edit country in admin dashboard">
<meta name="robots" content="noindex, nofollow">
@endsection

@section('content')
<div class="min-h-screen bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('admin.countries') }}" class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-arrow-left"></i> Back to Countries
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Edit Country</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-700">{{ auth()->user()->name }}</span>
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt mr-1"></i> Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-3xl mx-auto py-6 sm:px-6 lg:px-8">
        @if(isset($country))
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Edit Country: {{ $country->name }}</h3>
                    
                    <form method="POST" action="{{ route('admin.countries.update', $country) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Country Name</label>
                                <input type="text" name="name" id="name" value="{{ old('name', $country->name) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="code" class="block text-sm font-medium text-gray-700">Country Code</label>
                                <input type="text" name="code" id="code" value="{{ old('code', $country->code) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('code') border-red-500 @enderror" maxlength="3">
                                @error('code')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="base_price" class="block text-sm font-medium text-gray-700">Base Price (€)</label>
                                <input type="number" name="base_price" id="base_price" value="{{ old('base_price', $country->base_price) }}" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('base_price') border-red-500 @enderror">
                                @error('base_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div>
                                <label for="processing_time" class="block text-sm font-medium text-gray-700">Processing Time</label>
                                <input type="text" name="processing_time" id="processing_time" value="{{ old('processing_time', $country->processing_time) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('processing_time') border-red-500 @enderror" placeholder="e.g., 5-7 days">
                                @error('processing_time')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $country->is_active) ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Active (Country is available for applications)
                                </label>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{{ route('admin.countries') }}" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-2"></i>Update Country
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        @else
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <p class="text-gray-500">Country not found.</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
