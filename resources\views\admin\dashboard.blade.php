@extends('layouts.admin')

@section('head')
<title>Admin Dashboard - EU Driving License Services</title>
<meta name="description" content="Admin dashboard for EU Driving License Services">
<meta name="robots" content="noindex, nofollow">
@endsection

@section('page-title', 'Dashboard')
@section('page-description', 'Overview of your EU Driving License Services')

@section('admin-content')
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Applications -->
    <div class="stat-card bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-alt text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Applications</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_applications'] ?? 0) }}</dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-green-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span class="font-medium">12%</span>
                    <span class="text-gray-500 ml-1">from last month</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Applications -->
    <div class="stat-card bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Pending Applications</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ number_format($stats['pending_applications'] ?? 0) }}</dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-yellow-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <span class="font-medium">Needs attention</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Processing -->
    <div class="stat-card bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cog text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Processing</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ number_format($stats['processing_applications'] ?? 0) }}</dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-blue-600">
                    <i class="fas fa-sync-alt mr-1"></i>
                    <span class="font-medium">In progress</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Revenue -->
    <div class="stat-card bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-euro-sign text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-2xl font-bold text-gray-900">€{{ number_format($stats['total_revenue'] ?? 0, 0) }}</dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm text-green-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span class="font-medium">€{{ number_format($stats['monthly_revenue'] ?? 0, 0) }}</span>
                    <span class="text-gray-500 ml-1">this month</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Applications Chart -->
    <div class="chart-container bg-white p-6 rounded-xl shadow-lg border border-gray-100">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Applications Overview</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-full">7D</button>
                <button class="px-3 py-1 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">30D</button>
                <button class="px-3 py-1 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">90D</button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="applicationsChart"></canvas>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="chart-container bg-white p-6 rounded-xl shadow-lg border border-gray-100">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Revenue Trends</h3>
            <div class="text-sm text-gray-500">This Year</div>
        </div>
        <div class="h-64">
            <canvas id="revenueChart"></canvas>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white shadow-lg rounded-xl mb-8 border border-gray-100">
    <div class="px-6 py-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ route('admin.applications') }}" class="group flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-all duration-200 border border-blue-200 hover:border-blue-300">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center group-hover:bg-blue-700 transition-colors">
                        <i class="fas fa-list text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-blue-900">View Applications</p>
                    <p class="text-xs text-blue-600">Manage all applications</p>
                </div>
            </a>

            <a href="{{ route('admin.countries') }}" class="group flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-all duration-200 border border-green-200 hover:border-green-300">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center group-hover:bg-green-700 transition-colors">
                        <i class="fas fa-globe text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-green-900">Manage Countries</p>
                    <p class="text-xs text-green-600">Edit country settings</p>
                </div>
            </a>

            <a href="{{ route('admin.users') }}" class="group flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-all duration-200 border border-purple-200 hover:border-purple-300">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center group-hover:bg-purple-700 transition-colors">
                        <i class="fas fa-users text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-purple-900">View Users</p>
                    <p class="text-xs text-purple-600">User management</p>
                </div>
            </a>

            <a href="{{ route('admin.reports') }}" class="group flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-all duration-200 border border-orange-200 hover:border-orange-300">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center group-hover:bg-orange-700 transition-colors">
                        <i class="fas fa-chart-bar text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-orange-900">View Reports</p>
                    <p class="text-xs text-orange-600">Analytics & insights</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Recent Applications -->
<div class="bg-white shadow-lg rounded-xl border border-gray-100">
    <div class="px-6 py-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Recent Applications</h3>
            <a href="{{ route('admin.applications') }}" class="text-sm text-blue-600 hover:text-blue-800 font-medium">View all →</a>
        </div>

        @if(isset($recentApplications) && $recentApplications->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application #</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($recentApplications as $application)
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <a href="{{ route('admin.applications.show', $application) }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $application->application_number }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-gray-600">{{ substr($application->first_name, 0, 1) }}{{ substr($application->last_name, 0, 1) }}</span>
                                    </div>
                                    {{ $application->first_name }} {{ $application->last_name }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="flex items-center">
                                    <span class="mr-2">🇪🇺</span>
                                    {{ $application->country->name ?? 'N/A' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $statusColors = [
                                        'submitted' => 'bg-blue-100 text-blue-800',
                                        'under_review' => 'bg-yellow-100 text-yellow-800',
                                        'processing' => 'bg-purple-100 text-purple-800',
                                        'completed' => 'bg-green-100 text-green-800',
                                        'delivered' => 'bg-green-100 text-green-800',
                                        'rejected' => 'bg-red-100 text-red-800',
                                    ];
                                    $statusClass = $statusColors[$application->application_status] ?? 'bg-gray-100 text-gray-800';
                                @endphp
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $statusClass }}">
                                    {{ ucfirst(str_replace('_', ' ', $application->application_status)) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $application->created_at->format('M d, Y') }}
                                <div class="text-xs text-gray-500">{{ $application->created_at->diffForHumans() }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('admin.applications.show', $application) }}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-alt text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-sm font-medium text-gray-900 mb-2">No applications yet</h3>
                <p class="text-sm text-gray-500">Applications will appear here once customers start submitting them.</p>
            </div>
        @endif
    </div>
</div>

<!-- Chart.js Scripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Applications Chart
    const applicationsCtx = document.getElementById('applicationsChart').getContext('2d');
    new Chart(applicationsCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Applications',
                data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'bar',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Revenue (€)',
                data: [3600, 5700, 4500, 7500, 6600, 9000, 8400, 10500, 9600, 12000, 11400, 13500],
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>
@endsection
