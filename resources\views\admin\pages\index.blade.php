@extends('layouts.admin')

@section('title', 'Pages Management')

@section('content')
<div class="container-eu">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-responsive-3xl font-bold text-gray-900">Pages Management</h1>
            <p class="text-gray-600 mt-2">Manage your website pages and content</p>
        </div>
        <a href="{{ route('admin.pages.create') }}" class="btn-eu-primary">
            <i class="fas fa-plus mr-2"></i>Create New Page
        </a>
    </div>

    @if(session('success'))
        <div class="bg-success-100 border border-success-400 text-success-700 px-4 py-3 rounded mb-6">
            {{ session('success') }}
        </div>
    @endif

    <div class="card-eu">
        <div class="card-eu-header">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold">All Pages</h2>
                <div class="flex gap-2">
                    <select class="form-input-eu" id="filterType">
                        <option value="">All Types</option>
                        @foreach(\App\Http\Controllers\Admin\PageController::getPageTypes() as $key => $label)
                            <option value="{{ $key }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    <select class="form-input-eu" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Country
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Updated
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($pages as $page)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $page->translate('en')->title ?? 'No title' }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        /{{ $page->translate('en')->slug ?? 'no-slug' }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ ucfirst($page->page_type) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {{ $page->country ? $page->country->name : 'Global' }}
                            </td>
                            <td class="px-6 py-4">
                                <button 
                                    onclick="togglePublished({{ $page->id }})"
                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $page->is_published ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    <span class="w-2 h-2 rounded-full {{ $page->is_published ? 'bg-green-400' : 'bg-gray-400' }} mr-1"></span>
                                    {{ $page->is_published ? 'Published' : 'Draft' }}
                                </button>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {{ $page->updated_at->format('M j, Y') }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium">
                                <div class="flex justify-end gap-2">
                                    <a href="{{ route('admin.pages.show', $page) }}" 
                                       class="text-blue-600 hover:text-blue-900">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.pages.edit', $page) }}" 
                                       class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="deletePage({{ $page->id }})" 
                                            class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <i class="fas fa-file-alt text-4xl mb-4"></i>
                                <p>No pages found. <a href="{{ route('admin.pages.create') }}" class="text-blue-600 hover:text-blue-800">Create your first page</a></p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($pages->hasPages())
            <div class="card-eu-footer">
                {{ $pages->links() }}
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Page</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to delete this page? This action cannot be undone.</p>
            <div class="flex justify-end gap-3">
                <button onclick="closeDeleteModal()" class="btn-eu-outline">Cancel</button>
                <button onclick="confirmDelete()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">Delete</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let deletePageId = null;

function togglePublished(pageId) {
    fetch(`/admin/pages/${pageId}/toggle-published`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}

function deletePage(pageId) {
    deletePageId = pageId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    deletePageId = null;
}

function confirmDelete() {
    if (deletePageId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/pages/${deletePageId}`;
        
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Filter functionality
document.getElementById('filterType').addEventListener('change', function() {
    // Implement filtering logic
});

document.getElementById('filterStatus').addEventListener('change', function() {
    // Implement filtering logic
});
</script>
@endpush
