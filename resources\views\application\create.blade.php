@extends('layouts.app')

@section('title', 'Apply for EU Driving License')
@section('meta_description', 'Apply for your EU driving license online. Fast, secure, and legal application process with guaranteed results.')

@section('content')
<!-- Progress Header -->
<section class="bg-eu-blue text-white py-6">
    <div class="container-eu">
        <div class="flex items-center justify-between">
            <h1 class="text-2xl font-bold">Apply for EU Driving License</h1>
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 bg-eu-gold text-eu-blue rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <span class="text-sm">Application</span>
                </div>
                <div class="w-8 h-0.5 bg-blue-300"></div>
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 bg-blue-300 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <span class="text-sm text-blue-200">Review</span>
                </div>
                <div class="w-8 h-0.5 bg-blue-300"></div>
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 bg-blue-300 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <span class="text-sm text-blue-200">Payment</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Application Form -->
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu max-w-4xl mx-auto">
        
        @if($errors->any())
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('application.store') }}" method="POST" id="applicationForm" class="space-y-8">
            @csrf
            
            <!-- Country Selection -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">1. Select Country</h2>
                    <p class="text-gray-600 mt-1">Choose the country for your driving license</p>
                </div>
                <div class="card-eu-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($countries as $countryOption)
                            <label class="relative cursor-pointer">
                                <input type="radio" 
                                       name="country_id" 
                                       value="{{ $countryOption->id }}" 
                                       class="sr-only peer"
                                       {{ ($country && $country->id === $countryOption->id) ? 'checked' : '' }}
                                       onchange="updatePricing()">
                                <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center gap-3">
                                        <img src="{{ $countryOption->flag_url }}" 
                                             alt="{{ $countryOption->name }}" 
                                             class="w-8 h-6 object-cover rounded">
                                        <div>
                                            <div class="font-medium text-gray-900">{{ $countryOption->name }}</div>
                                            <div class="text-sm text-gray-600">From €{{ number_format($countryOption->base_price, 0) }}</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">2. Personal Information</h2>
                    <p class="text-gray-600 mt-1">Provide your personal details as they appear on your passport</p>
                </div>
                <div class="card-eu-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label-eu">First Name *</label>
                            <input type="text" name="first_name" value="{{ old('first_name') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Last Name *</label>
                            <input type="text" name="last_name" value="{{ old('last_name') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Email Address *</label>
                            <input type="email" name="email" value="{{ old('email') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Phone Number *</label>
                            <input type="tel" name="phone" value="{{ old('phone') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Date of Birth *</label>
                            <input type="date" name="date_of_birth" value="{{ old('date_of_birth') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Place of Birth *</label>
                            <input type="text" name="place_of_birth" value="{{ old('place_of_birth') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Nationality *</label>
                            <input type="text" name="nationality" value="{{ old('nationality') }}" 
                                   class="form-input-eu" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">3. Address Information</h2>
                    <p class="text-gray-600 mt-1">Your current residential address</p>
                </div>
                <div class="card-eu-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Address Line 1 *</label>
                            <input type="text" name="address_line_1" value="{{ old('address_line_1') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Address Line 2</label>
                            <input type="text" name="address_line_2" value="{{ old('address_line_2') }}" 
                                   class="form-input-eu">
                        </div>
                        <div>
                            <label class="form-label-eu">City *</label>
                            <input type="text" name="city" value="{{ old('city') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Postal Code *</label>
                            <input type="text" name="postal_code" value="{{ old('postal_code') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Country *</label>
                            <input type="text" name="country" value="{{ old('country') }}" 
                                   class="form-input-eu" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- License Information -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">4. License Information</h2>
                    <p class="text-gray-600 mt-1">Details about the driving license you want to obtain</p>
                </div>
                <div class="card-eu-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label-eu">License Category *</label>
                            <select name="license_category" class="form-input-eu" required>
                                <option value="">Select Category</option>
                                <option value="B" {{ old('license_category') === 'B' ? 'selected' : '' }}>Category B - Cars</option>
                                <option value="A1" {{ old('license_category') === 'A1' ? 'selected' : '' }}>Category A1 - Light Motorcycles</option>
                                <option value="A2" {{ old('license_category') === 'A2' ? 'selected' : '' }}>Category A2 - Medium Motorcycles</option>
                                <option value="A" {{ old('license_category') === 'A' ? 'selected' : '' }}>Category A - Heavy Motorcycles</option>
                                <option value="C1" {{ old('license_category') === 'C1' ? 'selected' : '' }}>Category C1 - Light Trucks</option>
                                <option value="C" {{ old('license_category') === 'C' ? 'selected' : '' }}>Category C - Heavy Trucks</option>
                                <option value="D1" {{ old('license_category') === 'D1' ? 'selected' : '' }}>Category D1 - Minibuses</option>
                                <option value="D" {{ old('license_category') === 'D' ? 'selected' : '' }}>Category D - Buses</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label-eu">Previous License Number</label>
                            <input type="text" name="previous_license_number" value="{{ old('previous_license_number') }}" 
                                   class="form-input-eu">
                        </div>
                        <div>
                            <label class="form-label-eu">Previous License Country</label>
                            <input type="text" name="previous_license_country" value="{{ old('previous_license_country') }}" 
                                   class="form-input-eu">
                        </div>
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Medical Conditions</label>
                            <textarea name="medical_conditions" rows="3" class="form-input-eu" 
                                      placeholder="Please list any medical conditions that may affect driving">{{ old('medical_conditions') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">5. Emergency Contact</h2>
                    <p class="text-gray-600 mt-1">Someone we can contact in case of emergency</p>
                </div>
                <div class="card-eu-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="form-label-eu">Emergency Contact Name *</label>
                            <input type="text" name="emergency_contact_name" value="{{ old('emergency_contact_name') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Emergency Contact Phone *</label>
                            <input type="tel" name="emergency_contact_phone" value="{{ old('emergency_contact_phone') }}" 
                                   class="form-input-eu" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delivery Information -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">6. Delivery Information</h2>
                    <p class="text-gray-600 mt-1">Where should we deliver your driving license?</p>
                </div>
                <div class="card-eu-body">
                    <div class="mb-4">
                        <label class="flex items-center gap-2">
                            <input type="checkbox" id="sameAsAddress" onchange="copyAddress()">
                            <span class="text-sm text-gray-600">Same as residential address</span>
                        </label>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Delivery Address Line 1 *</label>
                            <input type="text" name="delivery_address_line_1" value="{{ old('delivery_address_line_1') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Delivery Address Line 2</label>
                            <input type="text" name="delivery_address_line_2" value="{{ old('delivery_address_line_2') }}" 
                                   class="form-input-eu">
                        </div>
                        <div>
                            <label class="form-label-eu">Delivery City *</label>
                            <input type="text" name="delivery_city" value="{{ old('delivery_city') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div>
                            <label class="form-label-eu">Delivery Postal Code *</label>
                            <input type="text" name="delivery_postal_code" value="{{ old('delivery_postal_code') }}" 
                                   class="form-input-eu" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="form-label-eu">Delivery Country *</label>
                            <input type="text" name="delivery_country" value="{{ old('delivery_country') }}" 
                                   class="form-input-eu" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Selection -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">7. Package Selection</h2>
                    <p class="text-gray-600 mt-1">Choose your service level and processing speed</p>
                </div>
                <div class="card-eu-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <label class="relative cursor-pointer">
                            <input type="radio" name="package_type" value="standard" class="sr-only peer" 
                                   {{ old('package_type', 'standard') === 'standard' ? 'checked' : '' }}
                                   onchange="updatePricing()">
                            <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50">
                                <h3 class="font-semibold text-gray-900">Standard</h3>
                                <p class="text-sm text-gray-600 mt-1">Basic service package</p>
                                <p class="text-lg font-bold text-eu-blue mt-2">Base Price</p>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="package_type" value="premium" class="sr-only peer"
                                   {{ old('package_type') === 'premium' ? 'checked' : '' }}
                                   onchange="updatePricing()">
                            <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50">
                                <h3 class="font-semibold text-gray-900">Premium</h3>
                                <p class="text-sm text-gray-600 mt-1">Enhanced service with priority support</p>
                                <p class="text-lg font-bold text-eu-blue mt-2">+50%</p>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="package_type" value="vip" class="sr-only peer"
                                   {{ old('package_type') === 'vip' ? 'checked' : '' }}
                                   onchange="updatePricing()">
                            <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50">
                                <h3 class="font-semibold text-gray-900">VIP</h3>
                                <p class="text-sm text-gray-600 mt-1">Ultimate service with dedicated manager</p>
                                <p class="text-lg font-bold text-eu-blue mt-2">+100%</p>
                            </div>
                        </label>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <label class="relative cursor-pointer">
                            <input type="radio" name="processing_speed" value="standard" class="sr-only peer"
                                   {{ old('processing_speed', 'standard') === 'standard' ? 'checked' : '' }}
                                   onchange="updatePricing()">
                            <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50">
                                <h3 class="font-semibold text-gray-900">Standard Speed</h3>
                                <p class="text-sm text-gray-600 mt-1">7-10 business days</p>
                                <p class="text-lg font-bold text-eu-blue mt-2">Included</p>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="processing_speed" value="express" class="sr-only peer"
                                   {{ old('processing_speed') === 'express' ? 'checked' : '' }}
                                   onchange="updatePricing()">
                            <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50">
                                <h3 class="font-semibold text-gray-900">Express Speed</h3>
                                <p class="text-sm text-gray-600 mt-1">3-5 business days</p>
                                <p class="text-lg font-bold text-eu-blue mt-2">+€200</p>
                            </div>
                        </label>
                        <label class="relative cursor-pointer">
                            <input type="radio" name="processing_speed" value="urgent" class="sr-only peer"
                                   {{ old('processing_speed') === 'urgent' ? 'checked' : '' }}
                                   onchange="updatePricing()">
                            <div class="border-2 border-gray-200 rounded-lg p-4 peer-checked:border-eu-blue peer-checked:bg-blue-50">
                                <h3 class="font-semibold text-gray-900">Urgent Speed</h3>
                                <p class="text-sm text-gray-600 mt-1">1-2 business days</p>
                                <p class="text-lg font-bold text-eu-blue mt-2">+€400</p>
                            </div>
                        </label>
                    </div>
                    
                    <div class="mt-6">
                        <label class="form-label-eu">Special Requirements</label>
                        <textarea name="special_requirements" rows="3" class="form-input-eu" 
                                  placeholder="Any special requirements or notes for your application">{{ old('special_requirements') }}</textarea>
                    </div>
                </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="card-eu">
                <div class="card-eu-header">
                    <h2 class="text-xl font-semibold text-gray-900">8. Terms and Conditions</h2>
                    <p class="text-gray-600 mt-1">Please read and accept our terms</p>
                </div>
                <div class="card-eu-body">
                    <div class="space-y-4">
                        <label class="flex items-start gap-3">
                            <input type="checkbox" name="terms_accepted" value="1" class="mt-1" required>
                            <span class="text-sm text-gray-600">
                                I have read and agree to the 
                                <a href="{{ route('terms') }}" target="_blank" class="text-eu-blue hover:underline">Terms of Service</a>
                                and understand that this service assists with obtaining authentic driving licenses through legal channels.
                            </span>
                        </label>
                        <label class="flex items-start gap-3">
                            <input type="checkbox" name="privacy_accepted" value="1" class="mt-1" required>
                            <span class="text-sm text-gray-600">
                                I have read and agree to the 
                                <a href="{{ route('privacy') }}" target="_blank" class="text-eu-blue hover:underline">Privacy Policy</a>
                                and consent to the processing of my personal data as described.
                            </span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Price Summary -->
            <div class="card-eu border-2 border-eu-blue">
                <div class="card-eu-header bg-eu-blue text-white">
                    <h2 class="text-xl font-semibold">Price Summary</h2>
                </div>
                <div class="card-eu-body">
                    <div id="priceSummary" class="space-y-2">
                        <div class="flex justify-between">
                            <span>Base Price:</span>
                            <span id="basePrice">€0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Package Upgrade:</span>
                            <span id="packagePrice">€0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Processing Speed:</span>
                            <span id="speedPrice">€0</span>
                        </div>
                        <hr class="my-2">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total Amount:</span>
                            <span id="totalPrice" class="text-eu-blue">€0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" class="btn-eu-primary text-lg px-8 py-4">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Submit Application
                </button>
                <p class="text-sm text-gray-600 mt-2">
                    You will be able to review your application before final submission
                </p>
            </div>
        </form>
    </div>
</section>
@endsection

@push('scripts')
<script>
const countryPrices = @json($countries->pluck('base_price', 'id'));

function updatePricing() {
    const countryId = document.querySelector('input[name="country_id"]:checked')?.value;
    const packageType = document.querySelector('input[name="package_type"]:checked')?.value || 'standard';
    const processingSpeed = document.querySelector('input[name="processing_speed"]:checked')?.value || 'standard';
    
    if (!countryId) return;
    
    const basePrice = countryPrices[countryId] || 0;
    
    // Package multipliers
    const packageMultiplier = {
        'standard': 1.0,
        'premium': 1.5,
        'vip': 2.0
    }[packageType] || 1.0;
    
    // Speed additions
    const speedAddition = {
        'standard': 0,
        'express': 200,
        'urgent': 400
    }[processingSpeed] || 0;
    
    const packagePrice = (basePrice * packageMultiplier) - basePrice;
    const totalPrice = (basePrice * packageMultiplier) + speedAddition;
    
    document.getElementById('basePrice').textContent = '€' + basePrice.toLocaleString();
    document.getElementById('packagePrice').textContent = '€' + packagePrice.toLocaleString();
    document.getElementById('speedPrice').textContent = '€' + speedAddition.toLocaleString();
    document.getElementById('totalPrice').textContent = '€' + totalPrice.toLocaleString();
}

function copyAddress() {
    const checkbox = document.getElementById('sameAsAddress');
    if (checkbox.checked) {
        document.querySelector('input[name="delivery_address_line_1"]').value = document.querySelector('input[name="address_line_1"]').value;
        document.querySelector('input[name="delivery_address_line_2"]').value = document.querySelector('input[name="address_line_2"]').value;
        document.querySelector('input[name="delivery_city"]').value = document.querySelector('input[name="city"]').value;
        document.querySelector('input[name="delivery_postal_code"]').value = document.querySelector('input[name="postal_code"]').value;
        document.querySelector('input[name="delivery_country"]').value = document.querySelector('input[name="country"]').value;
    }
}

// Initialize pricing on page load
document.addEventListener('DOMContentLoaded', function() {
    updatePricing();
    
    // Add event listeners for real-time updates
    document.querySelectorAll('input[name="country_id"], input[name="package_type"], input[name="processing_speed"]').forEach(input => {
        input.addEventListener('change', updatePricing);
    });
});
</script>
@endpush
