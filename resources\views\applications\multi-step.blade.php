@extends('layouts.app')

@section('title', __('messages.apply.page_title'))
@section('meta_description', __('messages.apply.meta_description'))

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Title Area -->
        <div class="mb-8">
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ localized_route('home') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <i class="fas fa-home mr-2"></i>
                            {{ __('messages.nav.home') }}
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ __('messages.apply.page_title') }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ __('messages.apply.page_title') }}</h1>
                    <p class="mt-2 text-lg text-gray-600">{{ __('messages.apply.meta_description') }}</p>
                </div>
                <div class="hidden md:block">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-shield-alt text-green-500"></i>
                        <span>Secure & Encrypted</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                @for($i = 1; $i <= 4; $i++)
                    <div class="flex items-center {{ $i < 4 ? 'flex-1' : '' }}">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full {{ $step >= $i ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600' }} font-semibold">
                            {{ $i }}
                        </div>
                        @if($i < 4)
                            <div class="flex-1 h-1 mx-4 {{ $step > $i ? 'bg-blue-600' : 'bg-gray-300' }}"></div>
                        @endif
                    </div>
                @endfor
            </div>
            <div class="flex justify-between mt-2">
                <span class="text-sm {{ $step >= 1 ? 'text-blue-600 font-medium' : 'text-gray-500' }}">{{ __('messages.apply.step1_title') }}</span>
                <span class="text-sm {{ $step >= 2 ? 'text-blue-600 font-medium' : 'text-gray-500' }}">{{ __('messages.apply.step2_title') }}</span>
                <span class="text-sm {{ $step >= 3 ? 'text-blue-600 font-medium' : 'text-gray-500' }}">{{ __('messages.apply.step3_title') }}</span>
                <span class="text-sm {{ $step >= 4 ? 'text-blue-600 font-medium' : 'text-gray-500' }}">{{ __('messages.apply.step4_title') }}</span>
            </div>
        </div>

        <!-- Main Form Card -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-4 bg-blue-600 text-white">
                <h1 class="text-2xl font-bold">{{ $stepData['title'] }}</h1>
                <p class="text-blue-100 mt-1">{{ $stepData['description'] }}</p>
            </div>

            <div class="p-6">
                <form id="step-form" data-step="{{ $step }}" data-application-id="{{ $application->id }}">
                    @csrf

                    @if($step == 1)
                        @include('applications.steps.step1')
                    @elseif($step == 2)
                        @include('applications.steps.step2')
                    @elseif($step == 3)
                        @include('applications.steps.step3')
                    @elseif($step == 4)
                        @include('applications.steps.step4')
                    @endif

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                        @if($step > 1)
                            <a href="{{ route('applications.step', ['application' => $application, 'step' => $step - 1]) }}"
                               class="px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>{{ __('messages.apply.previous') }}
                            </a>
                        @else
                            <div></div>
                        @endif

                        @if($step < 4)
                            <button type="submit" id="next-btn"
                                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                                <span class="btn-text">{{ __('messages.apply.next') }}</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                                <div class="spinner hidden ml-2">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                </div>
                            </button>
                        @else
                            <a href="{{ route('applications.review', $application) }}"
                               class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-check mr-2"></i>{{ __('messages.apply.submit') }}
                            </a>
                        @endif
                    </div>
                </form>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">
                <i class="fas fa-info-circle mr-2"></i>Need Help?
            </h3>
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Contact Support</h4>
                    <p class="text-blue-700 text-sm">
                        <i class="fas fa-phone mr-2"></i>+****************<br>
                        <i class="fas fa-envelope mr-2"></i><EMAIL>
                    </p>
                </div>
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Live Chat</h4>
                    <p class="text-blue-700 text-sm">Available 24/7 for instant assistance</p>
                    <button class="mt-2 px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                        Start Chat
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="message-container" class="fixed top-4 right-4 z-50"></div>

@endsection

@push('scripts')
<script>
// DEBUGGING: Script execution starts
console.log('FileUploadManager script processing begins.');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded event triggered.');
    const form = document.getElementById('step-form');
    const nextBtn = document.getElementById('next-btn');

    console.log('Form element:', form);
    console.log('Next button element:', nextBtn);

    if (form && nextBtn) {
        console.log('Both form and button found, adding event listener');
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            console.log('Form submit event triggered!');

            // Show spinner
            const btnText = nextBtn.querySelector('.btn-text');
            const spinner = nextBtn.querySelector('.spinner');
            const arrow = nextBtn.querySelector('.fa-arrow-right');

            btnText.textContent = 'Saving...';
            spinner.classList.remove('hidden');
            arrow.classList.add('hidden');
            nextBtn.disabled = true;

            // Prepare form data
            const formData = new FormData(form);
            const step = form.dataset.step;
            const applicationId = form.dataset.applicationId;

            // Debug: Log form data
            console.log('Submitting step:', step);
            console.log('Application ID:', applicationId);
            console.log('Form data:', Object.fromEntries(formData));

            // Send AJAX request with locale prefix
            const locale = document.documentElement.lang || 'en';
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            console.log('CSRF token element:', csrfToken);
            console.log('CSRF token value:', csrfToken ? csrfToken.getAttribute('content') : 'NOT FOUND');

            fetch(`/${locale}/applications/${applicationId}/step/${step}`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showMessage('Step saved successfully!', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect_url;
                    }, 1000);
                } else {
                    showMessage(data.message || 'An error occurred', 'error');
                    displayErrors(data.errors);
                    resetButton();
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                showMessage('An error occurred. Please try again.', 'error');
                resetButton();
            });
        });

        // Also add direct click event as backup
        nextBtn.addEventListener('click', function(e) {
            console.log('Next button clicked directly');
            if (e.target.type === 'submit') {
                // Let the form submit event handle it
                return;
            }
            // If it's not a submit button, trigger form submission
            form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
        });
    } else {
        console.error('Form or Next button not found!');
        console.log('Available forms:', document.querySelectorAll('form'));
        console.log('Available buttons:', document.querySelectorAll('button'));
    }

    function resetButton() {
        const btnText = nextBtn.querySelector('.btn-text');
        const spinner = nextBtn.querySelector('.spinner');
        const arrow = nextBtn.querySelector('.fa-arrow-right');

        btnText.textContent = 'Next Step';
        spinner.classList.add('hidden');
        arrow.classList.remove('hidden');
        nextBtn.disabled = false;
    }

    function showMessage(message, type) {
        const container = document.getElementById('message-container');
        const alertClass = type === 'success' ? 'bg-green-500' : 'bg-red-500';

        const messageEl = document.createElement('div');
        messageEl.className = `${alertClass} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300`;
        messageEl.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(messageEl);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    function displayErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.error-message').forEach(el => el.remove());

        if (errors) {
            Object.keys(errors).forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (input) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                    errorDiv.textContent = errors[field][0];
                    input.parentNode.appendChild(errorDiv);
                    input.classList.add('border-red-500');
                }
            });
        }
    }
});


</script>
@endpush
