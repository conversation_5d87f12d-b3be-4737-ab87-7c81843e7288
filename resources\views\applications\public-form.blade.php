@extends('layouts.app')

@section('head')
<title>{{ $seoData['title'] }}</title>
<meta name="description" content="{{ $seoData['description'] }}">
<meta name="keywords" content="{{ $seoData['keywords'] }}">
<meta name="robots" content="index, follow">
<link rel="canonical" href="{{ $seoData['canonical'] }}">

<!-- Open Graph Tags -->
@foreach($seoData['og_tags'] as $property => $content)
<meta property="{{ $property }}" content="{{ $content }}">
@endforeach

<!-- Twitter Card Tags -->
@foreach($seoData['twitter_tags'] as $name => $content)
<meta name="{{ $name }}" content="{{ $content }}">
@endforeach

<!-- Hreflang Tags -->
@foreach($seoData['hreflang'] as $hreflang)
<link rel="alternate" hreflang="{{ $hreflang['hreflang'] }}" href="{{ $hreflang['href'] }}">
@endforeach

<!-- Schema.org Markup -->
<script type="application/ld+json">
{!! json_encode($seoData['schema'], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
</script>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="application-header bg-primary text-white py-5">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h1 class="display-4 mb-3">{{ __('messages.apply.title', ['country' => $country->name]) }}</h1>
                            <p class="lead mb-0">{{ __('messages.apply.subtitle') }}. {{ __('messages.apply.description') }}.</p>
                        </div>
                        <div class="col-lg-4 text-lg-end">
                            <div class="price-badge">
                                <span class="h3 mb-0">{{ __('messages.apply.starting_from') }} €{{ number_format($country->base_price, 0) }}</span>
                                <small class="d-block">{{ __('messages.apply.processing_time') }}: {{ $country->processing_days_min }}-{{ $country->processing_days_max }} {{ __('messages.apply.days') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Steps -->
            <div class="progress-steps bg-light py-4">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="steps-wrapper">
                                <div class="step active" data-step="1">
                                    <div class="step-number">1</div>
                                    <div class="step-title">{{ __('messages.apply.step_1') }}</div>
                                </div>
                                <div class="step" data-step="2">
                                    <div class="step-number">2</div>
                                    <div class="step-title">{{ __('messages.apply.step_2') }}</div>
                                </div>
                                <div class="step" data-step="3">
                                    <div class="step-number">3</div>
                                    <div class="step-title">{{ __('messages.apply.step_3') }}</div>
                                </div>
                                <div class="step" data-step="4">
                                    <div class="step-number">4</div>
                                    <div class="step-title">{{ __('messages.apply.step_4') }}</div>
                                </div>
                                <div class="step" data-step="5">
                                    <div class="step-number">5</div>
                                    <div class="step-title">{{ __('messages.apply.step_5') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Application Form -->
            <div class="application-form py-5">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card shadow-lg border-0">
                                <div class="card-header bg-white border-bottom-0 py-4">
                                    <h3 class="card-title mb-0">
                                        <i class="fas fa-user-circle text-primary me-2"></i>
                                        Step 1: {{ __('messages.apply.step_1') }}
                                    </h3>
                                    <p class="text-muted mb-0 mt-2">{{ __('messages.apply.personal_info_desc') }}</p>
                                </div>

                                <div class="card-body p-4">
                                    <form id="application-form" data-step="1" data-country="{{ $country->code }}">
                                        @csrf

                                        <!-- Personal Information -->
                                        <div class="step-content" id="step-1">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="first_name" class="form-label required">{{ __('messages.apply.first_name') }}</label>
                                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="last_name" class="form-label required">{{ __('messages.apply.last_name') }}</label>
                                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="date_of_birth" class="form-label required">{{ __('messages.apply.date_of_birth') }}</label>
                                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="place_of_birth" class="form-label required">{{ __('messages.apply.place_of_birth') }}</label>
                                                    <input type="text" class="form-control" id="place_of_birth" name="place_of_birth" required>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="nationality" class="form-label required">{{ __('messages.apply.nationality') }}</label>
                                                    <select class="form-select" id="nationality" name="nationality" required>
                                                        <option value="">{{ __('messages.apply.select_nationality') }}</option>
                                                        <option value="Irish">Irish</option>
                                                        <option value="British">British</option>
                                                        <option value="German">German</option>
                                                        <option value="French">French</option>
                                                        <option value="Spanish">Spanish</option>
                                                        <option value="Italian">Italian</option>
                                                        <option value="Dutch">Dutch</option>
                                                        <option value="Other">{{ __('messages.apply.other') }}</option>
                                                    </select>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="gender" class="form-label required">{{ __('messages.apply.gender') }}</label>
                                                    <select class="form-select" id="gender" name="gender" required>
                                                        <option value="">{{ __('messages.apply.select_gender') }}</option>
                                                        <option value="Male">{{ __('messages.apply.male') }}</option>
                                                        <option value="Female">{{ __('messages.apply.female') }}</option>
                                                        <option value="Other">{{ __('messages.apply.other') }}</option>
                                                    </select>
                                                    <div class="invalid-feedback"></div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Navigation Buttons -->
                                        <div class="form-navigation mt-4 pt-3 border-top">
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-outline-secondary" id="prev-btn" style="display: none;">
                                                    <i class="fas fa-arrow-left me-2"></i>{{ __('messages.apply.previous') }}
                                                </button>
                                                <div class="ms-auto">
                                                    <button type="button" class="btn btn-primary" id="next-btn">
                                                        {{ __('messages.apply.next') }} <i class="fas fa-arrow-right ms-2"></i>
                                                    </button>
                                                    <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                                                        <i class="fas fa-paper-plane me-2"></i>{{ __('messages.apply.submit_application') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Processing your application...</h5>
                <p class="text-muted mb-0">Please wait while we save your information.</p>
            </div>
        </div>
    </div>
</div>

<style>
.application-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.price-badge {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.steps-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.steps-wrapper::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #007bff;
    color: white;
}

.step.completed .step-number {
    background: #28a745;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    text-align: center;
    color: #6c757d;
    font-weight: 500;
}

.step.active .step-title {
    color: #007bff;
    font-weight: 600;
}

.form-label.required::after {
    content: ' *';
    color: #dc3545;
}

.card {
    border-radius: 15px;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

@media (max-width: 768px) {
    .steps-wrapper {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .step {
        flex: 1;
        min-width: calc(50% - 0.5rem);
    }

    .steps-wrapper::before {
        display: none;
    }
}

.upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-area i {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-area.dragover {
    border-color: #007bff;
    background: #e3f2fd;
}
</style>

<script src="{{ asset('js/application-form.js') }}"></script>
@endsection
