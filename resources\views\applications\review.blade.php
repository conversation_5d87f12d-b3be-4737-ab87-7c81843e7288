@extends('layouts.app')

@section('title', 'Review Application - ' . $application->application_number)
@section('meta_description', 'Review and submit your EU driving license application')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
            <div class="px-6 py-4 bg-green-600 text-white">
                <h1 class="text-2xl font-bold">
                    <i class="fas fa-check-circle mr-2"></i>
                    Application Complete
                </h1>
                <p class="text-green-100 mt-1">Review your application before final submission</p>
            </div>

            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Application #{{ $application->application_number }}</h2>
                        <p class="text-gray-600">{{ $application->country->name ?? 'EU' }} Driving License Application</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            {{ ucfirst(str_replace('_', ' ', $application->application_status)) }}
                        </span>
                        <p class="text-sm text-gray-500 mt-1">Created {{ $application->created_at->format('M j, Y') }}</p>
                    </div>
                </div>

                <!-- Progress Indicator -->
                <div class="mb-8">
                    <div class="flex items-center justify-between">
                        @for($i = 1; $i <= 4; $i++)
                            <div class="flex items-center {{ $i < 4 ? 'flex-1' : '' }}">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-600 text-white font-semibold">
                                    <i class="fas fa-check"></i>
                                </div>
                                @if($i < 4)
                                    <div class="flex-1 h-1 mx-4 bg-green-600"></div>
                                @endif
                            </div>
                        @endfor
                    </div>
                    <div class="flex justify-between mt-2">
                        <span class="text-sm text-green-600 font-medium">Personal Info</span>
                        <span class="text-sm text-green-600 font-medium">Documents</span>
                        <span class="text-sm text-green-600 font-medium">Additional Info</span>
                        <span class="text-sm text-green-600 font-medium">Complete</span>
                    </div>
                </div>

                <!-- Application Summary -->
                @include('applications.steps.step4')

                <!-- Action Buttons -->
                <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                    <a href="{{ route('applications.step', ['application' => $application, 'step' => 4]) }}"
                       class="px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Review
                    </a>

                    <form action="{{ route('applications.submit', $application) }}" method="POST" id="submit-form">
                        @csrf
                        <button type="submit" id="submit-btn"
                                class="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center">
                            <span class="btn-text">Submit Application</span>
                            <i class="fas fa-paper-plane ml-2"></i>
                            <div class="spinner hidden ml-2">
                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            </div>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- What Happens Next -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-route text-blue-600 mr-2"></i>
                What Happens Next?
            </h3>

            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">1</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Application Review</h4>
                        <p class="text-gray-600 text-sm">Our team will review your application and documents within 24-48 hours.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">2</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Document Verification</h4>
                        <p class="text-gray-600 text-sm">We'll verify your documents with relevant authorities and may request additional information if needed.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">3</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Processing & Production</h4>
                        <p class="text-gray-600 text-sm">Once approved, your license will be processed and produced according to your selected package.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-blue-600 font-semibold text-sm">4</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Delivery</h4>
                        <p class="text-gray-600 text-sm">Your license will be securely delivered to your specified address with tracking information.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">
                <i class="fas fa-headset mr-2"></i>Need Assistance?
            </h3>
            <div class="grid md:grid-cols-3 gap-4">
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Email Support</h4>
                    <p class="text-blue-700 text-sm">
                        <i class="fas fa-envelope mr-2"></i><EMAIL>
                    </p>
                </div>
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Phone Support</h4>
                    <p class="text-blue-700 text-sm">
                        <i class="fas fa-phone mr-2"></i>+****************
                    </p>
                </div>
                <div>
                    <h4 class="font-medium text-blue-800 mb-2">Live Chat</h4>
                    <button class="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                        Start Chat
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="message-container" class="fixed top-4 right-4 z-50"></div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('submit-form');
    const submitBtn = document.getElementById('submit-btn');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Show spinner
            const btnText = submitBtn.querySelector('.btn-text');
            const spinner = submitBtn.querySelector('.spinner');
            const icon = submitBtn.querySelector('.fa-paper-plane');

            btnText.textContent = 'Submitting...';
            spinner.classList.remove('hidden');
            icon.classList.add('hidden');
            submitBtn.disabled = true;
        });
    }

    // Documents are optional - users can proceed without uploading files
    const uploadedDocs = @json(array_keys($application->documents_uploaded ?? []));

    if (uploadedDocs.length === 0) {
        showMessage('Note: You can upload documents later if needed.', 'info');
    }

    function showMessage(message, type) {
        const container = document.getElementById('message-container');
        let alertClass = 'bg-blue-500';

        if (type === 'success') alertClass = 'bg-green-500';
        else if (type === 'error') alertClass = 'bg-red-500';
        else if (type === 'warning') alertClass = 'bg-yellow-500';
        else if (type === 'info') alertClass = 'bg-blue-500';

        const messageEl = document.createElement('div');
        messageEl.className = `${alertClass} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300`;
        messageEl.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(messageEl);

        // Auto remove after 8 seconds for warnings
        if (type === 'warning') {
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 8000);
        }
    }
});
</script>
@endpush
