<!-- Step 1: Personal Information -->
<div class="mb-8">
    <h3 class="text-lg font-semibold text-gray-900 mb-6">{{ __('messages.apply.step1_title') }}</h3>
    <p class="text-gray-600 mb-6">{{ __('messages.apply.personal_info_desc') }}</p>
</div>

<div class="grid md:grid-cols-2 gap-6">
    <!-- First Name -->
    <div>
        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.first_name') }} <span class="text-red-500">*</span>
        </label>
        <input type="text"
               id="first_name"
               name="first_name"
               value="{{ old('first_name', $application->first_name) }}"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
               placeholder="Enter your first name"
               required>
    </div>

    <!-- Last Name -->
    <div>
        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.last_name') }} <span class="text-red-500">*</span>
        </label>
        <input type="text"
               id="last_name"
               name="last_name"
               value="{{ old('last_name', $application->last_name) }}"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
               placeholder="Enter your last name"
               required>
    </div>

    <!-- Email -->
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.email') }} <span class="text-red-500">*</span>
        </label>
        <input type="email"
               id="email"
               name="email"
               value="{{ old('email', $application->email) }}"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
               placeholder="Enter your email address"
               required>
    </div>

    <!-- Phone -->
    <div>
        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.phone') }} <span class="text-red-500">*</span>
        </label>
        <input type="tel"
               id="phone"
               name="phone"
               value="{{ old('phone', $application->phone) }}"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
               placeholder="Enter your phone number"
               required>
    </div>

    <!-- Date of Birth -->
    <div>
        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.date_of_birth') }} <span class="text-red-500">*</span>
        </label>
        <input type="date"
               id="date_of_birth"
               name="date_of_birth"
               value="{{ old('date_of_birth', $application->date_of_birth ? $application->date_of_birth->format('Y-m-d') : '') }}"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
               required>
    </div>

    <!-- Place of Birth -->
    <div>
        <label for="place_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.place_of_birth') }} <span class="text-red-500">*</span>
        </label>
        <input type="text"
               id="place_of_birth"
               name="place_of_birth"
               value="{{ old('place_of_birth', $application->place_of_birth) }}"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
               placeholder="Enter your place of birth"
               required>
    </div>

    <!-- Nationality -->
    <div class="md:col-span-2">
        <label for="nationality" class="block text-sm font-medium text-gray-700 mb-2">
            {{ __('messages.apply.nationality') }} <span class="text-red-500">*</span>
        </label>
        <select id="nationality"
                name="nationality"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                required>
            <option value="">Select your nationality</option>
            <option value="Austrian" {{ old('nationality', $application->nationality) == 'Austrian' ? 'selected' : '' }}>Austrian</option>
            <option value="Belgian" {{ old('nationality', $application->nationality) == 'Belgian' ? 'selected' : '' }}>Belgian</option>
            <option value="Bulgarian" {{ old('nationality', $application->nationality) == 'Bulgarian' ? 'selected' : '' }}>Bulgarian</option>
            <option value="Croatian" {{ old('nationality', $application->nationality) == 'Croatian' ? 'selected' : '' }}>Croatian</option>
            <option value="Cypriot" {{ old('nationality', $application->nationality) == 'Cypriot' ? 'selected' : '' }}>Cypriot</option>
            <option value="Czech" {{ old('nationality', $application->nationality) == 'Czech' ? 'selected' : '' }}>Czech</option>
            <option value="Danish" {{ old('nationality', $application->nationality) == 'Danish' ? 'selected' : '' }}>Danish</option>
            <option value="Dutch" {{ old('nationality', $application->nationality) == 'Dutch' ? 'selected' : '' }}>Dutch</option>
            <option value="Estonian" {{ old('nationality', $application->nationality) == 'Estonian' ? 'selected' : '' }}>Estonian</option>
            <option value="Finnish" {{ old('nationality', $application->nationality) == 'Finnish' ? 'selected' : '' }}>Finnish</option>
            <option value="French" {{ old('nationality', $application->nationality) == 'French' ? 'selected' : '' }}>French</option>
            <option value="German" {{ old('nationality', $application->nationality) == 'German' ? 'selected' : '' }}>German</option>
            <option value="Greek" {{ old('nationality', $application->nationality) == 'Greek' ? 'selected' : '' }}>Greek</option>
            <option value="Hungarian" {{ old('nationality', $application->nationality) == 'Hungarian' ? 'selected' : '' }}>Hungarian</option>
            <option value="Irish" {{ old('nationality', $application->nationality) == 'Irish' ? 'selected' : '' }}>Irish</option>
            <option value="Italian" {{ old('nationality', $application->nationality) == 'Italian' ? 'selected' : '' }}>Italian</option>
            <option value="Latvian" {{ old('nationality', $application->nationality) == 'Latvian' ? 'selected' : '' }}>Latvian</option>
            <option value="Lithuanian" {{ old('nationality', $application->nationality) == 'Lithuanian' ? 'selected' : '' }}>Lithuanian</option>
            <option value="Luxembourgish" {{ old('nationality', $application->nationality) == 'Luxembourgish' ? 'selected' : '' }}>Luxembourgish</option>
            <option value="Maltese" {{ old('nationality', $application->nationality) == 'Maltese' ? 'selected' : '' }}>Maltese</option>
            <option value="Polish" {{ old('nationality', $application->nationality) == 'Polish' ? 'selected' : '' }}>Polish</option>
            <option value="Portuguese" {{ old('nationality', $application->nationality) == 'Portuguese' ? 'selected' : '' }}>Portuguese</option>
            <option value="Romanian" {{ old('nationality', $application->nationality) == 'Romanian' ? 'selected' : '' }}>Romanian</option>
            <option value="Slovak" {{ old('nationality', $application->nationality) == 'Slovak' ? 'selected' : '' }}>Slovak</option>
            <option value="Slovenian" {{ old('nationality', $application->nationality) == 'Slovenian' ? 'selected' : '' }}>Slovenian</option>
            <option value="Spanish" {{ old('nationality', $application->nationality) == 'Spanish' ? 'selected' : '' }}>Spanish</option>
            <option value="Swedish" {{ old('nationality', $application->nationality) == 'Swedish' ? 'selected' : '' }}>Swedish</option>
            <option value="Other" {{ old('nationality', $application->nationality) == 'Other' ? 'selected' : '' }}>Other</option>
        </select>
    </div>
</div>

<!-- Existing License Section -->
<div class="mt-8 border-t border-gray-200 pt-8">
    <h3 class="text-lg font-semibold text-gray-900 mb-6">{{ __('messages.apply.existing_license') }}</h3>

    <div class="space-y-4">
        <!-- Has Existing License Checkbox -->
        <div>
            <label class="flex items-center">
                <input type="checkbox"
                       id="has_existing_license"
                       name="has_existing_license"
                       value="1"
                       {{ old('has_existing_license', $application->previous_license_number ? '1' : '0') == '1' ? 'checked' : '' }}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       onchange="toggleLicenseFields(this.checked)">
                <span class="ml-2 text-sm text-gray-700">{{ __('messages.apply.existing_license_yes') }}</span>
            </label>
        </div>

        <!-- License Details (Hidden by default) -->
        <div id="license-details" class="grid md:grid-cols-2 gap-6" style="display: {{ old('has_existing_license', $application->previous_license_number ? '1' : '0') == '1' ? 'grid' : 'none' }};">
            <!-- License Number -->
            <div>
                <label for="previous_license_number" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ __('messages.apply.license_number') }}
                </label>
                <input type="text"
                       id="previous_license_number"
                       name="previous_license_number"
                       value="{{ old('previous_license_number', $application->previous_license_number) }}"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                       placeholder="Enter your license number">
            </div>

            <!-- License Country -->
            <div>
                <label for="previous_license_country" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ __('messages.apply.license_country') }}
                </label>
                <select id="previous_license_country"
                        name="previous_license_country"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    <option value="">Select issuing country</option>
                    <option value="AT" {{ old('previous_license_country', $application->previous_license_country) == 'AT' ? 'selected' : '' }}>Austria</option>
                    <option value="BE" {{ old('previous_license_country', $application->previous_license_country) == 'BE' ? 'selected' : '' }}>Belgium</option>
                    <option value="BG" {{ old('previous_license_country', $application->previous_license_country) == 'BG' ? 'selected' : '' }}>Bulgaria</option>
                    <option value="HR" {{ old('previous_license_country', $application->previous_license_country) == 'HR' ? 'selected' : '' }}>Croatia</option>
                    <option value="CY" {{ old('previous_license_country', $application->previous_license_country) == 'CY' ? 'selected' : '' }}>Cyprus</option>
                    <option value="CZ" {{ old('previous_license_country', $application->previous_license_country) == 'CZ' ? 'selected' : '' }}>Czech Republic</option>
                    <option value="DK" {{ old('previous_license_country', $application->previous_license_country) == 'DK' ? 'selected' : '' }}>Denmark</option>
                    <option value="EE" {{ old('previous_license_country', $application->previous_license_country) == 'EE' ? 'selected' : '' }}>Estonia</option>
                    <option value="FI" {{ old('previous_license_country', $application->previous_license_country) == 'FI' ? 'selected' : '' }}>Finland</option>
                    <option value="FR" {{ old('previous_license_country', $application->previous_license_country) == 'FR' ? 'selected' : '' }}>France</option>
                    <option value="DE" {{ old('previous_license_country', $application->previous_license_country) == 'DE' ? 'selected' : '' }}>Germany</option>
                    <option value="GR" {{ old('previous_license_country', $application->previous_license_country) == 'GR' ? 'selected' : '' }}>Greece</option>
                    <option value="HU" {{ old('previous_license_country', $application->previous_license_country) == 'HU' ? 'selected' : '' }}>Hungary</option>
                    <option value="IE" {{ old('previous_license_country', $application->previous_license_country) == 'IE' ? 'selected' : '' }}>Ireland</option>
                    <option value="IT" {{ old('previous_license_country', $application->previous_license_country) == 'IT' ? 'selected' : '' }}>Italy</option>
                    <option value="LV" {{ old('previous_license_country', $application->previous_license_country) == 'LV' ? 'selected' : '' }}>Latvia</option>
                    <option value="LT" {{ old('previous_license_country', $application->previous_license_country) == 'LT' ? 'selected' : '' }}>Lithuania</option>
                    <option value="LU" {{ old('previous_license_country', $application->previous_license_country) == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                    <option value="MT" {{ old('previous_license_country', $application->previous_license_country) == 'MT' ? 'selected' : '' }}>Malta</option>
                    <option value="NL" {{ old('previous_license_country', $application->previous_license_country) == 'NL' ? 'selected' : '' }}>Netherlands</option>
                    <option value="PL" {{ old('previous_license_country', $application->previous_license_country) == 'PL' ? 'selected' : '' }}>Poland</option>
                    <option value="PT" {{ old('previous_license_country', $application->previous_license_country) == 'PT' ? 'selected' : '' }}>Portugal</option>
                    <option value="RO" {{ old('previous_license_country', $application->previous_license_country) == 'RO' ? 'selected' : '' }}>Romania</option>
                    <option value="SK" {{ old('previous_license_country', $application->previous_license_country) == 'SK' ? 'selected' : '' }}>Slovakia</option>
                    <option value="SI" {{ old('previous_license_country', $application->previous_license_country) == 'SI' ? 'selected' : '' }}>Slovenia</option>
                    <option value="ES" {{ old('previous_license_country', $application->previous_license_country) == 'ES' ? 'selected' : '' }}>Spain</option>
                    <option value="SE" {{ old('previous_license_country', $application->previous_license_country) == 'SE' ? 'selected' : '' }}>Sweden</option>
                    <option value="OTHER" {{ old('previous_license_country', $application->previous_license_country) == 'OTHER' ? 'selected' : '' }}>Other</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Information Notice -->
<div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-500 mt-1"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Important Information</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>All information must match your official identification documents</li>
                    <li>Ensure your email address is correct as we'll send updates there</li>
                    <li>Your phone number will be used for verification purposes</li>
                    <li>Date of birth must be accurate for age verification</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleLicenseFields(show) {
    const licenseDetails = document.getElementById('license-details');
    const licenseNumber = document.getElementById('previous_license_number');
    const licenseCountry = document.getElementById('previous_license_country');

    if (show) {
        licenseDetails.style.display = 'grid';
        licenseNumber.setAttribute('required', 'required');
        licenseCountry.setAttribute('required', 'required');
    } else {
        licenseDetails.style.display = 'none';
        licenseNumber.removeAttribute('required');
        licenseCountry.removeAttribute('required');
        licenseNumber.value = '';
        licenseCountry.value = '';
    }
}

// Initialize on page load - use a slight delay to ensure other scripts load first
setTimeout(function() {
    const hasLicenseCheckbox = document.getElementById('has_existing_license');
    if (hasLicenseCheckbox) {
        toggleLicenseFields(hasLicenseCheckbox.checked);

        // Store license status in sessionStorage for step 2
        hasLicenseCheckbox.addEventListener('change', function() {
            sessionStorage.setItem('hasExistingLicense', this.checked ? '1' : '0');
        });

        // Set initial value in sessionStorage
        sessionStorage.setItem('hasExistingLicense', hasLicenseCheckbox.checked ? '1' : '0');
    }
}, 100);
</script>
@endpush