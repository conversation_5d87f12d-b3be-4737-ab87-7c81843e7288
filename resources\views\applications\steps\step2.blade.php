<!-- Step 2: Document Uploads -->
<div class="space-y-8">
    <!-- Upload Instructions -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-500 mt-1"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">{{ __('messages.apply.upload_instruction') }}</h3>
            </div>
        </div>
    </div>

    <!-- Signature Upload -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-signature text-blue-600 mr-2"></i>
            {{ __('messages.apply.signature_upload') }} <span class="text-gray-500">(Optional)</span>
        </h3>
        <p class="text-gray-600 mb-4">
            Please sign your name on a clean white or neutral colored paper and take a clear photo.
        </p>

        <div class="upload-area" data-document-type="signature">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                <input type="file" id="signature-input" name="signature" class="hidden" accept="image/*,application/pdf">
                <div class="upload-content" id="signature-content">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700 mb-2">{{ __('messages.apply.click_upload') }}</p>
                    <p class="text-sm text-gray-500">{{ __('messages.apply.file_formats') }}</p>
                </div>
                <div class="upload-success hidden" id="signature-success">
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                    <p class="text-lg font-medium text-green-700 mb-2">Signature uploaded successfully</p>
                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Replace file</button>
                </div>
            </div>
        </div>

        <!-- Requirements -->
        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">Signature Requirements:</h4>
            <ul class="text-sm text-yellow-700 space-y-1">
                <li>• Use black or blue ink pen</li>
                <li>• Sign on clean white or neutral paper</li>
                <li>• Ensure good lighting and clear image</li>
                <li>• Signature should match your official documents</li>
            </ul>
        </div>
    </div>

    <!-- ID Photo Upload -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-id-card text-blue-600 mr-2"></i>
            {{ __('messages.apply.id_upload') }} <span class="text-gray-500">(Optional)</span>
        </h3>
        <p class="text-gray-600 mb-4">
            Upload a clear photo of your government-issued ID or passport.
        </p>

        <div class="upload-area" data-document-type="id_photo">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                <input type="file" id="id-input" name="id_photo" class="hidden" accept="image/*,application/pdf">
                <div class="upload-content" id="id-photo-content">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700 mb-2">{{ __('messages.apply.click_upload') }}</p>
                    <p class="text-sm text-gray-500">{{ __('messages.apply.file_formats') }}</p>
                </div>
                <div class="upload-success hidden" id="id-photo-success">
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                    <p class="text-lg font-medium text-green-700 mb-2">ID document uploaded successfully</p>
                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Replace file</button>
                </div>
            </div>
        </div>

        <!-- Requirements -->
        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">ID Photo Requirements:</h4>
            <ul class="text-sm text-yellow-700 space-y-1">
                <li>• Clear, high-resolution image</li>
                <li>• All text must be readable</li>
                <li>• No glare or shadows</li>
                <li>• Valid government-issued document</li>
            </ul>
        </div>
    </div>

    <!-- Passport Size Photo Upload -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-user text-blue-600 mr-2"></i>
            {{ __('messages.apply.passport_photo_upload') }} <span class="text-gray-500">(Optional)</span>
        </h3>
        <p class="text-gray-600 mb-4">
            Upload a recent passport-style photograph of yourself.
        </p>

        <div class="upload-area" data-document-type="passport_photo">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                <input type="file" id="passport-input" name="passport_photo" class="hidden" accept="image/*">
                <div class="upload-content" id="passport-photo-content">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700 mb-2">{{ __('messages.apply.click_upload') }}</p>
                    <p class="text-sm text-gray-500">PNG, JPG up to 5MB</p>
                </div>
                <div class="upload-success hidden" id="passport-photo-success">
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                    <p class="text-lg font-medium text-green-700 mb-2">Passport photo uploaded successfully</p>
                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Replace file</button>
                </div>
            </div>
        </div>

        <!-- Requirements -->
        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">Photo Requirements:</h4>
            <ul class="text-sm text-yellow-700 space-y-1">
                <li>• Recent photo (taken within last 6 months)</li>
                <li>• Plain white or light-colored background</li>
                <li>• Face clearly visible, looking straight at camera</li>
                <li>• No hats, sunglasses, or heavy shadows</li>
            </ul>
        </div>
    </div>

    <!-- Old License Upload (Conditional) -->
    <div id="old-license-section" class="space-y-6" style="display: none;">
        <!-- Old License Front -->
        <div class="border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-id-card-alt text-blue-600 mr-2"></i>
                {{ __('messages.apply.old_license_front') }}
            </h3>
            <p class="text-gray-600 mb-4">
                Upload a clear photo of the front side of your existing/expired license.
            </p>

            <div class="upload-area" data-document-type="old_license_front">
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                    <input type="file" id="old-front-input" name="old_license_front" class="hidden" accept="image/*,application/pdf">
                    <div class="upload-content" id="old-license-front-content">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-700 mb-2">{{ __('messages.apply.click_upload') }}</p>
                        <p class="text-sm text-gray-500">{{ __('messages.apply.file_formats') }}</p>
                    </div>
                    <div class="upload-success hidden" id="old-license-front-success">
                        <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                        <p class="text-lg font-medium text-green-700 mb-2">Old license front uploaded successfully</p>
                        <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Replace file</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Old License Back -->
        <div class="border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-id-card-alt text-blue-600 mr-2"></i>
                {{ __('messages.apply.old_license_back') }}
            </h3>
            <p class="text-gray-600 mb-4">
                Upload a clear photo of the back side of your existing/expired license.
            </p>

            <div class="upload-area" data-document-type="old_license_back">
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                    <input type="file" id="old-back-input" name="old_license_back" class="hidden" accept="image/*,application/pdf">
                    <div class="upload-content" id="old-license-back-content">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-700 mb-2">{{ __('messages.apply.click_upload') }}</p>
                        <p class="text-sm text-gray-500">{{ __('messages.apply.file_formats') }}</p>
                    </div>
                    <div class="upload-success hidden" id="old-license-back-success">
                        <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                        <p class="text-lg font-medium text-green-700 mb-2">Old license back uploaded successfully</p>
                        <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Replace file</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Security Notice -->
<div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <i class="fas fa-shield-alt text-green-500 mt-1"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Secure Document Handling</h3>
            <div class="mt-2 text-sm text-green-700">
                <p>All uploaded documents are encrypted and stored securely. We use bank-level security to protect your personal information and comply with GDPR regulations.</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Check if user has existing license and show/hide old license upload section
function checkExistingLicense() {
    // Check sessionStorage first (from step 1), then check current form
    let hasExistingLicense = sessionStorage.getItem('hasExistingLicense');

    // If not in sessionStorage, check current form
    if (!hasExistingLicense) {
        const hasLicense = document.querySelector('input[name="has_existing_license"]:checked');
        hasExistingLicense = hasLicense ? hasLicense.value : '0';
    }

    const oldLicenseSection = document.getElementById('old-license-section');

    if (hasExistingLicense === '1' && oldLicenseSection) {
        oldLicenseSection.style.display = 'block';

        // Make old license uploads required
        const oldFrontInput = document.getElementById('old-front-input');
        const oldBackInput = document.getElementById('old-back-input');
        if (oldFrontInput) oldFrontInput.setAttribute('required', 'required');
        if (oldBackInput) oldBackInput.setAttribute('required', 'required');
    } else if (oldLicenseSection) {
        oldLicenseSection.style.display = 'none';

        // Remove required attribute
        const oldFrontInput = document.getElementById('old-front-input');
        const oldBackInput = document.getElementById('old-back-input');
        if (oldFrontInput) {
            oldFrontInput.removeAttribute('required');
            oldFrontInput.value = '';
        }
        if (oldBackInput) {
            oldBackInput.removeAttribute('required');
            oldBackInput.value = '';
        }
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize existing license check
    checkExistingLicense();

    // Listen for changes from step 1 (if we're on the same page)
    document.addEventListener('change', function(e) {
        if (e.target.name === 'has_existing_license') {
            checkExistingLicense();
        }
    });
});
</script>
@endpush
