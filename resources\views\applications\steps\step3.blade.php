<!-- Step 3: License Details -->
<div class="space-y-8">
    <!-- License Details -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-id-card-alt text-blue-600 mr-2"></i>
            {{ __('messages.apply.license_details') }}
        </h3>

        <div class="grid md:grid-cols-2 gap-6">
            <!-- License Category -->
            <div>
                <label for="license_category" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ __('messages.apply.license_category') }} <span class="text-red-500">*</span>
                </label>
                <select id="license_category"
                        name="license_category"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        required>
                    <option value="">Select license category</option>
                    <option value="A1" {{ old('license_category', $application->license_category) == 'A1' ? 'selected' : '' }}>A1 - Light motorcycles (up to 125cc)</option>
                    <option value="A2" {{ old('license_category', $application->license_category) == 'A2' ? 'selected' : '' }}>A2 - Medium motorcycles (up to 35kW)</option>
                    <option value="A" {{ old('license_category', $application->license_category) == 'A' ? 'selected' : '' }}>A - All motorcycles</option>
                    <option value="B" {{ old('license_category', $application->license_category) == 'B' ? 'selected' : '' }}>B - Cars and light vehicles</option>
                    <option value="C1" {{ old('license_category', $application->license_category) == 'C1' ? 'selected' : '' }}>C1 - Medium trucks (3.5-7.5t)</option>
                    <option value="C" {{ old('license_category', $application->license_category) == 'C' ? 'selected' : '' }}>C - Heavy trucks (over 7.5t)</option>
                    <option value="D1" {{ old('license_category', $application->license_category) == 'D1' ? 'selected' : '' }}>D1 - Minibuses (up to 16 seats)</option>
                    <option value="D" {{ old('license_category', $application->license_category) == 'D' ? 'selected' : '' }}>D - Buses (over 16 seats)</option>
                </select>
            </div>

            <!-- Package Type -->
            <div>
                <label for="package_type" class="block text-sm font-medium text-gray-700 mb-2">
                    Package Type <span class="text-red-500">*</span>
                </label>
                <select id="package_type"
                        name="package_type"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        required>
                    <option value="">Select package type</option>
                    <option value="standard" {{ old('package_type', $application->package_type) == 'standard' ? 'selected' : '' }}>Standard Package</option>
                    <option value="express" {{ old('package_type', $application->package_type) == 'express' ? 'selected' : '' }}>Express Package</option>
                    <option value="premium" {{ old('package_type', $application->package_type) == 'premium' ? 'selected' : '' }}>Premium Package</option>
                </select>
            </div>

            <!-- Processing Speed -->
            <div>
                <label for="processing_speed" class="block text-sm font-medium text-gray-700 mb-2">
                    Processing Speed <span class="text-red-500">*</span>
                </label>
                <select id="processing_speed"
                        name="processing_speed"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        required>
                    <option value="">Select processing speed</option>
                    <option value="standard" {{ old('processing_speed', $application->processing_speed) == 'standard' ? 'selected' : '' }}>Standard (7-10 days)</option>
                    <option value="express" {{ old('processing_speed', $application->processing_speed) == 'express' ? 'selected' : '' }}>Express (3-5 days)</option>
                    <option value="urgent" {{ old('processing_speed', $application->processing_speed) == 'urgent' ? 'selected' : '' }}>Urgent (1-2 days)</option>
                </select>
            </div>

            <!-- Existing License Question -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Do you have an existing license?
                </label>
                <select id="has_existing_license_display"
                        name="has_existing_license_display"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        disabled>
                    <option value="0">No, this is my first license</option>
                    <option value="1">Yes, I have an existing or expired license</option>
                </select>
                <p class="text-sm text-gray-500 mt-1">This information was provided in Step 1 and cannot be changed here.</p>
            </div>
        </div>
    </div>

    <!-- Special Requirements -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-clipboard-list text-blue-600 mr-2"></i>
            Special Requirements
        </h3>
        <p class="text-gray-600 mb-4">
            If you have any special requirements or medical conditions that may affect your license application, please describe them here.
        </p>

        <div>
            <label for="special_requirements" class="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes (Optional)
            </label>
            <textarea id="special_requirements"
                      name="special_requirements"
                      rows="4"
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="Enter any special requirements, medical conditions, or additional information...">{{ old('special_requirements', $application->special_requirements) }}</textarea>
        </div>
    </div>
</div>

<!-- Processing Information -->
<div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <i class="fas fa-clock text-blue-500 mt-1"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Processing Information</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>Processing times may vary based on your selected package and speed. Express and urgent processing options are available for faster delivery.</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get existing license status from sessionStorage and update display
    const hasExistingLicense = sessionStorage.getItem('hasExistingLicense');
    const displaySelect = document.getElementById('has_existing_license_display');

    if (hasExistingLicense && displaySelect) {
        displaySelect.value = hasExistingLicense;
    }
});
</script>
@endpush
