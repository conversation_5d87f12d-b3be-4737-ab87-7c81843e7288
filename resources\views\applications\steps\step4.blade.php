<!-- Step 4: Review & Submit -->
<div class="space-y-8">
    <!-- Application Summary -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-user text-blue-600 mr-2"></i>
            Personal Information
        </h3>
        
        <div class="grid md:grid-cols-2 gap-4">
            <div>
                <span class="text-sm font-medium text-gray-500">Full Name:</span>
                <p class="text-gray-900">{{ $application->first_name }} {{ $application->last_name }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Email:</span>
                <p class="text-gray-900">{{ $application->email }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Phone:</span>
                <p class="text-gray-900">{{ $application->phone }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Date of Birth:</span>
                <p class="text-gray-900">{{ $application->date_of_birth ? $application->date_of_birth->format('F j, Y') : 'Not provided' }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Place of Birth:</span>
                <p class="text-gray-900">{{ $application->place_of_birth ?: 'Not provided' }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Nationality:</span>
                <p class="text-gray-900">{{ $application->nationality ?: 'Not provided' }}</p>
            </div>
        </div>
    </div>

    <!-- Address Information -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-home text-blue-600 mr-2"></i>
            Address Information
        </h3>
        
        <div class="grid md:grid-cols-2 gap-4">
            <div class="md:col-span-2">
                <span class="text-sm font-medium text-gray-500">Address:</span>
                <p class="text-gray-900">
                    {{ $application->address_line_1 ?: 'Not provided' }}
                    @if($application->address_line_2)
                        <br>{{ $application->address_line_2 }}
                    @endif
                </p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">City:</span>
                <p class="text-gray-900">{{ $application->city ?: 'Not provided' }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Postal Code:</span>
                <p class="text-gray-900">{{ $application->postal_code ?: 'Not provided' }}</p>
            </div>
            <div class="md:col-span-2">
                <span class="text-sm font-medium text-gray-500">Country:</span>
                <p class="text-gray-900">{{ $application->country ?: 'Not provided' }}</p>
            </div>
        </div>
    </div>

    <!-- License Information -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-id-card-alt text-blue-600 mr-2"></i>
            License Information
        </h3>
        
        <div class="grid md:grid-cols-2 gap-4">
            <div>
                <span class="text-sm font-medium text-gray-500">License Category:</span>
                <p class="text-gray-900">
                    @if($application->license_category)
                        {{ $application->license_category }} - 
                        @switch($application->license_category)
                            @case('A1')
                                Light motorcycles (up to 125cc)
                                @break
                            @case('A2')
                                Medium motorcycles (up to 35kW)
                                @break
                            @case('A')
                                All motorcycles
                                @break
                            @case('B')
                                Cars and light vehicles
                                @break
                            @case('C1')
                                Medium trucks (3.5-7.5t)
                                @break
                            @case('C')
                                Heavy trucks (over 7.5t)
                                @break
                            @case('D1')
                                Minibuses (9-16 seats)
                                @break
                            @case('D')
                                Buses (over 16 seats)
                                @break
                            @default
                                {{ $application->license_category }}
                        @endswitch
                    @else
                        Not provided
                    @endif
                </p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Target Country:</span>
                <p class="text-gray-900">{{ $application->country->name ?? 'Not specified' }}</p>
            </div>
            @if($application->previous_license_number)
            <div>
                <span class="text-sm font-medium text-gray-500">Previous License:</span>
                <p class="text-gray-900">{{ $application->previous_license_number }} ({{ $application->previous_license_country }})</p>
            </div>
            @endif
        </div>
    </div>

    <!-- Uploaded Documents -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-paperclip text-blue-600 mr-2"></i>
            Uploaded Documents
        </h3>
        
        @php
            $documents = $application->documents_uploaded ?? [];
        @endphp
        
        <div class="space-y-3">
            <!-- Signature -->
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-signature text-blue-600 mr-3"></i>
                    <span class="font-medium">Signature on White Paper</span>
                </div>
                @if(isset($documents['signature']))
                    <span class="text-green-600 flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>Uploaded
                    </span>
                @else
                    <span class="text-red-600 flex items-center">
                        <i class="fas fa-times-circle mr-2"></i>Missing
                    </span>
                @endif
            </div>

            <!-- ID Photo -->
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-id-card text-blue-600 mr-3"></i>
                    <span class="font-medium">ID/Passport Photo</span>
                </div>
                @if(isset($documents['id_photo']))
                    <span class="text-green-600 flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>Uploaded
                    </span>
                @else
                    <span class="text-red-600 flex items-center">
                        <i class="fas fa-times-circle mr-2"></i>Missing
                    </span>
                @endif
            </div>

            <!-- Passport Photo -->
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-user text-blue-600 mr-3"></i>
                    <span class="font-medium">Passport Size Photo</span>
                </div>
                @if(isset($documents['passport_photo']))
                    <span class="text-green-600 flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>Uploaded
                    </span>
                @else
                    <span class="text-red-600 flex items-center">
                        <i class="fas fa-times-circle mr-2"></i>Missing
                    </span>
                @endif
            </div>

            <!-- Additional Documents -->
            @if(isset($documents['additional']))
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-file text-blue-600 mr-3"></i>
                        <span class="font-medium">Additional Documents</span>
                    </div>
                    <span class="text-green-600 flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>Uploaded
                    </span>
                </div>
            @endif
        </div>
    </div>

    <!-- Application Status -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
            Application Status
        </h3>
        
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="fas fa-clock text-blue-600 mr-3"></i>
                <div>
                    <p class="font-medium text-blue-900">Application Number: {{ $application->application_number }}</p>
                    <p class="text-sm text-blue-700 mt-1">Status: {{ ucfirst(str_replace('_', ' ', $application->application_status)) }}</p>
                    <p class="text-sm text-blue-700">Created: {{ $application->created_at->format('F j, Y \a\t g:i A') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            <i class="fas fa-file-contract text-blue-600 mr-2"></i>
            Terms and Conditions
        </h3>
        
        <div class="space-y-4">
            <label class="flex items-start">
                <input type="checkbox" name="terms_accepted" class="mt-1 mr-3" required>
                <span class="text-sm text-gray-700">
                    I confirm that all information provided is accurate and complete. I understand that providing false information may result in the rejection of my application and potential legal consequences.
                </span>
            </label>
            
            <label class="flex items-start">
                <input type="checkbox" name="privacy_accepted" class="mt-1 mr-3" required>
                <span class="text-sm text-gray-700">
                    I agree to the <a href="#" class="text-blue-600 hover:text-blue-800">Privacy Policy</a> and <a href="#" class="text-blue-600 hover:text-blue-800">Terms of Service</a>. I consent to the processing of my personal data for the purpose of this application.
                </span>
            </label>
            
            <label class="flex items-start">
                <input type="checkbox" name="communication_accepted" class="mt-1 mr-3">
                <span class="text-sm text-gray-700">
                    I agree to receive email updates about my application status and promotional communications (optional).
                </span>
            </label>
        </div>
    </div>
</div>

<!-- Final Submission Notice -->
<div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-500 mt-1"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Important Notice</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <p>Please review all information carefully before proceeding. Once submitted, you will not be able to modify your application without contacting our support team.</p>
            </div>
        </div>
    </div>
</div>
