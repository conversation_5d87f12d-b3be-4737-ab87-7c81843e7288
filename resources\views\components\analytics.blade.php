@props(['page' => 'home', 'country' => null])

@php
use App\Services\AnalyticsService;

$analyticsService = new AnalyticsService();
$gtmCode = $analyticsService->getGTMCode();
@endphp

{{-- Google Search Console Verification --}}
@if(config('services.google_search_console.verification_code'))
<meta name="google-site-verification" content="{{ config('services.google_search_console.verification_code') }}">
@endif

{{-- Google Tag Manager (Head) --}}
@if($gtmCode && isset($gtmCode['head']))
{!! $gtmCode['head'] !!}
@endif

{{-- Google Analytics 4 --}}
{!! $analyticsService->getGA4TrackingCode() !!}

{{-- Facebook Pixel --}}
{!! $analyticsService->getFacebookPixelCode() !!}

{{-- Custom Analytics Events --}}
<script>
// Enhanced ecommerce tracking for application submissions
function trackApplicationStart(country, packageType) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'begin_checkout', {
            currency: 'EUR',
            country: country,
            package_type: packageType,
            event_category: 'Application',
            event_label: country + '_' + packageType
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'InitiateCheckout', {
            content_category: 'Driving License',
            content_name: country + ' Driving License',
            value: 0,
            currency: 'EUR'
        });
    }
}

function trackApplicationComplete(country, packageType, amount) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'purchase', {
            transaction_id: Date.now().toString(),
            value: amount,
            currency: 'EUR',
            country: country,
            package_type: packageType,
            items: [{
                item_id: country + '_license',
                item_name: country + ' Driving License',
                category: 'Driving License',
                quantity: 1,
                price: amount
            }]
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Purchase', {
            value: amount,
            currency: 'EUR',
            content_name: country + ' Driving License',
            content_category: 'Driving License',
            content_ids: [country + '_license'],
            content_type: 'product'
        });
    }
}

function trackPageView(page, country = null) {
    const data = {
        page_title: document.title,
        page_location: window.location.href,
        language: document.documentElement.lang || 'en'
    };
    
    if (country) {
        data.country = country;
    }
    
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'page_view', data);
    }
}

function trackContactFormSubmission() {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'generate_lead', {
            event_category: 'Contact',
            event_label: 'Contact Form Submission'
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Lead');
    }
}

function trackDownload(fileName) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'file_download', {
            file_name: fileName,
            event_category: 'Downloads'
        });
    }
}

function trackOutboundLink(url) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'click', {
            event_category: 'Outbound Links',
            event_label: url,
            transport_type: 'beacon'
        });
    }
}

// Auto-track scroll depth
let scrollDepthTracked = [];
function trackScrollDepth() {
    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    
    [25, 50, 75, 90].forEach(threshold => {
        if (scrollPercent >= threshold && !scrollDepthTracked.includes(threshold)) {
            scrollDepthTracked.push(threshold);
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'scroll', {
                    event_category: 'Engagement',
                    event_label: threshold + '%',
                    value: threshold
                });
            }
        }
    });
}

// Initialize scroll tracking
window.addEventListener('scroll', trackScrollDepth);

// Track time on page
let startTime = Date.now();
window.addEventListener('beforeunload', function() {
    const timeOnPage = Math.round((Date.now() - startTime) / 1000);
    
    if (typeof gtag !== 'undefined' && timeOnPage > 10) {
        gtag('event', 'timing_complete', {
            name: 'time_on_page',
            value: timeOnPage,
            event_category: 'Engagement'
        });
    }
});

// Track current page
document.addEventListener('DOMContentLoaded', function() {
    trackPageView('{{ $page }}', '{{ $country }}');
});
</script>

{{-- Structured Data for Analytics --}}
<script type="application/ld+json">
{!! json_encode($analyticsService->getStructuredAnalyticsData($page, $country), JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>
