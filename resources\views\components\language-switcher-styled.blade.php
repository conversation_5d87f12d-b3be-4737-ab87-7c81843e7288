@php
$currentLocale = app()->getLocale();
$supportedLocales = [
    'en' => ['name' => 'English', 'native' => 'English'],
    'de' => ['name' => 'German', 'native' => 'Deutsch'],
    'fr' => ['name' => 'French', 'native' => 'Français'],
    'es' => ['name' => 'Spanish', 'native' => 'Español'],
    'it' => ['name' => 'Italian', 'native' => 'Italiano'],
    'nl' => ['name' => 'Dutch', 'native' => 'Nederlands'],
    'ie' => ['name' => 'Irish English', 'native' => 'Irish English'],
    'ga' => ['name' => 'Irish', 'native' => 'Gaeilge'],
];
@endphp

<div class="language">
    <span>{{ __('messages.language.label') }}</span>
    <form action="#" class="language-switcher">
        <select class="selectpicker" onchange="changeLanguage(this.value)">
            @foreach($supportedLocales as $localeCode => $properties)
                <option value="{{ $localeCode }}" {{ $localeCode == $currentLocale ? 'selected' : '' }}>
                    {{ $properties['native'] }}
                </option>
            @endforeach
        </select>
    </form>
</div>

<script>
function changeLanguage(locale) {
    // Get current path without the locale prefix
    const currentPath = window.location.pathname;
    const pathSegments = currentPath.split('/').filter(segment => segment !== '');

    // Remove the current locale from the path if it exists
    const supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl', 'ie', 'ga'];
    if (pathSegments.length > 0 && supportedLocales.includes(pathSegments[0])) {
        pathSegments.shift(); // Remove the first segment (current locale)
    }

    // Build the new URL with the selected locale
    const newPath = '/' + locale + (pathSegments.length > 0 ? '/' + pathSegments.join('/') : '');

    // Navigate to the new URL
    window.location.href = newPath;
}
</script>

<style>
/* Language switcher styling to match template design */
.language {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-right: 30px;
}

.language span {
    margin: 0;
    margin-right: 5px;
    color: #3d6376;
    font-size: 14px;
    font-weight: 400;
}

.language .language-switcher {
    margin: 0;
    padding: 0;
}

.language .language-switcher .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: auto;
    min-width: 100px;
}

.language .language-switcher button {
    position: relative;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 15px 0 0;
    background: transparent;
    border: transparent;
    color: #3d6376;
    font-weight: 400;
}

.language .language-switcher .dropdown-toggle .filter-option {
    color: #3d6376;
    font-size: 14px;
}

.language .language-switcher .dropdown-toggle .filter-option:after {
    position: absolute;
    content: '\f107';
    font-size: 12px;
    font-family: 'Font Awesome 5 Pro';
    color: #3d6376;
    right: 0;
    top: 0;
    line-height: inherit;
}

.language .language-switcher .dropdown-menu {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-width: 120px;
}

.language .language-switcher .dropdown-menu .dropdown-item {
    color: #3d6376;
    font-size: 14px;
    padding: 8px 15px;
}

.language .language-switcher .dropdown-menu .dropdown-item:hover,
.language .language-switcher .dropdown-menu .dropdown-item.active {
    background-color: #f8f9fa;
    color: #2c5aa0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .language {
        margin-right: 15px;
    }
    
    .language span {
        font-size: 13px;
    }
    
    .language .language-switcher button {
        font-size: 13px;
    }
}
</style>
