@php
$currentLocale = app()->getLocale();
$supportedLocales = [
    'en' => ['name' => 'English', 'native' => 'English'],
    'de' => ['name' => 'German', 'native' => 'Deutsch'],
    'fr' => ['name' => 'French', 'native' => 'Français'],
    'es' => ['name' => 'Spanish', 'native' => 'Español'],
    'it' => ['name' => 'Italian', 'native' => 'Italiano'],
    'nl' => ['name' => 'Dutch', 'native' => 'Nederlands'],
    'ie' => ['name' => 'Irish English', 'native' => 'Irish English'],
    'ga' => ['name' => 'Irish', 'native' => 'Gaeilge'],
];
@endphp

<form action="#" class="language-switcher-top">
    <select class="selectpicker language-select" onchange="changeLanguage(this.value)">
        @foreach($supportedLocales as $localeCode => $properties)
            <option value="{{ $localeCode }}" {{ $localeCode == $currentLocale ? 'selected' : '' }}>
                {{ strtoupper($localeCode) }}
            </option>
        @endforeach
    </select>
</form>

<script>
function changeLanguage(locale) {
    // Get current path without the locale prefix
    const currentPath = window.location.pathname;
    const pathSegments = currentPath.split('/').filter(segment => segment !== '');

    // Remove the current locale from the path if it exists
    const supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl', 'ie', 'ga'];
    if (pathSegments.length > 0 && supportedLocales.includes(pathSegments[0])) {
        pathSegments.shift(); // Remove the first segment (current locale)
    }

    // Build the new URL with the selected locale
    const newPath = '/' + locale + (pathSegments.length > 0 ? '/' + pathSegments.join('/') : '');

    // Navigate to the new URL
    window.location.href = newPath;
}
</script>

<style>
.language-switcher-top {
    margin: 0;
    padding: 0;
}

.language-switcher-top .language-select {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    min-width: 60px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.language-switcher-top .language-select:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
}

.language-switcher-top .language-select:focus {
    outline: none;
    border-color: var(--eu-blue);
    box-shadow: 0 0 5px rgba(0, 51, 153, 0.3);
}

.language-switcher-top .language-select option {
    background: white;
    color: #333;
    padding: 5px;
}
</style>
