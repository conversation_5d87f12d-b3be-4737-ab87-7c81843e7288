@php
$currentLocale = app()->getLocale();
$supportedLocales = [
    'en' => ['name' => 'English', 'native' => 'English'],
    'de' => ['name' => 'German', 'native' => 'Deutsch'],
    'fr' => ['name' => 'French', 'native' => 'Français'],
    'es' => ['name' => 'Spanish', 'native' => 'Español'],
    'it' => ['name' => 'Italian', 'native' => 'Italiano'],
    'nl' => ['name' => 'Dutch', 'native' => 'Nederlands'],
    'ie' => ['name' => 'Irish English', 'native' => 'Irish English'],
    'ga' => ['name' => 'Irish', 'native' => 'Gaeilge'],
];
@endphp

<div class="language-switcher">
    <div class="language-dropdown">
        <button class="language-btn" type="button" onclick="toggleMainLanguageDropdown()">
            <span class="current-lang">{{ strtoupper($currentLocale) }}</span>
            <i class="fas fa-chevron-down"></i>
        </button>
        <div class="language-menu" id="mainLanguageMenu">
            @foreach($supportedLocales as $localeCode => $properties)
                <a class="language-item {{ $localeCode == $currentLocale ? 'active' : '' }}"
                   href="{{ url('/' . $localeCode . (request()->getPathInfo() !== '/' ? request()->getPathInfo() : '')) }}"
                   hreflang="{{ $localeCode }}"
                   onclick="selectMainLanguage('{{ strtoupper($localeCode) }}')">
                    {{ strtoupper($localeCode) }}
                </a>
            @endforeach
        </div>
    </div>
</div>

<script>
function toggleMainLanguageDropdown() {
    const menu = document.getElementById('mainLanguageMenu');
    menu.classList.toggle('show');
}

function selectMainLanguage(lang) {
    // The href will handle the navigation
    document.getElementById('mainLanguageMenu').classList.remove('show');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const switcher = document.querySelector('.language-switcher');
    if (!switcher.contains(event.target)) {
        document.getElementById('mainLanguageMenu').classList.remove('show');
    }
});
</script>

<style>
.language-switcher {
    position: relative;
    z-index: 1000;
}

.language-dropdown {
    position: relative;
}

.language-btn {
    background: var(--eu-blue) !important;
    border: 1px solid var(--eu-blue) !important;
    color: white !important;
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.language-btn:hover {
    background: var(--eu-blue-dark) !important;
    border-color: var(--eu-blue-dark) !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 51, 153, 0.3);
}

.language-btn i {
    font-size: 16px;
}

.current-lang {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.language-menu {
    background: white;
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 10px 0;
    margin-top: 5px;
    min-width: 200px;
    position: absolute;
    top: 100%;
    right: 0;
    display: none;
    z-index: 1000;
}

.language-menu.show {
    display: block;
}

.language-item {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
}

.language-item:hover {
    background: rgba(0, 51, 153, 0.1);
    color: var(--eu-blue);
    transform: translateX(5px);
}

.language-item.active {
    background: var(--eu-blue);
    color: white;
}

.language-item.active:hover {
    background: var(--eu-blue-dark);
    color: white;
}

.flag-icon {
    width: 20px;
    height: 15px;
    background-size: cover;
    border-radius: 2px;
    display: inline-block;
}

.flag-icon-gb { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDEyMTY5Ii8+CjxwYXRoIGQ9Ik0wIDBoMjB2MTVIMHoiIGZpbGw9IiMwMTIxNjkiLz4KPC9zdmc+'); }
.flag-icon-de { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiMwMDAwMDAiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiNERDAwMDAiLz4KPHJlY3QgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSI1IiBmaWxsPSIjRkZDRTAwIi8+Cjwvc3ZnPg=='); }
.flag-icon-fr { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYuNjciIGhlaWdodD0iMTUiIGZpbGw9IiMwMDIzOTUiLz4KPHJlY3QgeD0iNi42NyIgd2lkdGg9IjYuNjciIGhlaWdodD0iMTUiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeD0iMTMuMzMiIHdpZHRoPSI2LjY3IiBoZWlnaHQ9IjE1IiBmaWxsPSIjRUQyOTM5Ii8+Cjwvc3ZnPg=='); }
.flag-icon-es { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjMuNzUiIGZpbGw9IiNBQTAwMDAiLz4KPHJlY3QgeT0iMy43NSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjcuNSIgZmlsbD0iI0ZGQ0UwMCIvPgo8cmVjdCB5PSIxMS4yNSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjMuNzUiIGZpbGw9IiNBQTAwMDAiLz4KPC9zdmc+'); }
.flag-icon-it { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYuNjciIGhlaWdodD0iMTUiIGZpbGw9IiMwMDk5NDYiLz4KPHJlY3QgeD0iNi42NyIgd2lkdGg9IjYuNjciIGhlaWdodD0iMTUiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeD0iMTMuMzMiIHdpZHRoPSI2LjY3IiBoZWlnaHQ9IjE1IiBmaWxsPSIjQ0UyQjM3Ii8+Cjwvc3ZnPg=='); }
.flag-icon-nl { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiNBRTFDMjgiLz4KPHJlY3QgeT0iNSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSI1IiBmaWxsPSIjMjE0NjhCIi8+Cjwvc3ZnPg=='); }

.language-name {
    flex: 1;
    font-weight: 500;
}

.language-code {
    font-size: 12px;
    opacity: 0.7;
    font-weight: 600;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .language-btn {
        padding: 6px 12px;
        font-size: 13px;
    }

    .language-menu {
        min-width: 180px;
    }

    .language-item {
        padding: 8px 15px;
    }
}
</style>
