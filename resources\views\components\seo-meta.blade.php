@props(['page' => 'home', 'country' => null, 'customData' => []])

@php
use App\Services\SeoService;

$seoService = new SeoService();
$seoData = $seoService->generateMetaTags($page, $country, $customData);
@endphp

{{-- Basic Meta Tags --}}
<title>{{ $seoData['title'] }}</title>
<meta name="description" content="{{ $seoData['description'] }}">
<meta name="keywords" content="{{ $seoData['keywords'] }}">
<meta name="robots" content="index, follow">
<meta name="author" content="EU Driving License Services">

{{-- Canonical URL --}}
<link rel="canonical" href="{{ $seoData['canonical'] }}">

{{-- Hreflang Tags --}}
@foreach($seoData['hreflang'] as $hreflang)
<link rel="alternate" hreflang="{{ $hreflang['hreflang'] }}" href="{{ $hreflang['href'] }}">
@endforeach

{{-- Open Graph Tags --}}
@foreach($seoData['og_tags'] as $property => $content)
<meta property="{{ $property }}" content="{{ $content }}">
@endforeach

{{-- Twitter Card Tags --}}
@foreach($seoData['twitter_tags'] as $name => $content)
<meta name="{{ $name }}" content="{{ $content }}">
@endforeach

{{-- Schema.org JSON-LD --}}
<script type="application/ld+json">
{!! json_encode($seoData['schema'], JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>

{{-- Additional SEO Meta Tags --}}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">

{{-- Language and Locale --}}
<meta http-equiv="content-language" content="{{ app()->getLocale() }}">
<meta name="language" content="{{ app()->getLocale() }}">

{{-- Additional Open Graph Tags --}}
<meta property="og:image" content="{{ asset('images/eu-driving-license-og.jpg') }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="{{ $seoData['title'] }}">

{{-- Twitter Image --}}
<meta name="twitter:image" content="{{ asset('images/eu-driving-license-twitter.jpg') }}">
<meta name="twitter:image:alt" content="{{ $seoData['title'] }}">

{{-- Favicon and Icons --}}
<link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
<link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
<link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">
<link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">

{{-- Additional SEO Enhancements --}}
<meta name="theme-color" content="#1e40af">
<meta name="msapplication-TileColor" content="#1e40af">
<meta name="format-detection" content="telephone=no">

{{-- Preconnect for Performance --}}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

{{-- DNS Prefetch for External Resources --}}
<link rel="dns-prefetch" href="//www.google-analytics.com">
<link rel="dns-prefetch" href="//fonts.googleapis.com">

{{-- Additional Schema for Service Pages --}}
@if($page === 'buy' && $country)
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Product",
  "name": "{{ ucfirst($country) }} Driving License",
  "description": "{{ $seoData['description'] }}",
  "brand": {
    "@type": "Brand",
    "name": "EU Driving License Services"
  },
  "offers": {
    "@type": "Offer",
    "url": "{{ $seoData['canonical'] }}",
    "priceCurrency": "EUR",
    "availability": "https://schema.org/InStock",
    "seller": {
      "@type": "Organization",
      "name": "EU Driving License Services"
    }
  }
}
</script>
@endif

{{-- FAQ Schema for FAQ Pages --}}
@if($page === 'faq' && $country)
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "How long does it take to get a {{ ucfirst($country) }} driving license?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "The processing time for a {{ ucfirst($country) }} driving license is typically 5-7 business days."
      }
    },
    {
      "@type": "Question",
      "name": "Is the {{ ucfirst($country) }} driving license valid in other EU countries?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, all EU driving licenses are valid across all European Union member states."
      }
    },
    {
      "@type": "Question",
      "name": "What documents are required for a {{ ucfirst($country) }} driving license?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "You need a valid passport, proof of residence, and completed application form."
      }
    }
  ]
}
</script>
@endif

{{-- Breadcrumb Schema --}}
@if($page !== 'home')
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "{{ config('app.url') }}"
    }
    @if($country)
    ,{
      "@type": "ListItem",
      "position": 2,
      "name": "{{ ucfirst($country) }} Driving License",
      "item": "{{ config('app.url') }}/driving-license/{{ $country }}"
    }
    @endif
    @if($page === 'buy')
    ,{
      "@type": "ListItem",
      "position": {{ $country ? 3 : 2 }},
      "name": "Buy {{ ucfirst($country) }} License",
      "item": "{{ $seoData['canonical'] }}"
    }
    @endif
  ]
}
</script>
@endif

{{-- Organization Schema --}}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "EU Driving License Services",
  "url": "{{ config('app.url') }}",
  "logo": "{{ asset('images/logo.png') }}",
  "description": "Professional EU driving license services across all European Union countries.",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "******-123-4567",
    "contactType": "Customer Service",
    "availableLanguage": ["English", "German", "French", "Spanish", "Italian", "Dutch"]
  },
  "sameAs": [
    "https://www.facebook.com/EUDrivingLicense",
    "https://www.twitter.com/EUDrivingLicense",
    "https://www.linkedin.com/company/eu-driving-license"
  ]
}
</script>
