@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="flex items-center justify-center mb-6">
                <img src="{{ asset('images/flags/' . $country->code . '.webp') }}" 
                     alt="{{ $country->name }} Flag" 
                     class="w-16 h-12 object-cover rounded mr-4">
                <h1 class="text-4xl md:text-6xl font-bold">
                    Buy {{ $country->name }} Driving License
                </h1>
            </div>
            <p class="text-xl md:text-2xl mb-8 text-green-100">
                Fast, Legal & Secure - Get Your Authentic {{ $country->name }} Driving License Online
            </p>
            <div class="bg-white text-green-600 px-6 py-3 rounded-lg inline-block">
                <span class="text-2xl font-bold">€{{ number_format($country->base_price, 0) }}</span>
                <span class="text-lg ml-2">Processing: {{ $country->processing_time }}</span>
            </div>
        </div>
    </div>
</section>

<!-- Order Form Section -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Order Form -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-8">Order Your {{ $country->name }} Driving License</h2>
                
                <form class="space-y-6">
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <input type="text" id="full_name" name="full_name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="email" name="email" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="phone" name="phone" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                        <input type="date" id="date_of_birth" name="date_of_birth" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <textarea id="address" name="address" rows="3" required 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label for="license_type" class="block text-sm font-medium text-gray-700 mb-2">License Type</label>
                        <select id="license_type" name="license_type" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select License Type</option>
                            <option value="B">Category B (Car)</option>
                            <option value="A">Category A (Motorcycle)</option>
                            <option value="C">Category C (Truck)</option>
                            <option value="D">Category D (Bus)</option>
                        </select>
                    </div>
                    
                    <div class="flex items-start">
                        <input type="checkbox" id="terms" name="terms" required 
                               class="mt-1 mr-2">
                        <label for="terms" class="text-sm text-gray-600">
                            I agree to the <a href="{{ url('/terms-of-service') }}" class="text-blue-600 hover:underline">Terms of Service</a> 
                            and <a href="{{ url('/privacy-policy') }}" class="text-blue-600 hover:underline">Privacy Policy</a>
                        </label>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold text-lg transition-colors">
                        Proceed to Payment - €{{ number_format($country->base_price, 0) }}
                    </button>
                </form>
            </div>
            
            <!-- Order Summary -->
            <div>
                <div class="bg-gray-50 rounded-lg p-6 sticky top-8">
                    <h3 class="text-xl font-semibold mb-6">Order Summary</h3>
                    
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between">
                            <span>{{ $country->name }} Driving License</span>
                            <span class="font-semibold">€{{ number_format($country->base_price, 0) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Processing Fee</span>
                            <span class="font-semibold">Included</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Delivery</span>
                            <span class="font-semibold">Free</span>
                        </div>
                        <hr>
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span>€{{ number_format($country->base_price, 0) }}</span>
                        </div>
                    </div>
                    
                    <div class="space-y-4 text-sm text-gray-600">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Processing time: {{ $country->processing_time }}</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>100% legal and authentic</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>EU-wide validity</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Money-back guarantee</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-semibold text-blue-900 mb-2">Need Help?</h4>
                        <p class="text-sm text-blue-700">
                            Our customer support team is available 24/7 to assist you with your order.
                        </p>
                        <a href="{{ url('/contact') }}" class="text-blue-600 hover:underline text-sm font-medium">
                            Contact Support →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Security Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Secure & Trusted Service</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold mb-2">SSL Encrypted</h3>
                <p class="text-gray-600">All transactions are secured with 256-bit SSL encryption</p>
            </div>
            
            <div>
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold mb-2">Money-Back Guarantee</h3>
                <p class="text-gray-600">100% satisfaction guaranteed or your money back</p>
            </div>
            
            <div>
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold mb-2">24/7 Support</h3>
                <p class="text-gray-600">Round-the-clock customer support for all your needs</p>
            </div>
        </div>
    </div>
</section>
@endsection
