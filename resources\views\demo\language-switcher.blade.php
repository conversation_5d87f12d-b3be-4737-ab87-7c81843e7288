@extends('layouts.app')

@section('content')
<section class="demo-section" style="padding: 100px 0; background: #f8f9fa;">
    <div class="auto-container">
        <div class="sec-title text-center">
            <div class="sub-title">Language Switcher Demo</div>
            <h2>Multilingual Language Selector</h2>
            <div class="text">This page demonstrates the new language switcher component that matches the template design.</div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="demo-box" style="background: #fff; padding: 40px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin-bottom: 30px;">
                    <h3 style="color: #2c5aa0; margin-bottom: 20px;">Current Language Information</h3>
                    <div class="info-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <strong>Current Locale:</strong> {{ app()->getLocale() }}
                        </div>
                        <div>
                            <strong>Language Label:</strong> {{ __('messages.language.label') }}
                        </div>
                        <div>
                            <strong>Welcome Message:</strong> {{ __('messages.header.welcome') }}
                        </div>
                        <div>
                            <strong>Home Navigation:</strong> {{ __('messages.nav.home') }}
                        </div>
                    </div>
                </div>

                <div class="demo-box" style="background: #fff; padding: 40px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin-bottom: 30px;">
                    <h3 style="color: #2c5aa0; margin-bottom: 20px;">Language Switcher Component</h3>
                    <p>The language switcher in the header uses the following structure:</p>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                        @include('components.language-switcher-styled')
                    </div>
                    
                    <p style="margin-top: 20px;">
                        <strong>Features:</strong>
                    </p>
                    <ul style="margin-left: 20px;">
                        <li>Displays "{{ __('messages.language.label') }}" text (translated to current language)</li>
                        <li>Shows current language in native script</li>
                        <li>Dropdown with all supported languages</li>
                        <li>Maintains URL structure when switching languages</li>
                        <li>Matches the original template design</li>
                    </ul>
                </div>

                <div class="demo-box" style="background: #fff; padding: 40px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                    <h3 style="color: #2c5aa0; margin-bottom: 20px;">Supported Languages</h3>
                    <div class="languages-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇬🇧 English</strong><br>
                            <small>en - Default</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇩🇪 Deutsch</strong><br>
                            <small>de - German</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇫🇷 Français</strong><br>
                            <small>fr - French</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇪🇸 Español</strong><br>
                            <small>es - Spanish</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇮🇹 Italiano</strong><br>
                            <small>it - Italian</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇳🇱 Nederlands</strong><br>
                            <small>nl - Dutch</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇮🇪 Irish English</strong><br>
                            <small>ie - Irish English</small>
                        </div>
                        <div style="padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center;">
                            <strong>🇮🇪 Gaeilge</strong><br>
                            <small>ga - Irish Gaelic</small>
                        </div>
                    </div>
                </div>

                <div class="text-center" style="margin-top: 40px;">
                    <a href="{{ localized_route('home') }}" class="theme-btn btn-style-one">
                        <span class="btn-title">Back to Homepage</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
