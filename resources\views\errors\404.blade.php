@extends('layouts.app')

@section('title', 'Page Not Found - EU Driving License')
@section('meta_description', 'The page you are looking for could not be found. Return to EU Driving License homepage or browse our services.')

@section('content')
<section class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="container-eu">
        <div class="text-center max-w-2xl mx-auto">
            <!-- 404 Illustration -->
            <div class="mb-8">
                <div class="relative">
                    <div class="text-9xl font-bold text-eu-blue opacity-20">404</div>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-car text-6xl text-eu-blue"></i>
                    </div>
                </div>
            </div>
            
            <!-- Error Message -->
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Oops! Page Not Found
            </h1>
            <p class="text-xl text-gray-600 mb-8">
                The page you're looking for seems to have taken a wrong turn. 
                Don't worry, we'll help you get back on track!
            </p>
            
            <!-- Helpful Links -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <a href="{{ localized_route('home') }}" class="card-eu hover:shadow-xl transition-shadow">
                    <div class="card-eu-body text-center">
                        <i class="fas fa-home text-3xl text-eu-blue mb-3"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Go Home</h3>
                        <p class="text-gray-600 text-sm">Return to our homepage</p>
                    </div>
                </a>
                
                <a href="{{ localized_route('apply') }}" class="card-eu hover:shadow-xl transition-shadow">
                    <div class="card-eu-body text-center">
                        <i class="fas fa-file-alt text-3xl text-eu-blue mb-3"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Apply Now</h3>
                        <p class="text-gray-600 text-sm">Start your application</p>
                    </div>
                </a>
            </div>
            
            <!-- Popular Pages -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">Popular Pages</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="{{ localized_route('how-it-works') }}" class="text-eu-blue hover:text-eu-blue-dark font-medium">
                        How It Works
                    </a>
                    <a href="{{ localized_route('pricing') }}" class="text-eu-blue hover:text-eu-blue-dark font-medium">
                        Pricing
                    </a>
                    <a href="{{ localized_route('about') }}" class="text-eu-blue hover:text-eu-blue-dark font-medium">
                        About Us
                    </a>
                    <a href="{{ localized_route('contact') }}" class="text-eu-blue hover:text-eu-blue-dark font-medium">
                        Contact
                    </a>
                </div>
            </div>
            
            <!-- Countries -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">Browse by Country</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <a href="{{ localized_route('country.show', 'de') }}" class="flex items-center gap-3 p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                        <img src="/images/flags/germany.png" alt="Germany" class="w-8 h-6 object-cover rounded">
                        <span class="font-medium text-gray-900">Germany</span>
                    </a>
                    <a href="{{ localized_route('country.show', 'es') }}" class="flex items-center gap-3 p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                        <img src="/images/flags/spain.png" alt="Spain" class="w-8 h-6 object-cover rounded">
                        <span class="font-medium text-gray-900">Spain</span>
                    </a>
                    <a href="{{ localized_route('country.show', 'it') }}" class="flex items-center gap-3 p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                        <img src="/images/flags/italy.png" alt="Italy" class="w-8 h-6 object-cover rounded">
                        <span class="font-medium text-gray-900">Italy</span>
                    </a>
                    <a href="{{ localized_route('country.show', 'nl') }}" class="flex items-center gap-3 p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                        <img src="/images/flags/netherlands.png" alt="Netherlands" class="w-8 h-6 object-cover rounded">
                        <span class="font-medium text-gray-900">Netherlands</span>
                    </a>
                    <a href="{{ localized_route('country.show', 'ie') }}" class="flex items-center gap-3 p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                        <img src="/images/flags/ireland.png" alt="Ireland" class="w-8 h-6 object-cover rounded">
                        <span class="font-medium text-gray-900">Ireland</span>
                    </a>
                    <a href="{{ localized_route('country.show', 'gb') }}" class="flex items-center gap-3 p-3 bg-white rounded-lg hover:shadow-md transition-shadow">
                        <img src="/images/flags/uk.png" alt="United Kingdom" class="w-8 h-6 object-cover rounded">
                        <span class="font-medium text-gray-900">UK</span>
                    </a>
                </div>
            </div>
            
            <!-- Search -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">Search Our Site</h2>
                <form action="{{ localized_route('search') }}" method="GET" class="max-w-md mx-auto">
                    <div class="relative">
                        <input type="text" 
                               name="q" 
                               placeholder="Search for pages, countries, or information..."
                               class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-eu-blue focus:border-eu-blue">
                        <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-eu-blue">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ localized_route('home') }}" class="btn-eu-primary">
                    <i class="fas fa-home mr-2"></i>
                    Back to Homepage
                </a>
                <a href="{{ localized_route('contact') }}" class="btn-eu-outline">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Support
                </a>
            </div>
            
            <!-- Help Text -->
            <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                <p class="text-blue-800 text-sm">
                    <i class="fas fa-info-circle mr-2"></i>
                    If you believe this is an error or you were looking for specific information, 
                    please <a href="{{ localized_route('contact') }}" class="underline font-medium">contact our support team</a> 
                    and we'll be happy to help you find what you need.
                </p>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
// Add some interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Animate the 404 number
    const number404 = document.querySelector('.text-9xl');
    if (number404) {
        number404.style.opacity = '0';
        number404.style.transform = 'scale(0.8)';
        number404.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            number404.style.opacity = '0.2';
            number404.style.transform = 'scale(1)';
        }, 300);
    }
    
    // Add hover effects to cards
    const cards = document.querySelectorAll('.card-eu');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Auto-focus search input after a delay
    setTimeout(() => {
        const searchInput = document.querySelector('input[name="q"]');
        if (searchInput) {
            searchInput.focus();
        }
    }, 1000);
});

// Track 404 errors for analytics
if (typeof gtag !== 'undefined') {
    gtag('event', 'page_view', {
        page_title: '404 Error',
        page_location: window.location.href,
        custom_parameter: '404_error'
    });
}
</script>
@endpush

@push('styles')
<style>
.card-eu {
    transition: all 0.3s ease;
}

.card-eu:hover {
    transform: translateY(-2px);
}

/* Animate the car icon */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.fa-car {
    animation: bounce 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .text-9xl {
        font-size: 6rem;
    }
    
    .fa-car {
        font-size: 3rem !important;
    }
}
</style>
@endpush
