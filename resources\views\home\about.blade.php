@extends('layouts.app')

@section('head')
    <!-- Enhanced SEO Meta Tags -->
    <title>{{ $seoData['title'] ?? __('messages.about.page_title') }}</title>
    <meta name="description" content="{{ $seoData['description'] ?? __('messages.about.meta_description') }}">
    <meta name="keywords" content="{{ $seoData['keywords'] ?? __('messages.about.meta_keywords') }}">
    <link rel="canonical" href="{{ $seoData['canonical'] ?? localized_route('about') }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $seoData['title'] ?? __('messages.about.page_title') }}">
    <meta property="og:description" content="{{ $seoData['description'] ?? __('messages.about.meta_description') }}">
    <meta property="og:image" content="{{ asset('images/about-og-image.webp') }}">
    <meta property="og:url" content="{{ localized_route('about') }}">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $seoData['title'] ?? __('messages.about.page_title') }}">
    <meta name="twitter:description" content="{{ $seoData['description'] ?? __('messages.about.meta_description') }}">
    <meta name="twitter:image" content="{{ asset('images/about-og-image.webp') }}">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ config('app.name') }}",
        "description": "{{ __('messages.about.company_description') }}",
        "url": "{{ url('/') }}",
        "logo": "{{ asset('images/logo.webp') }}",
        "foundingDate": "2020",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "EU"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "{{ __('messages.contact.phone') }}",
            "contactType": "customer service",
            "email": "{{ __('messages.contact.email') }}"
        },
        "sameAs": [
            "https://facebook.com/{{ config('app.name') }}",
            "https://twitter.com/{{ config('app.name') }}",
            "https://linkedin.com/company/{{ config('app.name') }}"
        ]
    }
    </script>
@endsection

@section('content')
    <!-- Page Banner Section -->
    <section class="page-banner">
        <div class="image-layer lazy-image" data-bg="url('{{ asset('images/background/about-banner.webp') }}')"></div>
        <div class="bottom-rotten-curve alternate"></div>

        <div class="auto-container">
            <h1>{{ __('messages.about.page_title') }}</h1>
            <ul class="bread-crumb clearfix">
                <li><a href="{{ localized_route('home') }}">{{ __('messages.nav.home') }}</a></li>
                <li class="active">{{ __('messages.nav.about') }}</li>
            </ul>
        </div>
    </section>

    <!-- About Section Two -->
    <section class="about-section-two">
        <div class="auto-container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="image-wrapper">
                        <div class="image-one">
                            <img class="lazy-image owl-lazy"
                                 src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                 data-src="{{ asset('images/about/about-main.webp') }}"
                                 alt="{{ __('messages.about.main_image_alt') }}"
                                 loading="lazy">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="content-box">
                        <div class="sec-title">
                            <div class="sub-title">{{ __('messages.about.section_subtitle') }}</div>
                            <h2>{{ __('messages.about.main_heading') }}</h2>
                            <div class="text">{{ __('messages.about.main_description') }}</div>
                        </div>
                        <div class="author-info">
                            <div class="wrapper-box">
                                <h2>{{ __('messages.about.founder_name') }}</h2>
                                <div class="designation">{{ __('messages.about.founder_title') }}</div>
                                <div class="text">{{ __('messages.about.founder_quote') }}</div>
                            </div>
                            <div class="signature">
                                <img src="{{ asset('images/about/signature.png') }}"
                                     alt="{{ __('messages.about.founder_signature_alt') }}"
                                     loading="lazy">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- History Section -->
    <section class="history-section">
        <div class="auto-container">
            <div class="sec-title text-center light">
                <div class="sub-title">{{ __('messages.about.history_subtitle') }}</div>
                <h2>{{ __('messages.about.history_title') }}</h2>
            </div>
            <ul class="nav nav-tabs tab-btn-style-one" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="tab-2024-area" data-toggle="tab" href="#tab-2024" role="tab" aria-controls="tab-2024" aria-selected="true">2024</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="tab-2022-area" data-toggle="tab" href="#tab-2022" role="tab" aria-controls="tab-2022" aria-selected="false">2022</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="tab-2021-area" data-toggle="tab" href="#tab-2021" role="tab" aria-controls="tab-2021" aria-selected="false">2021</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="tab-2020-area" data-toggle="tab" href="#tab-2020" role="tab" aria-controls="tab-2020" aria-selected="false">2020</a>
                </li>
            </ul>

            <!-- Tab panes -->
            <div class="tab-content">
                <div class="tab-pane fadeInUp animated active" id="tab-2024" role="tabpanel" aria-labelledby="tab-2024">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="image">
                                <img class="lazy-image"
                                     src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                     data-src="{{ asset('images/about/history-2024.webp') }}"
                                     alt="{{ __('messages.about.history_2024_alt') }}"
                                     loading="lazy">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="content pl-lg-4">
                                <div class="sec-title light">
                                    <div class="sub-title">{{ __('messages.about.history_2024_subtitle') }}</div>
                                    <h2>{{ __('messages.about.history_2024_title') }}</h2>
                                </div>
                                <div class="text">{{ __('messages.about.history_2024_description') }}</div>
                                <div class="link-btn">
                                    <a href="{{ localized_route('contact') }}" class="theme-btn btn-style-one">
                                        <span class="btn-title">{{ __('messages.about.learn_more') }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fadeInUp animated" id="tab-2022" role="tabpanel" aria-labelledby="tab-2022">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="image">
                                <img class="lazy-image"
                                     src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                     data-src="{{ asset('images/about/history-2022.webp') }}"
                                     alt="{{ __('messages.about.history_2022_alt') }}"
                                     loading="lazy">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="content pl-lg-4">
                                <div class="sec-title light">
                                    <div class="sub-title">{{ __('messages.about.history_2022_subtitle') }}</div>
                                    <h2>{{ __('messages.about.history_2022_title') }}</h2>
                                </div>
                                <div class="text">{{ __('messages.about.history_2022_description') }}</div>
                                <div class="link-btn">
                                    <a href="{{ localized_route('how-it-works') }}" class="theme-btn btn-style-one">
                                        <span class="btn-title">{{ __('messages.about.learn_more') }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fadeInUp animated" id="tab-2021" role="tabpanel" aria-labelledby="tab-2021">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="image">
                                <img class="lazy-image"
                                     src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                     data-src="{{ asset('images/about/history-2021.webp') }}"
                                     alt="{{ __('messages.about.history_2021_alt') }}"
                                     loading="lazy">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="content pl-lg-4">
                                <div class="sec-title light">
                                    <div class="sub-title">{{ __('messages.about.history_2021_subtitle') }}</div>
                                    <h2>{{ __('messages.about.history_2021_title') }}</h2>
                                </div>
                                <div class="text">{{ __('messages.about.history_2021_description') }}</div>
                                <div class="link-btn">
                                    <a href="{{ localized_route('pricing') }}" class="theme-btn btn-style-one">
                                        <span class="btn-title">{{ __('messages.about.learn_more') }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fadeInUp animated" id="tab-2020" role="tabpanel" aria-labelledby="tab-2020">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="image">
                                <img class="lazy-image"
                                     src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                     data-src="{{ asset('images/about/history-2020.webp') }}"
                                     alt="{{ __('messages.about.history_2020_alt') }}"
                                     loading="lazy">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="content pl-lg-4">
                                <div class="sec-title light">
                                    <div class="sub-title">{{ __('messages.about.history_2020_subtitle') }}</div>
                                    <h2>{{ __('messages.about.history_2020_title') }}</h2>
                                </div>
                                <div class="text">{{ __('messages.about.history_2020_description') }}</div>
                                <div class="link-btn">
                                    <a href="{{ localized_route('home') }}" class="theme-btn btn-style-one">
                                        <span class="btn-title">{{ __('messages.about.learn_more') }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="team-section">
        <div class="auto-container">
            <div class="sec-title text-center">
                <div class="sub-title">{{ __('messages.about.team_subtitle') }}</div>
                <h2>{{ __('messages.about.team_title') }}</h2>
            </div>
            <div class="row">
                <!-- Team Block One -->
                <div class="col-lg-4 team-block-one">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image owl-lazy"
                                 src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                 data-src="{{ asset('images/team/team-1.webp') }}"
                                 alt="{{ __('messages.about.team_member_1_alt') }}"
                                 loading="lazy">
                        </div>
                        <div class="content">
                            <div class="designation">{{ __('messages.about.team_member_1_role') }}</div>
                            <h3>{{ __('messages.about.team_member_1_name') }}</h3>
                            <ul class="social-links">
                                <li><a href="#" aria-label="Facebook"><span class="fab fa-facebook-f"></span></a></li>
                                <li><a href="#" aria-label="Twitter"><span class="fab fa-twitter"></span></a></li>
                                <li><a href="#" aria-label="LinkedIn"><span class="fab fa-linkedin-in"></span></a></li>
                                <li><a href="#" aria-label="Email"><span class="fas fa-envelope"></span></a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Team Block Two -->
                <div class="col-lg-4 team-block-one">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image owl-lazy"
                                 src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                 data-src="{{ asset('images/team/team-2.webp') }}"
                                 alt="{{ __('messages.about.team_member_2_alt') }}"
                                 loading="lazy">
                        </div>
                        <div class="content">
                            <div class="designation">{{ __('messages.about.team_member_2_role') }}</div>
                            <h3>{{ __('messages.about.team_member_2_name') }}</h3>
                            <ul class="social-links">
                                <li><a href="#" aria-label="Facebook"><span class="fab fa-facebook-f"></span></a></li>
                                <li><a href="#" aria-label="Twitter"><span class="fab fa-twitter"></span></a></li>
                                <li><a href="#" aria-label="LinkedIn"><span class="fab fa-linkedin-in"></span></a></li>
                                <li><a href="#" aria-label="Email"><span class="fas fa-envelope"></span></a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Team Block Three -->
                <div class="col-lg-4 team-block-one">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image owl-lazy"
                                 src="{{ asset('images/resource/image-spacer-for-validation.png') }}"
                                 data-src="{{ asset('images/team/team-3.webp') }}"
                                 alt="{{ __('messages.about.team_member_3_alt') }}"
                                 loading="lazy">
                        </div>
                        <div class="content">
                            <div class="designation">{{ __('messages.about.team_member_3_role') }}</div>
                            <h3>{{ __('messages.about.team_member_3_name') }}</h3>
                            <ul class="social-links">
                                <li><a href="#" aria-label="Facebook"><span class="fab fa-facebook-f"></span></a></li>
                                <li><a href="#" aria-label="Twitter"><span class="fab fa-twitter"></span></a></li>
                                <li><a href="#" aria-label="LinkedIn"><span class="fab fa-linkedin-in"></span></a></li>
                                <li><a href="#" aria-label="Email"><span class="fas fa-envelope"></span></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section Four -->
    <section class="services-section-four">
        <div class="auto-container">
            <div class="sec-title text-center">
                <div class="sub-title">{{ __('messages.about.features_subtitle') }}</div>
                <h2>{{ __('messages.about.features_title') }}</h2>
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <div class="icon-box">
                        <div class="icon">
                            <img src="{{ asset('images/icons/legal-compliance.webp') }}"
                                 alt="{{ __('messages.about.feature_1_icon_alt') }}"
                                 loading="lazy">
                        </div>
                        <h2>{{ __('messages.about.feature_1_title') }}</h2>
                        <div class="text">{{ __('messages.about.feature_1_description') }}</div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="icon-box">
                        <div class="icon">
                            <img src="{{ asset('images/icons/fast-processing.webp') }}"
                                 alt="{{ __('messages.about.feature_2_icon_alt') }}"
                                 loading="lazy">
                        </div>
                        <h2>{{ __('messages.about.feature_2_title') }}</h2>
                        <div class="text">{{ __('messages.about.feature_2_description') }}</div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="icon-box">
                        <div class="icon">
                            <img src="{{ asset('images/icons/secure-process.webp') }}"
                                 alt="{{ __('messages.about.feature_3_icon_alt') }}"
                                 loading="lazy">
                        </div>
                        <h2>{{ __('messages.about.feature_3_title') }}</h2>
                        <div class="text">{{ __('messages.about.feature_3_description') }}</div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="icon-box">
                        <div class="icon">
                            <img src="{{ asset('images/icons/expert-support.webp') }}"
                                 alt="{{ __('messages.about.feature_4_icon_alt') }}"
                                 loading="lazy">
                        </div>
                        <h2>{{ __('messages.about.feature_4_title') }}</h2>
                        <div class="text">{{ __('messages.about.feature_4_description') }}</div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="icon-box">
                        <div class="icon">
                            <img src="{{ asset('images/icons/money-back.webp') }}"
                                 alt="{{ __('messages.about.feature_5_icon_alt') }}"
                                 loading="lazy">
                        </div>
                        <h2>{{ __('messages.about.feature_5_title') }}</h2>
                        <div class="text">{{ __('messages.about.feature_5_description') }}</div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="icon-box">
                        <div class="icon">
                            <img src="{{ asset('images/icons/multilingual.webp') }}"
                                 alt="{{ __('messages.about.feature_6_icon_alt') }}"
                                 loading="lazy">
                        </div>
                        <h2>{{ __('messages.about.feature_6_title') }}</h2>
                        <div class="text">{{ __('messages.about.feature_6_description') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="counter-section-two">
        <div class="auto-container">
            <div class="sec-title text-center light">
                <div class="sub-title">{{ __('messages.about.stats_subtitle') }}</div>
                <h2>{{ __('messages.about.stats_title') }}</h2>
            </div>
            <div class="row">
                <!-- Counter Block -->
                <div class="col-lg-3 col-md-6 counter-block-one">
                    <div class="inner-box">
                        <div class="icon">
                            <span class="flaticon-user"></span>
                        </div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="15000">0</span><span>+</span>
                        </div>
                        <h3>{{ __('messages.about.stat_1_title') }}</h3>
                    </div>
                </div>

                <!-- Counter Block -->
                <div class="col-lg-3 col-md-6 counter-block-one">
                    <div class="inner-box">
                        <div class="icon">
                            <span class="flaticon-globe"></span>
                        </div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="27">0</span>
                        </div>
                        <h3>{{ __('messages.about.stat_2_title') }}</h3>
                    </div>
                </div>

                <!-- Counter Block -->
                <div class="col-lg-3 col-md-6 counter-block-one">
                    <div class="inner-box">
                        <div class="icon">
                            <span class="flaticon-award"></span>
                        </div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="99">0</span><span>%</span>
                        </div>
                        <h3>{{ __('messages.about.stat_3_title') }}</h3>
                    </div>
                </div>

                <!-- Counter Block -->
                <div class="col-lg-3 col-md-6 counter-block-one">
                    <div class="inner-box">
                        <div class="icon">
                            <span class="flaticon-support"></span>
                        </div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="24">0</span><span>/7</span>
                        </div>
                        <h3>{{ __('messages.about.stat_4_title') }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
