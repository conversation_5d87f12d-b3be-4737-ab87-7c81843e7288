@extends('layouts.app')

@section('head')
<title>{{ $seoData['title'] }}</title>
<meta name="description" content="{{ $seoData['description'] }}">
<meta name="keywords" content="{{ $seoData['keywords'] }}">
<meta name="robots" content="index, follow">
<link rel="canonical" href="{{ $seoData['canonical'] }}">

<!-- Open Graph Tags -->
@foreach($seoData['og_tags'] as $property => $content)
<meta property="{{ $property }}" content="{{ $content }}">
@endforeach

<!-- Twitter Card Tags -->
@foreach($seoData['twitter_tags'] as $name => $content)
<meta name="{{ $name }}" content="{{ $content }}">
@endforeach

<!-- Hreflang Tags -->
@foreach($seoData['hreflang'] as $hreflang)
<link rel="alternate" hreflang="{{ $hreflang['hreflang'] }}" href="{{ $hreflang['href'] }}">
@endforeach

<!-- Schema.org Markup -->
<script type="application/ld+json">
{!! json_encode($seoData['schema'], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
</script>

<style>
.contact-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    border: 1px solid #e9ecef;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.contact-icon i {
    color: #007bff;
}

.contact-title {
    color: #333;
    font-weight: 600;
}

.contact-details a {
    color: #007bff;
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: #0056b3;
}

.contact-form-wrapper {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
}
</style>
@endsection

@section('content')
<!-- Page Title Section -->
<section class="page-title" style="background-image: url({{ asset('images/background/page-title-bg.jpg') }});">
    <div class="auto-container">
        <div class="content-box">
            <div class="content-wrapper">
                <div class="title">
                    <h1>{{ __('messages.contact.title') }}</h1>
                </div>
                <ul class="bread-crumb">
                    <li><a href="{{ localized_route('home') }}">{{ __('messages.nav.home') }}</a></li>
                    <li>{{ __('messages.contact.subtitle') }}</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="contact-section py-5">
    <div class="container">
        <!-- Contact Information Cards -->
        <div class="row justify-content-center mb-5">
            <!-- Email Card -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="contact-card text-center p-4 h-100 border rounded shadow-sm">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-envelope fa-3x text-primary"></i>
                    </div>
                    <h4 class="contact-title mb-3">{{ __('messages.contact.email_title') }}</h4>
                    <div class="contact-details">
                        <p class="mb-2">
                            <a href="mailto:{{ __('messages.contact.email') }}" class="text-decoration-none">
                                {{ __('messages.contact.email') }}
                            </a>
                        </p>
                        <p class="mb-0">
                            <a href="mailto:<EMAIL>" class="text-decoration-none">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Phone Card -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="contact-card text-center p-4 h-100 border rounded shadow-sm">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-phone fa-3x text-primary"></i>
                    </div>
                    <h4 class="contact-title mb-3">{{ __('messages.contact.phone_title') }}</h4>
                    <div class="contact-details">
                        <p class="mb-2">
                            <a href="tel:{{ __('messages.contact.phone_link') }}" class="text-decoration-none">
                                {{ __('messages.contact.phone') }}
                            </a>
                        </p>
                        <p class="mb-0">
                            <a href="tel:+35318001234" class="text-decoration-none">
                                +353 1 800 1234
                            </a>
                        </p>
                        <small class="text-muted d-block mt-2">{{ __('messages.contact.hours') }}</small>
                    </div>
                </div>
            </div>

            <!-- Office Card -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="contact-card text-center p-4 h-100 border rounded shadow-sm">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-map-marker-alt fa-3x text-primary"></i>
                    </div>
                    <h4 class="contact-title mb-3">{{ __('messages.contact.address_title') }}</h4>
                    <div class="contact-details">
                        <p class="mb-2">{{ __('messages.contact.address') }}</p>
                        <div class="hours mt-3">
                            <strong>{{ __('messages.contact.hours_title') }}:</strong><br>
                            <small class="text-muted">{{ __('messages.contact.hours') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="contact-form-area">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form-wrapper bg-light p-5 rounded shadow">
                        <div class="text-center mb-4">
                            <h3 class="mb-3">{{ __('messages.contact.form_title') }}</h3>
                            <p class="text-muted">{{ __('messages.contact.form_description') }}</p>
                        </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

                        <!-- Contact Form -->
                        <form method="post" action="{{ route('contact.send') }}" id="contact-form">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">{{ __('messages.contact.name_label') }}</label>
                                    <input type="text" name="name" id="name" class="form-control" placeholder="{{ __('messages.contact.name_placeholder') }}" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">{{ __('messages.contact.email_label') }}</label>
                                    <input type="email" name="email" id="email" class="form-control" placeholder="{{ __('messages.contact.email_placeholder') }}" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">{{ __('messages.contact.phone_label') }}</label>
                                    <input type="tel" name="phone" id="phone" class="form-control" placeholder="{{ __('messages.contact.phone_placeholder') }}" value="{{ old('phone') }}">
                                    @error('phone')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">{{ __('messages.contact.subject_label') }}</label>
                                    <input type="text" name="subject" id="subject" class="form-control" placeholder="{{ __('messages.contact.subject_placeholder') }}" value="{{ old('subject') }}" required>
                                    @error('subject')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="message" class="form-label">{{ __('messages.contact.message_label') }}</label>
                                    <textarea name="message" id="message" class="form-control" rows="5" placeholder="{{ __('messages.contact.message_placeholder') }}" required>{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5" id="submit-btn">
                                        {{ __('messages.contact.submit_button') }}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



@endsection

@section('scripts')
<script>
// Contact form handling
document.getElementById('contact-form').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('#submit-btn');
    const originalText = submitBtn.innerHTML;

    // Add loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {{ __("messages.contact.submit_button") }}...';
    submitBtn.disabled = true;

    // Let the form submit normally (no preventDefault)
    // The loading state will show until page reloads
});

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert && alert.parentNode) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            }
        }, 5000);
    });
});

// Initialize map if Google Maps is available
function initMap() {
    if (typeof google !== 'undefined' && google.maps) {
        const mapElement = document.querySelector('.map-canvas');
        if (mapElement) {
            const lat = parseFloat(mapElement.getAttribute('data-lat'));
            const lng = parseFloat(mapElement.getAttribute('data-lng'));
            const zoom = parseInt(mapElement.getAttribute('data-zoom'));

            const map = new google.maps.Map(mapElement, {
                center: { lat: lat, lng: lng },
                zoom: zoom,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });

            const marker = new google.maps.Marker({
                position: { lat: lat, lng: lng },
                map: map,
                title: mapElement.getAttribute('data-title'),
                icon: mapElement.getAttribute('data-icon-path')
            });

            const infoWindow = new google.maps.InfoWindow({
                content: mapElement.getAttribute('data-content')
            });

            marker.addListener('click', function() {
                infoWindow.open(map, marker);
            });
        }
    }
}

// Load Google Maps if not already loaded
if (typeof google === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
} else {
    initMap();
}
</script>
@endsection
