@extends('layouts.app')

@section('content')
<!-- Modern Hero Section -->
<section class="hero-eu section-padding-lg-eu">
    <div class="container-eu">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="animate-fade-in">
                <h1 class="hero-title-eu">
                    {{ __('messages.homepage.title') }}
                </h1>
                <p class="hero-subtitle-eu">
                    {{ __('messages.homepage.subtitle') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 mt-8">
                    <a href="{{ localized_route('apply') }}" class="btn-eu-primary text-center">
                        {{ __('messages.header.get_license_now') }}
                    </a>
                    <a href="{{ localized_route('how-it-works') }}" class="btn-eu-outline text-center">
                        {{ __('messages.common.learn_more') }}
                    </a>
                </div>
                
                <!-- Trust Indicators -->
                <div class="flex items-center gap-6 mt-8 text-blue-100">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-shield-alt text-eu-gold"></i>
                        <span class="text-sm">{{ __('messages.homepage.secure') }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i class="fas fa-clock text-eu-gold"></i>
                        <span class="text-sm">{{ __('messages.homepage.fast') }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i class="fas fa-certificate text-eu-gold"></i>
                        <span class="text-sm">{{ __('messages.homepage.legal') }}</span>
                    </div>
                </div>
            </div>
            
            <div class="animate-scale-in">
                <div class="relative">
                    <img src="{{ asset('images/hero-driving-license.webp') }}" 
                         alt="{{ __('messages.homepage.hero_image_alt') }}" 
                         class="w-full h-auto rounded-2xl shadow-2xl">
                    <div class="absolute -bottom-6 -right-6 bg-eu-gold text-eu-blue p-4 rounded-xl shadow-lg">
                        <div class="text-2xl font-bold">5-7</div>
                        <div class="text-sm">{{ __('messages.homepage.days') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.homepage.features_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.homepage.features_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="card-eu card-eu-body text-center animate-slide-up">
                <div class="feature-icon-eu mx-auto">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h3 class="feature-title-eu">{{ __('messages.homepage.feature1_title') }}</h3>
                <p class="feature-description-eu">{{ __('messages.homepage.feature1_description') }}</p>
            </div>
            
            <div class="card-eu card-eu-body text-center animate-slide-up" style="animation-delay: 0.1s;">
                <div class="feature-icon-eu mx-auto">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="feature-title-eu">{{ __('messages.homepage.feature2_title') }}</h3>
                <p class="feature-description-eu">{{ __('messages.homepage.feature2_description') }}</p>
            </div>
            
            <div class="card-eu card-eu-body text-center animate-slide-up" style="animation-delay: 0.2s;">
                <div class="feature-icon-eu mx-auto">
                    <i class="fas fa-globe-europe"></i>
                </div>
                <h3 class="feature-title-eu">{{ __('messages.homepage.feature3_title') }}</h3>
                <p class="feature-description-eu">{{ __('messages.homepage.feature3_description') }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Countries Section -->
<section class="section-padding-eu" id="countries">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.homepage.countries_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.homepage.countries_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($countries as $country)
            <a href="{{ localized_route('country.show', $country->code) }}" class="country-card">
                <div class="p-6">
                    <div class="flex items-center gap-4 mb-4">
                        <img src="{{ $country->flag_url }}" 
                             alt="{{ $country->name }} flag" 
                             class="country-flag">
                        <div>
                            <h3 class="font-semibold text-gray-900">{{ $country->name }}</h3>
                            <p class="text-sm text-gray-600">{{ __('messages.countries.driving_license') }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            {{ __('messages.countries.from') }} €{{ $country->base_price }}
                        </div>
                        <div class="text-sm text-eu-blue font-medium">
                            {{ $country->processing_days_min }}-{{ $country->processing_days_max }} {{ __('messages.countries.days') }}
                        </div>
                    </div>
                </div>
            </a>
            @endforeach
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="section-padding-eu bg-eu-blue text-white">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold mb-4">
                {{ __('messages.homepage.process_title') }}
            </h2>
            <p class="text-responsive-lg text-blue-100 max-w-3xl mx-auto">
                {{ __('messages.homepage.process_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-eu-gold text-eu-blue rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    1
                </div>
                <h3 class="text-xl font-semibold mb-2">{{ __('messages.homepage.step1_title') }}</h3>
                <p class="text-blue-100">{{ __('messages.homepage.step1_description') }}</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-eu-gold text-eu-blue rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    2
                </div>
                <h3 class="text-xl font-semibold mb-2">{{ __('messages.homepage.step2_title') }}</h3>
                <p class="text-blue-100">{{ __('messages.homepage.step2_description') }}</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-eu-gold text-eu-blue rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    3
                </div>
                <h3 class="text-xl font-semibold mb-2">{{ __('messages.homepage.step3_title') }}</h3>
                <p class="text-blue-100">{{ __('messages.homepage.step3_description') }}</p>
            </div>
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ localized_route('apply') }}" class="btn-eu-secondary">
                {{ __('messages.homepage.start_application') }}
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
@if($testimonials->count() > 0)
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.homepage.testimonials_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.homepage.testimonials_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($testimonials->take(3) as $testimonial)
            <div class="testimonial-card-eu">
                <div class="testimonial-quote-eu mb-4">
                    "{{ $testimonial->content }}"
                </div>
                <div class="testimonial-author-eu">
                    <img src="{{ $testimonial->image_url }}" 
                         alt="{{ $testimonial->name }}" 
                         class="testimonial-avatar-eu mr-3">
                    <div>
                        <div class="font-semibold text-gray-900">{{ $testimonial->name }}</div>
                        <div class="text-sm text-gray-600">{{ $testimonial->location }}</div>
                        <div class="flex text-yellow-400 text-sm mt-1">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star{{ $i <= $testimonial->rating ? '' : '-o' }}"></i>
                            @endfor
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="section-padding-eu gradient-eu-hero text-white">
    <div class="container-eu text-center">
        <h2 class="text-responsive-3xl font-bold mb-4">
            {{ __('messages.homepage.cta_title') }}
        </h2>
        <p class="text-responsive-lg text-blue-100 max-w-3xl mx-auto mb-8">
            {{ __('messages.homepage.cta_subtitle') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ localized_route('apply') }}" class="btn-eu-secondary">
                {{ __('messages.homepage.get_started') }}
            </a>
            <a href="{{ localized_route('contact') }}" class="btn-eu-outline text-white border-white hover:bg-white hover:text-eu-blue">
                {{ __('messages.homepage.contact_us') }}
            </a>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
// Add smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add intersection observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.animationPlayState = 'running';
        }
    });
}, observerOptions);

// Observe all animated elements
document.querySelectorAll('.animate-fade-in, .animate-slide-up, .animate-scale-in').forEach(el => {
    el.style.animationPlayState = 'paused';
    observer.observe(el);
});
</script>
@endpush
