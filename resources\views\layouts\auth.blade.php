<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    @yield('head')

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Chart.js for admin charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'admin-blue': '#1e40af',
                        'admin-blue-dark': '#1e3a8a',
                        'admin-gray': '#f8fafc',
                        'admin-sidebar': '#1f2937',
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Login Page Styles */
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .form-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* Admin Dashboard Styles */
        .admin-sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .admin-nav-item {
            transition: all 0.3s ease;
        }

        .admin-nav-item:hover {
            background: rgba(59, 130, 246, 0.1);
            border-left: 4px solid #3b82f6;
            transform: translateX(4px);
        }

        .admin-nav-item.active {
            background: rgba(59, 130, 246, 0.2);
            border-left: 4px solid #3b82f6;
            color: #3b82f6;
        }

        .stat-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        /* Sidebar Collapse States */
        .admin-sidebar {
            transition: all 0.3s ease;
        }

        /* Collapsed sidebar */
        .sidebar-collapsed .admin-sidebar {
            width: 4rem; /* 64px */
        }

        .sidebar-collapsed .admin-sidebar h1,
        .sidebar-collapsed .admin-sidebar .text-xl,
        .sidebar-collapsed .admin-sidebar .text-xs,
        .sidebar-collapsed .admin-sidebar .text-sm,
        .sidebar-collapsed .admin-sidebar .space-y-2,
        .sidebar-collapsed .admin-sidebar .border-t {
            display: none;
        }

        .sidebar-collapsed .admin-sidebar .admin-nav-item {
            justify-content: center;
            padding: 0.75rem;
        }

        .sidebar-collapsed .admin-sidebar .admin-nav-item i {
            margin-right: 0;
        }

        /* Rotate toggle icon when collapsed */
        .sidebar-collapsed #sidebar-collapse-btn i,
        .sidebar-collapsed #desktop-sidebar-toggle i {
            transform: rotate(180deg);
        }

        /* Adjust main content when sidebar is collapsed */
        .sidebar-collapsed .flex-1 {
            margin-left: 0;
        }

        @media (min-width: 1024px) {
            .sidebar-collapsed .flex-1 {
                margin-left: 4rem;
            }
        }

        /* Mobile responsive */
        @media (max-width: 1024px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .admin-sidebar-open .admin-sidebar {
                transform: translateX(0);
            }

            .admin-sidebar-overlay {
                display: block;
                position: fixed;
                inset: 0;
                z-index: 40;
                background-color: rgba(0, 0, 0, 0.5);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .admin-sidebar-open .admin-sidebar-overlay {
                opacity: 1;
                visibility: visible;
            }

            .admin-sidebar-open {
                overflow: hidden;
            }

            /* Don't apply collapse styles on mobile */
            .sidebar-collapsed .admin-sidebar {
                width: 16rem; /* Keep full width on mobile */
            }
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="@if(request()->routeIs('login')) login-page @else bg-admin-gray @endif">
    <div class="min-h-screen">
        @yield('content')
    </div>

    <!-- Admin Dashboard JavaScript -->
    <script>
        // Sidebar collapse toggle (for all screen sizes)
        function toggleSidebarCollapse() {
            console.log('Toggle sidebar collapse clicked'); // Debug log
            document.body.classList.toggle('sidebar-collapsed');

            // Save state to localStorage
            const isCollapsed = document.body.classList.contains('sidebar-collapsed');
            localStorage.setItem('sidebar-collapsed', isCollapsed);

            // Add visual feedback
            const toggleBtns = document.querySelectorAll('#sidebar-collapse-btn, #desktop-sidebar-toggle');
            toggleBtns.forEach(btn => {
                if (btn) {
                    btn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        btn.style.transform = 'scale(1)';
                    }, 150);
                }
            });
        }

        // Mobile sidebar toggle - Enhanced version (for mobile overlay)
        function toggleSidebar() {
            console.log('Toggle sidebar clicked'); // Debug log
            document.body.classList.toggle('admin-sidebar-open');

            // Add visual feedback
            const toggleBtn = document.getElementById('sidebar-toggle');
            if (toggleBtn) {
                toggleBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    toggleBtn.style.transform = 'scale(1)';
                }, 150);
            }
        }

        // Close sidebar when clicking overlay
        function closeSidebar() {
            console.log('Close sidebar called'); // Debug log
            document.body.classList.remove('admin-sidebar-open');
        }

        // Enhanced click outside detection
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('admin-sidebar');
            const toggleBtn = document.getElementById('sidebar-toggle');
            const overlay = document.querySelector('.admin-sidebar-overlay');

            // Only handle on mobile/tablet
            if (window.innerWidth <= 1024 &&
                document.body.classList.contains('admin-sidebar-open') &&
                sidebar && toggleBtn &&
                !sidebar.contains(event.target) &&
                !toggleBtn.contains(event.target)) {
                closeSidebar();
            }
        });

        // Handle escape key to close sidebar
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && document.body.classList.contains('admin-sidebar-open')) {
                closeSidebar();
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 1024) {
                document.body.classList.remove('admin-sidebar-open');
            }
        });

        // Auto-refresh dashboard stats every 30 seconds
        if (window.location.pathname.includes('/admin') && !window.location.pathname.includes('/admin/login')) {
            setInterval(function() {
                // Only refresh if user is still active (not idle)
                if (document.hasFocus()) {
                    // You can implement AJAX refresh here
                    console.log('Auto-refreshing dashboard stats...');
                }
            }, 30000);
        }

        // Initialize sidebar state and debug logging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Admin dashboard loaded');
            console.log('Toggle button:', document.getElementById('sidebar-toggle'));
            console.log('Desktop toggle button:', document.getElementById('desktop-sidebar-toggle'));
            console.log('Sidebar:', document.getElementById('admin-sidebar'));

            // Restore sidebar collapsed state from localStorage
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            if (isCollapsed) {
                document.body.classList.add('sidebar-collapsed');
                console.log('Restored collapsed sidebar state');
            }

            // Add tooltips for collapsed sidebar items
            updateSidebarTooltips();
        });

        // Update tooltips for sidebar items when collapsed
        function updateSidebarTooltips() {
            const navItems = document.querySelectorAll('.admin-nav-item');
            const isCollapsed = document.body.classList.contains('sidebar-collapsed');

            navItems.forEach(item => {
                if (isCollapsed) {
                    const text = item.textContent.trim();
                    item.setAttribute('title', text);
                } else {
                    item.removeAttribute('title');
                }
            });
        }

        // Update tooltips when sidebar state changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (mutation.target === document.body) {
                        updateSidebarTooltips();
                    }
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });
    </script>
</body>
</html>
