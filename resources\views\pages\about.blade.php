@extends('layouts.app')

@section('title', __('messages.about.title'))
@section('meta_description', __('messages.about.meta_description'))

@section('content')
<!-- Hero Section -->
<section class="hero-eu section-padding-eu">
    <div class="container-eu">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="hero-title-eu">{{ __('messages.about.title') }}</h1>
            <p class="hero-subtitle-eu">{{ __('messages.about.subtitle') }}</p>
        </div>
    </div>
</section>

<!-- About Content -->
<section class="section-padding-eu bg-white">
    <div class="container-eu">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-responsive-3xl font-bold text-gray-900 mb-6">
                    {{ __('messages.about.who_we_are_title') }}
                </h2>
                <p class="text-responsive-lg text-gray-600 mb-6">
                    {{ __('messages.about.who_we_are_content') }}
                </p>
                <p class="text-responsive-base text-gray-600 mb-8">
                    {{ __('messages.about.mission_content') }}
                </p>
                
                <div class="grid grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-eu-blue">10,000+</div>
                        <div class="text-sm text-gray-600">{{ __('messages.about.satisfied_customers') }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-eu-blue">27</div>
                        <div class="text-sm text-gray-600">{{ __('messages.about.eu_countries') }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-eu-blue">99%</div>
                        <div class="text-sm text-gray-600">{{ __('messages.about.success_rate') }}</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-eu-blue">24/7</div>
                        <div class="text-sm text-gray-600">{{ __('messages.about.support') }}</div>
                    </div>
                </div>
            </div>
            
            <div class="relative">
                <img src="{{ asset('images/about-team.webp') }}" 
                     alt="{{ __('messages.about.team_image_alt') }}" 
                     class="w-full h-auto rounded-2xl shadow-xl">
                <div class="absolute -bottom-6 -left-6 bg-eu-gold text-eu-blue p-6 rounded-xl shadow-lg">
                    <div class="text-2xl font-bold">5+ Years</div>
                    <div class="text-sm">{{ __('messages.about.experience') }}</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.about.values_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.about.values_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="card-eu card-eu-body text-center">
                <div class="feature-icon-eu mx-auto mb-4">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title-eu">{{ __('messages.about.value1_title') }}</h3>
                <p class="feature-description-eu">{{ __('messages.about.value1_description') }}</p>
            </div>
            
            <div class="card-eu card-eu-body text-center">
                <div class="feature-icon-eu mx-auto mb-4">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="feature-title-eu">{{ __('messages.about.value2_title') }}</h3>
                <p class="feature-description-eu">{{ __('messages.about.value2_description') }}</p>
            </div>
            
            <div class="card-eu card-eu-body text-center">
                <div class="feature-icon-eu mx-auto mb-4">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="feature-title-eu">{{ __('messages.about.value3_title') }}</h3>
                <p class="feature-description-eu">{{ __('messages.about.value3_description') }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="section-padding-eu bg-white">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.about.team_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.about.team_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="card-eu text-center">
                <div class="card-eu-body">
                    <img src="{{ asset('images/team/ceo.webp') }}" 
                         alt="CEO" 
                         class="w-24 h-24 rounded-full mx-auto mb-4 object-cover">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Michael Schmidt</h3>
                    <p class="text-eu-blue font-medium mb-3">CEO & Founder</p>
                    <p class="text-gray-600 text-sm">15+ years experience in European licensing and legal compliance.</p>
                </div>
            </div>
            
            <div class="card-eu text-center">
                <div class="card-eu-body">
                    <img src="{{ asset('images/team/cto.webp') }}" 
                         alt="CTO" 
                         class="w-24 h-24 rounded-full mx-auto mb-4 object-cover">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Sarah Johnson</h3>
                    <p class="text-eu-blue font-medium mb-3">CTO</p>
                    <p class="text-gray-600 text-sm">Technology expert specializing in secure document processing systems.</p>
                </div>
            </div>
            
            <div class="card-eu text-center">
                <div class="card-eu-body">
                    <img src="{{ asset('images/team/legal.webp') }}" 
                         alt="Legal Director" 
                         class="w-24 h-24 rounded-full mx-auto mb-4 object-cover">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Dr. Elena Rodriguez</h3>
                    <p class="text-eu-blue font-medium mb-3">Legal Director</p>
                    <p class="text-gray-600 text-sm">EU law specialist ensuring full legal compliance across all services.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="section-padding-eu bg-eu-blue text-white">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold mb-4">
                {{ __('messages.about.why_choose_title') }}
            </h2>
            <p class="text-responsive-lg text-blue-100 max-w-3xl mx-auto">
                {{ __('messages.about.why_choose_subtitle') }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-eu-gold text-eu-blue rounded-lg flex items-center justify-center text-xl flex-shrink-0">
                    <i class="fas fa-certificate"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('messages.about.advantage1_title') }}</h3>
                    <p class="text-blue-100">{{ __('messages.about.advantage1_description') }}</p>
                </div>
            </div>
            
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-eu-gold text-eu-blue rounded-lg flex items-center justify-center text-xl flex-shrink-0">
                    <i class="fas fa-rocket"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('messages.about.advantage2_title') }}</h3>
                    <p class="text-blue-100">{{ __('messages.about.advantage2_description') }}</p>
                </div>
            </div>
            
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-eu-gold text-eu-blue rounded-lg flex items-center justify-center text-xl flex-shrink-0">
                    <i class="fas fa-headset"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('messages.about.advantage3_title') }}</h3>
                    <p class="text-blue-100">{{ __('messages.about.advantage3_description') }}</p>
                </div>
            </div>
            
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-eu-gold text-eu-blue rounded-lg flex items-center justify-center text-xl flex-shrink-0">
                    <i class="fas fa-globe-europe"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('messages.about.advantage4_title') }}</h3>
                    <p class="text-blue-100">{{ __('messages.about.advantage4_description') }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu text-center">
        <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
            {{ __('messages.about.cta_title') }}
        </h2>
        <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto mb-8">
            {{ __('messages.about.cta_subtitle') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ localized_route('apply') }}" class="btn-eu-primary">
                {{ __('messages.about.get_started') }}
            </a>
            <a href="{{ localized_route('contact') }}" class="btn-eu-outline">
                {{ __('messages.about.contact_us') }}
            </a>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
// Add smooth scrolling and animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate counters
    const counters = document.querySelectorAll('.text-3xl.font-bold');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/[^0-9]/g, ''));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = counter.textContent.replace(/[0-9,]+/, target.toLocaleString());
                clearInterval(timer);
            } else {
                counter.textContent = counter.textContent.replace(/[0-9,]+/, Math.floor(current).toLocaleString());
            }
        }, 20);
    };
    
    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => observer.observe(counter));
});
</script>
@endpush
