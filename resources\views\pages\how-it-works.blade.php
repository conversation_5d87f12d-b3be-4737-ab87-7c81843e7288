@extends('layouts.app')

@section('title', __('messages.how_it_works.title'))
@section('meta_description', __('messages.how_it_works.meta_description'))

@section('content')
<!-- Hero Section -->
<section class="hero-eu section-padding-eu">
    <div class="container-eu">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="hero-title-eu">{{ __('messages.how_it_works.title') }}</h1>
            <p class="hero-subtitle-eu">{{ __('messages.how_it_works.subtitle') }}</p>
        </div>
    </div>
</section>

<!-- Process Steps -->
<section class="section-padding-eu bg-white">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.how_it_works.process_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.how_it_works.process_subtitle') }}
            </p>
        </div>
        
        <div class="relative">
            <!-- Progress Line -->
            <div class="hidden lg:block absolute top-24 left-0 right-0 h-1 bg-gray-200">
                <div class="h-full bg-eu-blue w-full"></div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Step 1 -->
                <div class="text-center relative">
                    <div class="w-16 h-16 bg-eu-blue text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 relative z-10">
                        1
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('messages.how_it_works.step1_title') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('messages.how_it_works.step1_description') }}</p>
                    <div class="text-sm text-eu-blue font-medium">{{ __('messages.how_it_works.step1_time') }}</div>
                </div>
                
                <!-- Step 2 -->
                <div class="text-center relative">
                    <div class="w-16 h-16 bg-eu-blue text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 relative z-10">
                        2
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('messages.how_it_works.step2_title') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('messages.how_it_works.step2_description') }}</p>
                    <div class="text-sm text-eu-blue font-medium">{{ __('messages.how_it_works.step2_time') }}</div>
                </div>
                
                <!-- Step 3 -->
                <div class="text-center relative">
                    <div class="w-16 h-16 bg-eu-blue text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 relative z-10">
                        3
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('messages.how_it_works.step3_title') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('messages.how_it_works.step3_description') }}</p>
                    <div class="text-sm text-eu-blue font-medium">{{ __('messages.how_it_works.step3_time') }}</div>
                </div>
                
                <!-- Step 4 -->
                <div class="text-center relative">
                    <div class="w-16 h-16 bg-eu-gold text-eu-blue rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6 relative z-10">
                        4
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ __('messages.how_it_works.step4_title') }}</h3>
                    <p class="text-gray-600 mb-4">{{ __('messages.how_it_works.step4_description') }}</p>
                    <div class="text-sm text-eu-gold-dark font-medium">{{ __('messages.how_it_works.step4_time') }}</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Requirements Section -->
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-responsive-3xl font-bold text-gray-900 mb-6">
                    {{ __('messages.how_it_works.requirements_title') }}
                </h2>
                <p class="text-responsive-lg text-gray-600 mb-8">
                    {{ __('messages.how_it_works.requirements_subtitle') }}
                </p>
                
                <div class="space-y-4">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm flex-shrink-0 mt-1">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ __('messages.how_it_works.req1_title') }}</h4>
                            <p class="text-gray-600 text-sm">{{ __('messages.how_it_works.req1_description') }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm flex-shrink-0 mt-1">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ __('messages.how_it_works.req2_title') }}</h4>
                            <p class="text-gray-600 text-sm">{{ __('messages.how_it_works.req2_description') }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm flex-shrink-0 mt-1">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ __('messages.how_it_works.req3_title') }}</h4>
                            <p class="text-gray-600 text-sm">{{ __('messages.how_it_works.req3_description') }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm flex-shrink-0 mt-1">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ __('messages.how_it_works.req4_title') }}</h4>
                            <p class="text-gray-600 text-sm">{{ __('messages.how_it_works.req4_description') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="relative">
                <img src="{{ asset('images/requirements-checklist.webp') }}" 
                     alt="{{ __('messages.how_it_works.requirements_image_alt') }}" 
                     class="w-full h-auto rounded-2xl shadow-xl">
                <div class="absolute -top-6 -right-6 bg-green-500 text-white p-4 rounded-xl shadow-lg">
                    <div class="text-2xl font-bold">✓</div>
                    <div class="text-sm">{{ __('messages.how_it_works.verified') }}</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Timeline Section -->
<section class="section-padding-eu bg-white">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.how_it_works.timeline_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.how_it_works.timeline_subtitle') }}
            </p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                
                <div class="space-y-8">
                    <!-- Day 1 -->
                    <div class="flex items-start gap-6">
                        <div class="w-16 h-16 bg-eu-blue text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                            Day 1
                        </div>
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('messages.how_it_works.day1_title') }}</h3>
                            <p class="text-gray-600">{{ __('messages.how_it_works.day1_description') }}</p>
                        </div>
                    </div>
                    
                    <!-- Day 2-3 -->
                    <div class="flex items-start gap-6">
                        <div class="w-16 h-16 bg-eu-blue text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                            Day 2-3
                        </div>
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('messages.how_it_works.day2_title') }}</h3>
                            <p class="text-gray-600">{{ __('messages.how_it_works.day2_description') }}</p>
                        </div>
                    </div>
                    
                    <!-- Day 4-7 -->
                    <div class="flex items-start gap-6">
                        <div class="w-16 h-16 bg-eu-blue text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                            Day 4-7
                        </div>
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('messages.how_it_works.day4_title') }}</h3>
                            <p class="text-gray-600">{{ __('messages.how_it_works.day4_description') }}</p>
                        </div>
                    </div>
                    
                    <!-- Day 8-10 -->
                    <div class="flex items-start gap-6">
                        <div class="w-16 h-16 bg-eu-gold text-eu-blue rounded-full flex items-center justify-center font-bold flex-shrink-0">
                            Day 8-10
                        </div>
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('messages.how_it_works.day8_title') }}</h3>
                            <p class="text-gray-600">{{ __('messages.how_it_works.day8_description') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding-eu bg-gray-50">
    <div class="container-eu">
        <div class="text-center mb-16">
            <h2 class="text-responsive-3xl font-bold text-gray-900 mb-4">
                {{ __('messages.how_it_works.faq_title') }}
            </h2>
            <p class="text-responsive-lg text-gray-600 max-w-3xl mx-auto">
                {{ __('messages.how_it_works.faq_subtitle') }}
            </p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="space-y-4">
                <div class="card-eu">
                    <div class="card-eu-body">
                        <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(this)">
                            <h3 class="text-lg font-semibold text-gray-900">{{ __('messages.how_it_works.faq1_question') }}</h3>
                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform"></i>
                        </button>
                        <div class="mt-4 text-gray-600 hidden">
                            <p>{{ __('messages.how_it_works.faq1_answer') }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="card-eu">
                    <div class="card-eu-body">
                        <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(this)">
                            <h3 class="text-lg font-semibold text-gray-900">{{ __('messages.how_it_works.faq2_question') }}</h3>
                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform"></i>
                        </button>
                        <div class="mt-4 text-gray-600 hidden">
                            <p>{{ __('messages.how_it_works.faq2_answer') }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="card-eu">
                    <div class="card-eu-body">
                        <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(this)">
                            <h3 class="text-lg font-semibold text-gray-900">{{ __('messages.how_it_works.faq3_question') }}</h3>
                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform"></i>
                        </button>
                        <div class="mt-4 text-gray-600 hidden">
                            <p>{{ __('messages.how_it_works.faq3_answer') }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="card-eu">
                    <div class="card-eu-body">
                        <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(this)">
                            <h3 class="text-lg font-semibent text-gray-900">{{ __('messages.how_it_works.faq4_question') }}</h3>
                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform"></i>
                        </button>
                        <div class="mt-4 text-gray-600 hidden">
                            <p>{{ __('messages.how_it_works.faq4_answer') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding-eu gradient-eu-hero text-white">
    <div class="container-eu text-center">
        <h2 class="text-responsive-3xl font-bold mb-4">
            {{ __('messages.how_it_works.cta_title') }}
        </h2>
        <p class="text-responsive-lg text-blue-100 max-w-3xl mx-auto mb-8">
            {{ __('messages.how_it_works.cta_subtitle') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ localized_route('apply') }}" class="btn-eu-secondary">
                {{ __('messages.how_it_works.start_application') }}
            </a>
            <a href="{{ localized_route('contact') }}" class="btn-eu-outline text-white border-white hover:bg-white hover:text-eu-blue">
                {{ __('messages.how_it_works.ask_question') }}
            </a>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
    } else {
        content.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
}

// Add scroll animations
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe timeline items
    document.querySelectorAll('.flex.items-start.gap-6').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'all 0.6s ease';
        observer.observe(item);
    });
});
</script>
@endpush
