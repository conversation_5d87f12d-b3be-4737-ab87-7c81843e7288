<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.16/dist/tailwind.min.css" rel="stylesheet">
</head>

<body>
    <div class="flex justify-center items-center h-screen">
        <div class="w-96">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                <input type="file" id="file-input" class="hidden" accept="image/*,application/pdf">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700 mb-2">Click to upload or drag and drop</p>
                    <p class="text-sm text-gray-500">JPG, PNG, PDF up to 5MB</p>
                </div>
                <div class="upload-success hidden">
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                    <p class="text-lg font-medium text-green-700 mb-2">File uploaded successfully</p>
                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">Replace file</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('file-input');
            const uploadContent = document.querySelector('.upload-content');
            const uploadSuccess = document.querySelector('.upload-success');

            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    uploadContent.classList.add('hidden');
                    uploadSuccess.classList.remove('hidden');
                }
            });

            uploadContent.addEventListener('click', function() {
                fileInput.click();
            });
        });
    </script>
</body>

</html>




