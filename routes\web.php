<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\ApplicationController;
use App\Http\Controllers\AdminController;


// Define supported locales
$supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl', 'ie', 'ga'];

// Create routes for each locale
foreach ($supportedLocales as $locale) {
    Route::group([
        'prefix' => $locale,
        'middleware' => ['web']
    ], function() use ($locale) {

    // Homepage
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // Country routes
    Route::get('/driving-license/{country}', [CountryController::class, 'show'])->name('country.show');
    Route::get('/buy-{country}-driving-licence-online', [CountryController::class, 'buy'])->name('country.buy');
    Route::get('/{country}-driving-license-requirements', [CountryController::class, 'requirements'])->name('country.requirements');
    Route::get('/{country}-driving-license-process', [CountryController::class, 'process'])->name('country.process');
    Route::get('/{country}-driving-license-faq', [CountryController::class, 'faq'])->name('country.faq');

    // Application routes
    Route::get('/apply/{country}', [ApplicationController::class, 'create'])->name('applications.create');
    Route::post('/apply/{country}', [ApplicationController::class, 'store'])->name('applications.store');
    Route::get('/applications/{application}', [ApplicationController::class, 'show'])->name('applications.show');
    Route::get('/applications/{application}/edit', [ApplicationController::class, 'edit'])->name('applications.edit');
    Route::put('/applications/{application}', [ApplicationController::class, 'update'])->name('applications.update');
    Route::post('/applications/{application}/submit', [ApplicationController::class, 'submit'])->name('applications.submit');

    // Multi-step application routes
    Route::get('/applications/{application}/step/{step}', [ApplicationController::class, 'showStep'])->name('applications.step');
    Route::post('/applications/{application}/step/{step}', [ApplicationController::class, 'saveStep'])->name('applications.save-step');
    Route::get('/applications/{application}/review', [ApplicationController::class, 'showReview'])->name('applications.review');
    Route::post('/applications/{application}/upload', [ApplicationController::class, 'uploadDocument'])->name('applications.upload-document');
    Route::post('/applications/{application}/cancel', [ApplicationController::class, 'cancel'])->name('applications.cancel');

    // Application tracking
    Route::get('/track', [ApplicationController::class, 'track'])->name('applications.track');
    Route::post('/track', [ApplicationController::class, 'track'])->name('applications.track.search');

    // Additional pages
    Route::get('/how-it-works', [HomeController::class, 'howItWorks'])->name('how-it-works');
    Route::get('/pricing', [HomeController::class, 'pricing'])->name('pricing');
    Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
    Route::get('/about', [HomeController::class, 'about'])->name('about');

    // Legal pages
    Route::get('/privacy-policy', [HomeController::class, 'privacy'])->name('privacy');
    Route::get('/terms-of-service', [HomeController::class, 'terms'])->name('terms');

    // Irish driving licence routes
    Route::get('/irish-driving-licence', [App\Http\Controllers\IrishLicenceController::class, 'index'])->name('irish-licence');
    Route::get('/buy-irish-driving-licence-online', [App\Http\Controllers\IrishLicenceController::class, 'buy'])->name('irish-licence.buy');
    Route::get('/irish-driving-licence-requirements', [App\Http\Controllers\IrishLicenceController::class, 'requirements'])->name('irish-licence.requirements');

    // Application routes (public access)
    Route::get('/apply', [ApplicationController::class, 'startApplication'])->name('apply');
    Route::get('/apply-test', function() {
        $application = new \App\Models\Application();
        $seoData = [
            'title' => 'Apply for EU Driving License - Test Form',
            'description' => 'Test single-step application form',
            'keywords' => 'apply, driving license, test',
            'canonical' => url('/apply-test')
        ];
        return view('applications.single-step', compact('application', 'seoData'));
    })->name('apply-test');

    // Contact form submission
    Route::post('/contact/send', [HomeController::class, 'sendContact'])->name('contact.send');

    // Demo page for language switcher
    Route::get('/demo/language-switcher', function() {
        $seoData = [
            'title' => 'Language Switcher Demo - EU Driving License',
            'description' => 'Demonstration of the multilingual language switcher component.',
            'keywords' => 'language switcher, multilingual, demo',
            'canonical' => url('/demo/language-switcher')
        ];
        return view('demo.language-switcher', compact('seoData'));
    })->name('demo.language-switcher');
    });
}

// Default route (redirect to localized homepage)
Route::get('/', function() {
    return redirect('/en');
});



// Sitemap routes (not localized)
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');
Route::get('/generate-sitemap', [App\Http\Controllers\SitemapController::class, 'generate'])->name('sitemap.generate');

// Authentication routes
require __DIR__.'/auth.php';

// Admin routes (not localized)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');

    // Applications management
    Route::get('/applications', [AdminController::class, 'applications'])->name('applications');
    Route::get('/applications/{application}', [AdminController::class, 'showApplication'])->name('applications.show');
    Route::post('/applications/{application}/status', [AdminController::class, 'updateApplicationStatus'])->name('applications.status');

    // Countries management
    Route::get('/countries', [AdminController::class, 'countries'])->name('countries');
    Route::get('/countries/{country}/edit', [AdminController::class, 'editCountry'])->name('countries.edit');
    Route::put('/countries/{country}', [AdminController::class, 'updateCountry'])->name('countries.update');

    // Users management
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::get('/users/{user}', [AdminController::class, 'showUser'])->name('users.show');

    // Reports and settings
    Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
    Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    Route::post('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');
});
