<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['page' => 'home', 'country' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['page' => 'home', 'country' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
use App\Services\AnalyticsService;

$analyticsService = new AnalyticsService();
$gtmCode = $analyticsService->getGTMCode();
?>


<?php if(config('services.google_search_console.verification_code')): ?>
<meta name="google-site-verification" content="<?php echo e(config('services.google_search_console.verification_code')); ?>">
<?php endif; ?>


<?php if($gtmCode && isset($gtmCode['head'])): ?>
<?php echo $gtmCode['head']; ?>

<?php endif; ?>


<?php echo $analyticsService->getGA4TrackingCode(); ?>



<?php echo $analyticsService->getFacebookPixelCode(); ?>



<script>
// Enhanced ecommerce tracking for application submissions
function trackApplicationStart(country, packageType) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'begin_checkout', {
            currency: 'EUR',
            country: country,
            package_type: packageType,
            event_category: 'Application',
            event_label: country + '_' + packageType
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'InitiateCheckout', {
            content_category: 'Driving License',
            content_name: country + ' Driving License',
            value: 0,
            currency: 'EUR'
        });
    }
}

function trackApplicationComplete(country, packageType, amount) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'purchase', {
            transaction_id: Date.now().toString(),
            value: amount,
            currency: 'EUR',
            country: country,
            package_type: packageType,
            items: [{
                item_id: country + '_license',
                item_name: country + ' Driving License',
                category: 'Driving License',
                quantity: 1,
                price: amount
            }]
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Purchase', {
            value: amount,
            currency: 'EUR',
            content_name: country + ' Driving License',
            content_category: 'Driving License',
            content_ids: [country + '_license'],
            content_type: 'product'
        });
    }
}

function trackPageView(page, country = null) {
    const data = {
        page_title: document.title,
        page_location: window.location.href,
        language: document.documentElement.lang || 'en'
    };
    
    if (country) {
        data.country = country;
    }
    
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'page_view', data);
    }
}

function trackContactFormSubmission() {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'generate_lead', {
            event_category: 'Contact',
            event_label: 'Contact Form Submission'
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Lead');
    }
}

function trackDownload(fileName) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'file_download', {
            file_name: fileName,
            event_category: 'Downloads'
        });
    }
}

function trackOutboundLink(url) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'click', {
            event_category: 'Outbound Links',
            event_label: url,
            transport_type: 'beacon'
        });
    }
}

// Auto-track scroll depth
let scrollDepthTracked = [];
function trackScrollDepth() {
    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    
    [25, 50, 75, 90].forEach(threshold => {
        if (scrollPercent >= threshold && !scrollDepthTracked.includes(threshold)) {
            scrollDepthTracked.push(threshold);
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'scroll', {
                    event_category: 'Engagement',
                    event_label: threshold + '%',
                    value: threshold
                });
            }
        }
    });
}

// Initialize scroll tracking
window.addEventListener('scroll', trackScrollDepth);

// Track time on page
let startTime = Date.now();
window.addEventListener('beforeunload', function() {
    const timeOnPage = Math.round((Date.now() - startTime) / 1000);
    
    if (typeof gtag !== 'undefined' && timeOnPage > 10) {
        gtag('event', 'timing_complete', {
            name: 'time_on_page',
            value: timeOnPage,
            event_category: 'Engagement'
        });
    }
});

// Track current page
document.addEventListener('DOMContentLoaded', function() {
    trackPageView('<?php echo e($page); ?>', '<?php echo e($country); ?>');
});
</script>


<script type="application/ld+json">
<?php echo json_encode($analyticsService->getStructuredAnalyticsData($page, $country), JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>

</script>
<?php /**PATH C:\Users\<USER>\Desktop\Web Devs\eu-drivinglicence\resources\views/components/analytics.blade.php ENDPATH**/ ?>