<?php $__env->startSection('content'); ?>
<!-- Banner Section -->
<section class="banner-section style-two">
    <div class="banner-carousel theme_carousel owl-theme owl-carousel" data-options='{"loop": true, "margin": 0, "autoheight":true, "lazyload":true, "nav": true, "dots": true, "autoplay": true, "autoplayTimeout": 6000, "smartSpeed": 300, "responsive":{ "0" :{ "items": "1" }, "768" :{ "items" : "1" } , "1000":{ "items" : "1" }}}'>
        <!-- Slide Item -->
        <div class="slide-item">
            <div class="image-layer lazy-image" data-bg="url('<?php echo e(asset('images/main-slider/2.jpg')); ?>')"></div>

            <div class="auto-container">
                <div class="content-box">
                    <h3><?php echo e(__('messages.homepage.welcome_message')); ?></h3>
                    <h2><?php echo e(__('messages.homepage.title')); ?></h2>
                    <div class="btn-box">
                        <a href="<?php echo e(localized_route('apply')); ?>" class="theme-btn btn-style-one"><span class="btn-title">-- <?php echo e(__('messages.header.get_license_now')); ?> --</span></a>
                        <a href="<?php echo e(localized_route('how-it-works')); ?>" class="theme-btn btn-style-two"><span class="btn-title">-- <?php echo e(__('messages.common.learn_more')); ?> --</span></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide Item -->
        <div class="slide-item">
            <div class="image-layer lazy-image" data-bg="url('<?php echo e(asset('images/main-slider/6.jpg')); ?>')"></div>

            <div class="auto-container">
                <div class="content-box">
                    <h3><?php echo e(__('messages.homepage.slide2_title1')); ?></h3>
                    <h2><?php echo e(__('messages.homepage.slide2_title2')); ?></h2>
                    <div class="btn-box">
                        <a href="#countries" class="theme-btn btn-style-one"><span class="btn-title">-- <?php echo e(__('messages.homepage.slide2_btn1')); ?> --</span></a>
                        <a href="<?php echo e(url('/about')); ?>" class="theme-btn btn-style-two"><span class="btn-title">-- <?php echo e(__('messages.homepage.slide2_btn2')); ?> --</span></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--End Banner Section -->

<!-- Feature Section -->
<section class="feature-section">
    <div class="auto-container">
        <div class="wrapper-box">
            <div class="row">
                <div class="col-lg-2 col-md-4 col-sm-6 col-xs-6 col-6">
                    <div class="feature-block-one">
                        <div class="inner-box">
                            <div class="icon"><i class="fas fa-balance-scale"></i></div>
                            <h5><?php echo e(__('messages.features.legal_service')); ?></h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 col-xs-6 col-6">
                    <div class="feature-block-one">
                        <div class="inner-box">
                            <div class="icon"><i class="fas fa-tachometer-alt"></i></div>
                            <h5><?php echo e(__('messages.features.fast_processing')); ?></h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 col-xs-6 col-6">
                    <div class="feature-block-one">
                        <div class="inner-box">
                            <div class="icon"><i class="fas fa-globe-europe"></i></div>
                            <h5><?php echo e(__('messages.features.eu_wide_valid')); ?></h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 col-xs-6 col-6">
                    <div class="feature-block-one">
                        <div class="inner-box">
                            <div class="icon"><i class="fas fa-headset"></i></div>
                            <h5><?php echo e(__('messages.features.best_support')); ?></h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 col-xs-6 col-6">
                    <div class="feature-block-one">
                        <div class="inner-box">
                            <div class="icon"><i class="fas fa-shield-alt"></i></div>
                            <h5><?php echo e(__('messages.features.secure_payment')); ?></h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 col-xs-6 col-6">
                    <div class="feature-block-one">
                        <div class="inner-box">
                            <div class="icon"><i class="fas fa-file-alt"></i></div>
                            <h5><?php echo e(__('messages.features.documentation')); ?></h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section Two -->
<section class="about-section-two">
    <div class="auto-container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="content-box wow fadeInUp" data-wow-delay="200ms" data-wow-duration="1200ms">
                    <div class="sec-title">
                        <div class="sub-title"><?php echo e(__('messages.about.subtitle')); ?></div>
                        <h2><?php echo e(__('messages.about.title')); ?></h2>
                        <div class="text"><?php echo e(__('messages.about.description')); ?></div>
                    </div>
                    <div class="author-info">
                        <div class="wrapper-box">
                            <h2><?php echo e(__('messages.about.expert_title')); ?></h2>
                            <div class="designation"><?php echo e(__('messages.about.expert_role')); ?></div>
                            <div class="text"><?php echo e(__('messages.about.expert_description')); ?></div>
                        </div>
                        <div class="signature"><img src="<?php echo e(asset('images/resource/signature-2.png')); ?>" alt=""></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="image-wrapper wow fadeInUp" data-wow-delay="200ms" data-wow-duration="1200ms">
                    <div class="image-one">
                        <img class="lazy-image owl-lazy" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/image-10.jpg')); ?>" alt="">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Countries Section -->
<section id="countries" class="services-section">
    <div class="auto-container">
        <div class="sec-title text-center">
            <div class="sub-title"><?php echo e(__('messages.countries.subtitle')); ?></div>
            <h2><?php echo e(__('messages.countries.title')); ?></h2>
            <div class="text"><?php echo e(__('messages.countries.description')); ?></div>
        </div>
        <div class="row">
            <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4 col-md-6 col-sm-12 service-block">
                <div class="service-block-one wow fadeInUp" data-wow-delay="200ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="icon">
                            <img src="<?php echo e(asset('images/flags/' . $country->code . '.webp')); ?>" alt="<?php echo e($country->name); ?> Flag" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">
                        </div>
                        <h3><a href="<?php echo e(localized_route('country.show', $country)); ?>"><?php echo e($country->name); ?> License</a></h3>
                        <div class="text">Get your authentic <?php echo e($country->name); ?> driving license with fast processing in <?php echo e($country->processing_time); ?>.</div>
                        <div class="price-info" style="margin: 15px 0;">
                            <span class="price-tag">€<?php echo e(number_format($country->base_price, 0)); ?></span>
                            <span class="processing-time"><?php echo e($country->processing_time); ?></span>
                        </div>
                        <div class="btn-box">
                            <a href="<?php echo e(localized_route('country.buy', $country)); ?>" class="theme-btn btn-style-one"><span class="btn-title"><?php echo e(__('messages.countries.buy_now')); ?></span></a>
                            <a href="<?php echo e(localized_route('country.show', $country)); ?>" class="read-more"><?php echo e(__('messages.countries.learn_more')); ?> <i class="flaticon-right-arrow"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<!-- Irish Driving Licence Featured Section -->
<section class="irish-featured-section" style="background: linear-gradient(135deg, #009639 0%, #ffffff 50%, #ff7900 100%); padding: 80px 0;">
    <div class="auto-container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="content-box">
                    <div class="sec-title">
                        <div class="sub-title" style="color: #009639;">🇮🇪 <?php echo e(__('messages.irish.special_service')); ?></div>
                        <h2 style="color: #000;"><?php echo e(__('messages.irish.homepage_title')); ?></h2>
                        <div class="text" style="color: #333;"><?php echo e(__('messages.irish.homepage_description')); ?></div>
                    </div>
                    <div class="features-list" style="margin: 30px 0;">
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 10px 0; color: #009639;"><i class="fas fa-check-circle" style="margin-right: 10px;"></i> <strong><?php echo e(__('messages.irish.feature_1')); ?></strong> - <?php echo e(__('messages.irish.feature_1_desc')); ?></li>
                            <li style="margin: 10px 0; color: #009639;"><i class="fas fa-check-circle" style="margin-right: 10px;"></i> <strong><?php echo e(__('messages.irish.feature_2')); ?></strong> - <?php echo e(__('messages.irish.feature_2_desc')); ?></li>
                            <li style="margin: 10px 0; color: #009639;"><i class="fas fa-check-circle" style="margin-right: 10px;"></i> <strong><?php echo e(__('messages.irish.feature_3')); ?></strong> - <?php echo e(__('messages.irish.feature_3_desc')); ?></li>
                            <li style="margin: 10px 0; color: #009639;"><i class="fas fa-check-circle" style="margin-right: 10px;"></i> <strong><?php echo e(__('messages.irish.feature_4')); ?></strong> - <?php echo e(__('messages.irish.feature_4_desc')); ?></li>
                        </ul>
                    </div>
                    <div class="btn-box">
                        <a href="<?php echo e(localized_route('irish-licence')); ?>" class="theme-btn btn-style-one" style="background: #009639; border-color: #009639;">
                            <span class="btn-title"><?php echo e(__('messages.irish.learn_more_btn')); ?></span>
                        </a>
                        <a href="<?php echo e(localized_route('irish-licence.buy')); ?>" class="theme-btn btn-style-two" style="color: #009639; border-color: #009639;">
                            <span class="btn-title"><?php echo e(__('messages.irish.order_now_btn')); ?></span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="image-block text-center">
                    <img src="<?php echo e(asset('images/irish-driving-licence-featured.webp')); ?>" alt="<?php echo e(__('messages.irish.image_alt')); ?>" style="max-width: 100%; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <div class="service-highlights" style="margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.9); border-radius: 10px;">
                        <h4 style="color: #009639; margin-bottom: 15px;"><?php echo e(__('messages.irish.why_choose_us')); ?></h4>
                        <div class="highlights-grid" style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                            <span class="highlight-tag" style="background: #009639; color: white; padding: 8px 15px; border-radius: 20px; font-size: 13px; font-weight: 500;"><?php echo e(__('messages.irish.highlight_1')); ?></span>
                            <span class="highlight-tag" style="background: #ff7900; color: white; padding: 8px 15px; border-radius: 20px; font-size: 13px; font-weight: 500;"><?php echo e(__('messages.irish.highlight_2')); ?></span>
                            <span class="highlight-tag" style="background: #009639; color: white; padding: 8px 15px; border-radius: 20px; font-size: 13px; font-weight: 500;"><?php echo e(__('messages.irish.highlight_3')); ?></span>
                            <span class="highlight-tag" style="background: #ff7900; color: white; padding: 8px 15px; border-radius: 20px; font-size: 13px; font-weight: 500;"><?php echo e(__('messages.irish.highlight_4')); ?></span>
                        </div>
                        <div class="trust-indicators" style="margin-top: 15px; text-align: center;">
                            <small style="color: #666; font-size: 12px;"><?php echo e(__('messages.irish.trust_message')); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Counter Section -->
<section class="counter-section">
    <div class="image-layer" style="background-image: url(<?php echo e(asset('images/background/bg-4.jpg')); ?>);"></div>
    <div class="auto-container">
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-12 counter-block">
                <div class="counter-block-one wow slideInUp" data-wow-delay="200ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="icon"><span class="flaticon-team"></span></div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="5000">0</span><span>+</span>
                        </div>
                        <h3><?php echo e(__('messages.counter.happy_customers')); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12 counter-block">
                <div class="counter-block-one wow slideInUp" data-wow-delay="400ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="icon"><span class="flaticon-world"></span></div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="27">0</span>
                        </div>
                        <h3><?php echo e(__('messages.counter.eu_countries')); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12 counter-block">
                <div class="counter-block-one wow slideInUp" data-wow-delay="600ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="icon"><span class="flaticon-notebook"></span></div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="99">0</span><span>%</span>
                        </div>
                        <h3><?php echo e(__('messages.counter.success_rate')); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12 counter-block">
                <div class="counter-block-one wow slideInUp" data-wow-delay="800ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="icon"><span class="flaticon-money"></span></div>
                        <div class="count-outer count-box">
                            <span class="count-text" data-speed="3000" data-stop="7">0</span>
                        </div>
                        <h3><?php echo e(__('messages.counter.days_average')); ?></h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonial Section -->
<section class="testimonial-section style-two">
    <div class="image-layer" style="background-image: url(<?php echo e(asset('images/background/bg-5.jpg')); ?>);"></div>
    <div class="auto-container">
        <div class="sec-title text-center">
            <div class="sub-title"><?php echo e(__('messages.testimonials.subtitle')); ?></div>
            <h2><?php echo e(__('messages.testimonials.title')); ?></h2>
            <div class="text"><?php echo e(__('messages.testimonials.description')); ?></div>
        </div>
        <div class="testimonial-carousel owl-carousel owl-theme" data-options='{"loop": true, "margin": 30, "autoheight":true, "lazyload":true, "nav": true, "dots": true, "autoplay": true, "autoplayTimeout": 6000, "smartSpeed": 300, "responsive":{ "0" :{ "items": "1" }, "768" :{ "items" : "2" } , "1000":{ "items" : "3" }}}'>

            <?php $__empty_1 = true; $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="testimonial-block">
                <div class="inner-box">
                    <div class="quote"><span class="flaticon-quote"></span></div>
                    <div class="rating">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <i class="fa fa-star<?php echo e($i <= $testimonial->rating ? '' : '-o'); ?>"></i>
                        <?php endfor; ?>
                    </div>
                    <div class="text">"<?php echo e($testimonial->content); ?>"</div>
                    <div class="author-info">
                        <div class="image">
                            <img src="<?php echo e($testimonial->image_url); ?>" alt="<?php echo e($testimonial->name); ?>">
                        </div>
                        <h3><?php echo e($testimonial->name); ?></h3>
                        <div class="designation"><?php echo e($testimonial->location); ?></div>
                        <div class="license-type"><?php echo e($testimonial->license_type); ?></div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <!-- Fallback testimonials if none exist in database -->
            <div class="testimonial-block">
                <div class="inner-box">
                    <div class="quote"><span class="flaticon-quote"></span></div>
                    <div class="rating">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                    </div>
                    <div class="text">"Excellent service! Got my EU driving license quickly and legally. The process was smooth and professional. Highly recommended!"</div>
                    <div class="author-info">
                        <div class="image"><img src="<?php echo e(asset('images/resource/author-1.jpg')); ?>" alt="Sample Customer"></div>
                        <h3>Sample Customer</h3>
                        <div class="designation">Europe</div>
                        <div class="license-type">Category B License</div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- News Section -->
<section class="news-section">
    <div class="auto-container">
        <div class="sec-title text-center">
            <div class="sub-title"><?php echo e(__('messages.news.subtitle')); ?></div>
            <h2><?php echo e(__('messages.news.title')); ?></h2>
            <div class="text"><?php echo e(__('messages.news.description')); ?></div>
        </div>
        <div class="row">
            <!-- News Block 1 -->
            <div class="col-lg-4 col-md-6 col-sm-12 news-block">
                <div class="news-block-one wow fadeInUp" data-wow-delay="200ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/news-1.jpg')); ?>" alt="EU Driving License Regulations 2024">
                            <div class="category">Regulations</div>
                        </div>
                        <div class="lower-content">
                            <div class="post-meta">
                                <ul>
                                    <li><i class="far fa-calendar"></i>December 15, 2024</li>
                                    <li><i class="far fa-user"></i>EU License Team</li>
                                </ul>
                            </div>
                            <h3><a href="<?php echo e(url('/news/eu-driving-license-regulations-2024')); ?>">New EU Driving License Regulations for 2024</a></h3>
                            <div class="text">The European Union has updated driving license regulations for 2024, introducing new security features and streamlined processes for license validation across all member states.</div>
                            <div class="link-btn">
                                <a href="<?php echo e(url('/news/eu-driving-license-regulations-2024')); ?>">Read More <i class="flaticon-right-arrow"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Block 2 -->
            <div class="col-lg-4 col-md-6 col-sm-12 news-block">
                <div class="news-block-one wow fadeInUp" data-wow-delay="400ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/news-2.jpg')); ?>" alt="Digital Driving Licenses EU">
                            <div class="category">Technology</div>
                        </div>
                        <div class="lower-content">
                            <div class="post-meta">
                                <ul>
                                    <li><i class="far fa-calendar"></i>December 10, 2024</li>
                                    <li><i class="far fa-user"></i>Tech Updates</li>
                                </ul>
                            </div>
                            <h3><a href="<?php echo e(url('/news/digital-driving-licenses-eu')); ?>">Digital Driving Licenses Coming to EU</a></h3>
                            <div class="text">The European Union is preparing to introduce digital driving licenses that will be stored on smartphones and accepted across all member countries by 2025.</div>
                            <div class="link-btn">
                                <a href="<?php echo e(url('/news/digital-driving-licenses-eu')); ?>">Read More <i class="flaticon-right-arrow"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Block 3 -->
            <div class="col-lg-4 col-md-6 col-sm-12 news-block">
                <div class="news-block-one wow fadeInUp" data-wow-delay="600ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/news-3.jpg')); ?>" alt="Fast Track License Processing">
                            <div class="category">Service Update</div>
                        </div>
                        <div class="lower-content">
                            <div class="post-meta">
                                <ul>
                                    <li><i class="far fa-calendar"></i>December 5, 2024</li>
                                    <li><i class="far fa-user"></i>Service Team</li>
                                </ul>
                            </div>
                            <h3><a href="<?php echo e(url('/news/fast-track-license-processing')); ?>">Fast-Track Processing Now Available</a></h3>
                            <div class="text">We're excited to announce our new fast-track processing service, reducing delivery times to just 5-7 days for urgent driving license applications.</div>
                            <div class="link-btn">
                                <a href="<?php echo e(url('/news/fast-track-license-processing')); ?>">Read More <i class="flaticon-right-arrow"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Block 4 -->
            <div class="col-lg-4 col-md-6 col-sm-12 news-block">
                <div class="news-block-one wow fadeInUp" data-wow-delay="200ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/news-4.jpg')); ?>" alt="Brexit Driving License Impact">
                            <div class="category">Brexit</div>
                        </div>
                        <div class="lower-content">
                            <div class="post-meta">
                                <ul>
                                    <li><i class="far fa-calendar"></i>November 28, 2024</li>
                                    <li><i class="far fa-user"></i>Legal Team</li>
                                </ul>
                            </div>
                            <h3><a href="<?php echo e(url('/news/brexit-driving-license-impact')); ?>">Brexit Impact on UK Driving Licenses</a></h3>
                            <div class="text">Understanding how Brexit affects UK driving license validity in EU countries and the new requirements for British drivers traveling in Europe.</div>
                            <div class="link-btn">
                                <a href="<?php echo e(url('/news/brexit-driving-license-impact')); ?>">Read More <i class="flaticon-right-arrow"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Block 5 -->
            <div class="col-lg-4 col-md-6 col-sm-12 news-block">
                <div class="news-block-one wow fadeInUp" data-wow-delay="400ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/news-5.jpg')); ?>" alt="Customer Success Stories">
                            <div class="category">Success Stories</div>
                        </div>
                        <div class="lower-content">
                            <div class="post-meta">
                                <ul>
                                    <li><i class="far fa-calendar"></i>November 20, 2024</li>
                                    <li><i class="far fa-user"></i>Customer Stories</li>
                                </ul>
                            </div>
                            <h3><a href="<?php echo e(url('/news/customer-success-stories')); ?>">5000+ Successful License Deliveries</a></h3>
                            <div class="text">Celebrating our milestone of 5,000 successful driving license deliveries across Europe with stories from our satisfied customers.</div>
                            <div class="link-btn">
                                <a href="<?php echo e(url('/news/customer-success-stories')); ?>">Read More <i class="flaticon-right-arrow"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Block 6 -->
            <div class="col-lg-4 col-md-6 col-sm-12 news-block">
                <div class="news-block-one wow fadeInUp" data-wow-delay="600ms" data-wow-duration="1200ms">
                    <div class="inner-box">
                        <div class="image">
                            <img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/resource/news-6.jpg')); ?>" alt="Security Features EU Licenses">
                            <div class="category">Security</div>
                        </div>
                        <div class="lower-content">
                            <div class="post-meta">
                                <ul>
                                    <li><i class="far fa-calendar"></i>November 15, 2024</li>
                                    <li><i class="far fa-user"></i>Security Team</li>
                                </ul>
                            </div>
                            <h3><a href="<?php echo e(url('/news/security-features-eu-licenses')); ?>">Enhanced Security Features in EU Licenses</a></h3>
                            <div class="text">Learn about the advanced security features in modern EU driving licenses that prevent counterfeiting and ensure authenticity.</div>
                            <div class="link-btn">
                                <a href="<?php echo e(url('/news/security-features-eu-licenses')); ?>">Read More <i class="flaticon-right-arrow"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- View All News Button -->
        <div class="text-center" style="margin-top: 50px;">
            <a href="<?php echo e(url('/news')); ?>" class="theme-btn btn-style-one">
                <span class="btn-title"><?php echo e(__('messages.news.view_all')); ?></span>
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="auto-container">
        <div class="wrapper-box">
            <div class="content-box">
                <h2><?php echo e(__('messages.cta.title')); ?></h2>
                <div class="text"><?php echo e(__('messages.cta.description')); ?></div>
            </div>
            <div class="btn-box">
                <a href="<?php echo e(localized_route('apply')); ?>" class="theme-btn btn-style-one"><span class="btn-title"><?php echo e(__('messages.cta.button')); ?></span></a>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\eu-drivinglicence\resources\views/home/<USER>/ ?>