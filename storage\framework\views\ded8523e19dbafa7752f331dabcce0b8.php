<?php
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;
?>
<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <title><?php echo e($seoData['title'] ?? config('app.name')); ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo e($seoData['description'] ?? __('messages.seo.default_description')); ?>">
    <meta name="keywords" content="<?php echo e($seoData['keywords'] ?? __('messages.seo.default_keywords')); ?>">
    <link rel="canonical" href="<?php echo e($seoData['canonical'] ?? url()->current()); ?>">

    <!-- Hreflang Tags for International SEO -->
    <?php $__currentLoopData = hreflang_urls(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locale => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <link rel="alternate" hreflang="<?php echo e($locale); ?>" href="<?php echo e($url); ?>">
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <link rel="alternate" hreflang="x-default" href="<?php echo e(hreflang_urls()['en'] ?? url('/en')); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo e($seoData['og_title'] ?? $seoData['title'] ?? config('app.name')); ?>">
    <meta property="og:description" content="<?php echo e($seoData['og_description'] ?? $seoData['description'] ?? ''); ?>">
    <meta property="og:image" content="<?php echo e($seoData['og_image'] ?? asset('images/default-og.webp')); ?>">
    <meta property="og:url" content="<?php echo e($seoData['canonical'] ?? url()->current()); ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?php echo e(config('app.name')); ?>">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo e($seoData['og_title'] ?? $seoData['title'] ?? config('app.name')); ?>">
    <meta name="twitter:description" content="<?php echo e($seoData['og_description'] ?? $seoData['description'] ?? ''); ?>">
    <meta name="twitter:image" content="<?php echo e($seoData['og_image'] ?? asset('images/default-og.webp')); ?>">

    <!-- Stylesheets -->
    <link href="<?php echo e(asset('css/bootstrap.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/style.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/responsive.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/color-2.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/flaticon.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('css/eu-custom.css')); ?>" rel="stylesheet">

    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900&display=swap" rel="stylesheet">

    <!-- Theme Color -->
    <meta name="theme-color" content="#1e40af">

    <!-- Analytics & Tracking -->
    <?php if (isset($component)) { $__componentOriginalea2dce4014fb9bd77432d0a2cf5dc69b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalea2dce4014fb9bd77432d0a2cf5dc69b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.analytics','data' => ['page' => $page ?? 'home','country' => $country ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('analytics'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['page' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($page ?? 'home'),'country' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($country ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalea2dce4014fb9bd77432d0a2cf5dc69b)): ?>
<?php $attributes = $__attributesOriginalea2dce4014fb9bd77432d0a2cf5dc69b; ?>
<?php unset($__attributesOriginalea2dce4014fb9bd77432d0a2cf5dc69b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalea2dce4014fb9bd77432d0a2cf5dc69b)): ?>
<?php $component = $__componentOriginalea2dce4014fb9bd77432d0a2cf5dc69b; ?>
<?php unset($__componentOriginalea2dce4014fb9bd77432d0a2cf5dc69b); ?>
<?php endif; ?>

    <!-- Favicon and App Icons -->
    <link rel="shortcut icon" href="<?php echo e(asset('images/favicon-2.png')); ?>" type="image/x-icon">
    <link rel="icon" href="<?php echo e(asset('images/favicon-2.png')); ?>" type="image/x-icon">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('images/icons/apple-touch-icon.png')); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('images/icons/favicon-32x32.png')); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('images/icons/favicon-16x16.png')); ?>">
    <link rel="mask-icon" href="<?php echo e(asset('images/icons/safari-pinned-tab.svg')); ?>" color="#1e40af">

    <!-- Responsive -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Schema.org JSON-LD -->
    <?php if(isset($schemaMarkup)): ?>
        <script type="application/ld+json">
            <?php echo json_encode($schemaMarkup); ?>

        </script>
    <?php endif; ?>

    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>

    <!-- Additional Head Content -->
    <?php echo $__env->yieldContent('head'); ?>
</head>
<body>
    <?php
    use App\Services\AnalyticsService;
    $analyticsService = new AnalyticsService();
    $gtmCode = $analyticsService->getGTMCode();
    ?>

    
    <?php if($gtmCode && isset($gtmCode['body'])): ?>
        <?php echo $gtmCode['body']; ?>

    <?php endif; ?>

<div class="page-wrapper">
    <!-- Preloader -->
    <div class="loader-wrap style-two">
        <div class="preloader"><div class="preloader-close">Preloader Close</div></div>
        <div class="layer layer-one"><span class="overlay"></span></div>
        <div class="layer layer-two"><span class="overlay"></span></div>
        <div class="layer layer-three"><span class="overlay"></span></div>
    </div>

    <!-- Main Header -->
    <header class="main-header header-style-two">
        <!-- Header Top Two -->
        <div class="header-top-two">
            <div class="auto-container">
                <div class="inner">
                    <div class="top-left">
                        <ul class="contact-info">
                            <li><a href="mailto:<?php echo e(__('messages.contact.email')); ?>"><i class="far fa-envelope"></i><?php echo e(__('messages.contact.email')); ?></a></li>
                            <li><a href="tel:<?php echo e(__('messages.contact.phone_link')); ?>"><i class="far fa-phone"></i><?php echo e(__('messages.contact.phone')); ?></a></li>
                        </ul>
                    </div>

                    <div class="top-middile">
                        <div class="text"><?php echo e(__('messages.header.welcome')); ?></div>
                    </div>

                    <div class="top-right">
                        <?php echo $__env->make('components.language-switcher-styled', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <ul class="social-links clearfix">
                            <li><a href="https://facebook.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-facebook-f"></span></a></li>
                            <li><a href="https://twitter.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-twitter"></span></a></li>
                            <li><a href="https://instagram.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-instagram"></span></a></li>
                            <li><a href="https://linkedin.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-linkedin"></span></a></li>
                            <li><a href="https://youtube.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-youtube"></span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header Upper -->
        <div class="header-upper">
            <div class="auto-container">
                <div class="inner-container">
                    <!--Nav Box-->
                    <div class="nav-outer clearfix">
                        <!--Logo-->
                        <div class="logo-box">
                            <div class="logo"><a href="<?php echo e(url('/')); ?>"><img src="<?php echo e(asset('images/logo-2.png')); ?>" alt="<?php echo e(config('app.name')); ?>"></a></div>
                        </div>
                        <!--Mobile Navigation Toggler-->
                        <div class="mobile-nav-toggler"><span class="icon fas fa-bars"></span></div>

                        <!-- Main Menu -->
                        <nav class="main-menu navbar-expand-md navbar-light">
                            <div class="collapse navbar-collapse show clearfix" id="navbarSupportedContent">
                                <ul class="navigation clearfix">
                                    <li><a href="<?php echo e(localized_route('home')); ?>"><?php echo e(__('messages.nav.home')); ?></a></li>
                                    <li><a href="<?php echo e(localized_route('about')); ?>"><?php echo e(__('messages.nav.about')); ?></a></li>
                                    <li class="dropdown"><a href="#"><?php echo e(__('messages.nav.countries')); ?></a>
                                        <ul>
                                            <?php $__currentLoopData = $countries ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><a href="<?php echo e(localized_route('country.show', $country)); ?>"><?php echo e($country->name); ?></a></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </li>
                                    <li class="dropdown"><a href="<?php echo e(localized_route('irish-licence')); ?>" style="color: #009639;">🇮🇪 Irish Licence</a>
                                        <ul>
                                            <li><a href="<?php echo e(localized_route('irish-licence')); ?>">Irish Licence Info</a></li>
                                            <li><a href="<?php echo e(localized_route('irish-licence.buy')); ?>">Buy Irish Licence</a></li>
                                            <li><a href="<?php echo e(localized_route('irish-licence.requirements')); ?>">Requirements</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="<?php echo e(localized_route('how-it-works')); ?>"><?php echo e(__('messages.nav.how_it_works')); ?></a></li>
                                    <li><a href="<?php echo e(localized_route('pricing')); ?>"><?php echo e(__('messages.nav.pricing')); ?></a></li>
                                    <li><a href="<?php echo e(localized_route('contact')); ?>"><?php echo e(__('messages.nav.contact')); ?></a></li>
                                </ul>
                            </div>
                        </nav>
                        <!-- Main Menu End-->

                        <!-- Link Btn-->
                        <div class="link-btn"><a href="<?php echo e(localized_route('apply')); ?>" class="theme-btn btn-style-one"><span class="btn-title"><?php echo e(__('messages.header.get_license_now')); ?></span></a></div>
                    </div>
                </div>
            </div>
        </div>
        <!--End Header Upper-->

        <!-- Sticky Header  -->
        <div class="sticky-header">
            <div class="auto-container clearfix">
                <!--Logo-->
                <div class="logo pull-left">
                    <a href="<?php echo e(url('/')); ?>" title=""><img src="<?php echo e(asset('images/sticky-logo-2.png')); ?>" alt="" title=""></a>
                </div>
                <!--Right Col-->
                <div class="pull-right">
                    <!-- Main Menu -->
                    <nav class="main-menu clearfix">
                        <!--Keep This Empty / Menu will come through Javascript-->
                    </nav><!-- Main Menu End-->
                </div>
            </div>
        </div><!-- End Sticky Menu -->

        <!-- Mobile Menu  -->
        <div class="mobile-menu">
            <div class="menu-backdrop"></div>
            <div class="close-btn"><span class="icon fas fa-times"></span></div>

            <nav class="menu-box">
                <div class="nav-logo"><a href="<?php echo e(url('/')); ?>"><img src="<?php echo e(asset('images/logo.png')); ?>" alt="" title=""></a></div>
                <div class="menu-outer"><!--Here Menu Will Come Automatically Via Javascript / Same Menu as in Header--></div>
                <!--Social Links-->
                <div class="social-links">
                    <ul class="clearfix">
                        <li><a href="https://twitter.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-twitter"></span></a></li>
                        <li><a href="https://facebook.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-facebook-square"></span></a></li>
                        <li><a href="https://instagram.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-instagram"></span></a></li>
                        <li><a href="https://linkedin.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-linkedin"></span></a></li>
                        <li><a href="https://youtube.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-youtube"></span></a></li>
                    </ul>
                </div>
            </nav>
        </div><!-- End Mobile Menu -->
    </header>
    <!-- End Main Header -->

    <!--Search Popup-->
    <div id="search-popup" class="search-popup">
        <div class="close-search theme-btn"><span><i class="fas fa-times"></i></span></div>
        <div class="popup-inner">
            <div class="overlay-layer"></div>
            <div class="search-form">
                <form method="post" action="<?php echo e(url('/')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="form-group">
                        <fieldset>
                            <input type="search" class="form-control" name="search-input" value="" placeholder="Search Here" required >
                            <input type="submit" value="Search Now!" class="theme-btn">
                        </fieldset>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <?php echo $__env->yieldContent('content'); ?>

    <!-- Scroll To Top -->
    <div class="scroll-to-top scroll-to-target" data-target="html">
        <i class="fas fa-angle-up"></i>
    </div>

    <!-- Main Footer -->
    <footer class="main-footer">
        <div class="auto-container">
            <!--Widgets Section-->
            <div class="widgets-section">
                <div class="row clearfix">

                    <!--Column-->
                    <div class="column col-lg-4">
                        <div class="footer-widget logo-widget">
                            <div class="widget-content">
                                <div class="footer-logo">
                                    <a href="<?php echo e(url('/')); ?>"><img class="lazy-image" src="<?php echo e(asset('images/resource/image-spacer-for-validation.png')); ?>" data-src="<?php echo e(asset('images/logo.webp')); ?>" alt="<?php echo e(config('app.name')); ?> - EU Driving License" /></a>
                                </div>
                                <div class="text"><?php echo e(__('messages.footer.company_description')); ?></div>
                                <ul class="social-links clearfix">
                                    <li><a href="https://facebook.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-facebook-f"></span></a></li>
                                    <li><a href="https://twitter.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-twitter"></span></a></li>
                                    <li><a href="https://instagram.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-instagram"></span></a></li>
                                    <li><a href="https://linkedin.com" target="_blank" rel="noopener noreferrer"><span class="fab fa-linkedin"></span></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!--Column-->
                    <div class="column col-lg-4">
                        <div class="footer-widget links-widget">
                            <div class="widget-content">
                                <h3><?php echo e(__('messages.footer.quick_links')); ?></h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul>
                                            <li><a href="<?php echo e(localized_route('home')); ?>"><?php echo e(__('messages.nav.home')); ?></a></li>
                                            <li><a href="<?php echo e(localized_route('about')); ?>"><?php echo e(__('messages.nav.about')); ?></a></li>
                                            <li><a href="<?php echo e(localized_route('how-it-works')); ?>"><?php echo e(__('messages.nav.how_it_works')); ?></a></li>
                                            <li><a href="<?php echo e(localized_route('pricing')); ?>"><?php echo e(__('messages.nav.pricing')); ?></a></li>
                                            <li><a href="<?php echo e(localized_route('contact')); ?>"><?php echo e(__('messages.nav.contact')); ?></a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul>
                                            <li><a href="<?php echo e(localized_route('privacy')); ?>"><?php echo e(__('messages.footer.privacy_policy')); ?></a></li>
                                            <li><a href="<?php echo e(localized_route('terms')); ?>"><?php echo e(__('messages.footer.terms_of_service')); ?></a></li>
                                            <li><a href="<?php echo e(url('/faq')); ?>"><?php echo e(__('messages.footer.faq')); ?></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--Column-->
                    <div class="column col-lg-4">
                        <div class="footer-widget contact-widget">
                            <div class="widget-content">
                                <h3><?php echo e(__('messages.footer.contact_info')); ?></h3>
                                <ul class="contact-info-list">
                                    <li><i class="far fa-envelope"></i><?php echo e(__('messages.contact.email')); ?></li>
                                    <li><i class="far fa-phone"></i><?php echo e(__('messages.contact.phone')); ?></li>
                                    <li><i class="far fa-clock"></i><?php echo e(__('messages.footer.support_hours')); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!--Footer Bottom-->
        <div class="footer-bottom">
            <div class="auto-container">
                <div class="inner clearfix">
                    <div class="copyright">&copy; <?php echo e(date('Y')); ?> <?php echo e(config('app.name')); ?>. <?php echo e(__('messages.footer.all_rights_reserved')); ?></div>
                </div>
            </div>
        </div>
    </footer>

</div>

<!-- Scripts -->
<script src="<?php echo e(asset('js/jquery.js')); ?>"></script>
<script src="<?php echo e(asset('js/popper.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/bootstrap.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/bootstrap-select.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/jquery.fancybox.js')); ?>"></script>
<script src="<?php echo e(asset('js/isotope.js')); ?>"></script>
<script src="<?php echo e(asset('js/owl.js')); ?>"></script>
<script src="<?php echo e(asset('js/appear.js')); ?>"></script>
<script src="<?php echo e(asset('js/wow.js')); ?>"></script>
<script src="<?php echo e(asset('js/lazyload.js')); ?>"></script>
<script src="<?php echo e(asset('js/scrollbar.js')); ?>"></script>
<script src="<?php echo e(asset('js/TweenMax.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/jquery-ui.js')); ?>"></script>
<script src="<?php echo e(asset('js/script.js')); ?>"></script>
<script src="<?php echo e(asset('js/eu-custom.js')); ?>"></script>

<!-- File Upload Script -->
<script src="<?php echo e(asset('js/file-upload.js')); ?>"></script>

<?php echo $__env->yieldPushContent('scripts'); ?>

</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\Web Devs\eu-drivinglicence\resources\views/layouts/app.blade.php ENDPATH**/ ?>