<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Country;
use Illuminate\Support\Facades\App;

echo "🔗 Testing Route Generation\n";
echo str_repeat("=", 40) . "\n";

$country = Country::where('code', 'DE')->first();

if ($country) {
    echo "Testing with Germany (Code: DE)\n";
    echo "Country ID: {$country->id}\n";
    echo "Country Slug: {$country->slug}\n\n";
    
    // Test in different locales
    $locales = ['en', 'de', 'fr'];
    
    foreach ($locales as $locale) {
        App::setLocale($locale);
        echo "🌍 Locale: {$locale}\n";
        echo "Country Name: {$country->name}\n";
        echo "Country Slug: {$country->slug}\n";
        
        try {
            $showUrl = route('country.show', $country);
            $buyUrl = route('country.buy', $country);
            
            echo "✅ Show URL: {$showUrl}\n";
            echo "✅ Buy URL: {$buyUrl}\n";
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
} else {
    echo "❌ Germany not found in database!\n";
}

echo "🎯 Testing all countries:\n";
$countries = Country::all();

foreach ($countries as $country) {
    App::setLocale('en');
    echo "- {$country->name} (slug: {$country->slug})\n";
    
    try {
        $url = route('country.show', $country);
        echo "  ✅ URL: {$url}\n";
    } catch (Exception $e) {
        echo "  ❌ Error: " . $e->getMessage() . "\n";
    }
}
