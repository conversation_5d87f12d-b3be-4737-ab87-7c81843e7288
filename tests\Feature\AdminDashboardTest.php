<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Application;
use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test country
        $country = Country::create([
            'name' => 'Germany',
            'code' => 'DE',
            'flag' => '🇩🇪',
            'price' => 299.00,
            'processing_time' => '3-5 business days',
            'is_active' => true,
        ]);

        // Create country translation to avoid constraint violation
        $country->translations()->create([
            'locale' => 'en',
            'name' => 'Germany',
            'slug' => 'germany',
        ]);
    }

    public function test_admin_can_access_dashboard()
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => true,
        ]);

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
        $response->assertSee('Dashboard');
        $response->assertSee('Total Applications');
        $response->assertSee('Pending Applications');
    }

    public function test_non_admin_cannot_access_dashboard()
    {
        $user = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => false,
        ]);

        $response = $this->actingAs($user)->get('/admin');

        $response->assertStatus(403);
    }

    public function test_dashboard_displays_correct_stats()
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => true,
        ]);
        $country = Country::first();

        // Create test applications - simplified without factory
        for ($i = 0; $i < 5; $i++) {
            Application::create([
                'application_number' => 'APP-2025-' . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                'country_id' => $country->id,
                'first_name' => 'Test',
                'last_name' => 'User' . $i,
                'email' => 'test' . $i . '@example.com',
                'application_status' => 'submitted',
                'payment_status' => 'completed',
                'total_amount' => 299.00,
            ]);
        }

        for ($i = 5; $i < 8; $i++) {
            Application::create([
                'application_number' => 'APP-2025-' . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                'country_id' => $country->id,
                'first_name' => 'Test',
                'last_name' => 'User' . $i,
                'email' => 'test' . $i . '@example.com',
                'application_status' => 'processing',
                'payment_status' => 'completed',
                'total_amount' => 399.00,
            ]);
        }

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
        $response->assertSee('8'); // Total applications
        $response->assertSee('5'); // Pending applications (submitted status)
        $response->assertSee('3'); // Processing applications
    }

    public function test_dashboard_shows_recent_applications()
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => true,
        ]);
        $country = Country::first();

        Application::create([
            'application_number' => 'APP-2025-000001',
            'country_id' => $country->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'application_status' => 'submitted',
            'payment_status' => 'pending',
            'total_amount' => 299.00,
        ]);

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
        $response->assertSee('John Doe');
        $response->assertSee('APP-2025-000001');
    }

    public function test_guest_redirected_to_login()
    {
        $response = $this->get('/admin');

        $response->assertRedirect('/admin/login');
    }
}
