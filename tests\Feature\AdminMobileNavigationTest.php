<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminMobileNavigationTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_dashboard_has_mobile_navigation_elements()
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => true,
        ]);

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
        
        // Check for mobile menu button
        $response->assertSee('sidebar-toggle');
        $response->assertSee('fas fa-bars');
        
        // Check for mobile overlay
        $response->assertSee('admin-sidebar-overlay');
        
        // Check for close button in sidebar
        $response->assertSee('fas fa-times');
        
        // Check for JavaScript functions
        $response->assertSee('toggleSidebar()');
        $response->assertSee('closeSidebar()');
    }

    public function test_mobile_navigation_javascript_functions_exist()
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => true,
        ]);

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
        
        // Check for JavaScript functions
        $response->assertSee('function toggleSidebar()');
        $response->assertSee('function closeSidebar()');
        $response->assertSee('admin-sidebar-open');
        
        // Check for event listeners
        $response->assertSee('addEventListener');
        $response->assertSee('keydown');
    }

    public function test_mobile_navigation_css_classes_present()
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => true,
        ]);

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
        
        // Check for responsive classes
        $response->assertSee('lg:hidden');
        $response->assertSee('admin-sidebar');
        $response->assertSee('transition-transform');
        
        // Check for mobile-specific styling
        $response->assertSee('admin-sidebar-open');
        $response->assertSee('admin-sidebar-overlay');
    }
}
