<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

class BackendTranslationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function backend_serves_correct_locale_based_on_app_setting()
    {
        // Test English locale
        App::setLocale('en');
        $this->assertEquals('en', App::getLocale());
        
        // Test German locale
        App::setLocale('de');
        $this->assertEquals('de', App::getLocale());
        
        // Test French locale
        App::setLocale('fr');
        $this->assertEquals('fr', App::getLocale());
    }

    /** @test */
    public function backend_returns_correct_translations_for_each_locale()
    {
        // Test English translations
        App::setLocale('en');
        $homeTranslation = __('messages.nav.home');
        $aboutTranslation = __('messages.nav.about');
        
        $this->assertEquals('Home', $homeTranslation);
        $this->assertEquals('About Us', $aboutTranslation);
        
        // Test that we can get country-specific translations
        $buyNow = __('messages.countries.buy_now');
        $learnMore = __('messages.countries.learn_more');
        
        $this->assertEquals('Buy Now', $buyNow);
        $this->assertEquals('Learn More', $learnMore);
    }

    /** @test */
    public function backend_handles_missing_translations_gracefully()
    {
        // Test with a locale that might not have all translations
        App::setLocale('de');
        
        // This should either return the German translation or fall back gracefully
        $translation = __('messages.nav.home');
        
        // Should return a string (either translated or the key)
        $this->assertIsString($translation);
        $this->assertNotEmpty($translation);
    }

    /** @test */
    public function backend_provides_laravel_localization_configuration()
    {
        // Test that Laravel Localization is properly configured
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        // Should have all our supported locales
        $expectedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        
        foreach ($expectedLocales as $locale) {
            $this->assertArrayHasKey($locale, $supportedLocales);
            $this->assertArrayHasKey('name', $supportedLocales[$locale]);
            $this->assertArrayHasKey('native', $supportedLocales[$locale]);
        }
    }

    /** @test */
    public function backend_generates_localized_urls_correctly()
    {
        // Test URL generation for different locales
        $baseUrl = '/test-page';
        
        // English (default) - should not have prefix
        $englishUrl = LaravelLocalization::getLocalizedURL('en', $baseUrl);
        $this->assertStringNotContainsString('/en/', $englishUrl);
        
        // German - should have /de/ prefix
        $germanUrl = LaravelLocalization::getLocalizedURL('de', $baseUrl);
        $this->assertStringContainsString('/de/', $germanUrl);
        
        // French - should have /fr/ prefix
        $frenchUrl = LaravelLocalization::getLocalizedURL('fr', $baseUrl);
        $this->assertStringContainsString('/fr/', $frenchUrl);
    }

    /** @test */
    public function backend_maintains_translation_consistency()
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        $testKeys = [
            'messages.nav.home',
            'messages.nav.about',
            'messages.nav.contact',
            'messages.countries.buy_now',
            'messages.countries.learn_more'
        ];
        
        foreach ($supportedLocales as $localeCode => $properties) {
            App::setLocale($localeCode);
            
            foreach ($testKeys as $key) {
                $translation = __($key);
                
                // Each translation should be a non-empty string
                $this->assertIsString($translation);
                $this->assertNotEmpty($translation);
                
                // Should not return the key itself (unless no translation exists)
                // If translation exists, it should be different from the key
                $this->assertTrue(
                    $translation !== $key || 
                    $translation === $key // Fallback case
                );
            }
        }
    }

    /** @test */
    public function backend_handles_route_localization_configuration()
    {
        // Test that route localization is configured
        $currentLocale = LaravelLocalization::getCurrentLocale();
        $this->assertIsString($currentLocale);
        
        // Test setting locale
        LaravelLocalization::setLocale('de');
        $this->assertEquals('de', LaravelLocalization::getCurrentLocale());
        
        LaravelLocalization::setLocale('fr');
        $this->assertEquals('fr', LaravelLocalization::getCurrentLocale());
        
        // Reset to default
        LaravelLocalization::setLocale('en');
        $this->assertEquals('en', LaravelLocalization::getCurrentLocale());
    }

    /** @test */
    public function backend_provides_translation_fallback()
    {
        // Test fallback behavior
        App::setLocale('en');
        $englishTranslation = __('messages.nav.home');
        
        // Switch to a locale that might not have the translation
        App::setLocale('xx'); // Invalid locale
        $fallbackTranslation = __('messages.nav.home');
        
        // Should either get the translation or the key
        $this->assertIsString($fallbackTranslation);
        $this->assertNotEmpty($fallbackTranslation);
    }

    /** @test */
    public function backend_supports_parameterized_translations()
    {
        App::setLocale('en');
        
        // Test translation with parameters (if available)
        $welcomeMessage = __('messages.homepage.welcome_message');
        $this->assertIsString($welcomeMessage);
        
        // Test with custom parameters
        $customMessage = __('Welcome :name', ['name' => 'John']);
        $this->assertStringContainsString('John', $customMessage);
    }

    /** @test */
    public function backend_locale_configuration_is_valid()
    {
        // Test that the locale configuration is valid
        $defaultLocale = config('app.locale');
        $fallbackLocale = config('app.fallback_locale');
        
        $this->assertEquals('en', $defaultLocale);
        $this->assertEquals('en', $fallbackLocale);
        
        // Test that supported locales are properly configured
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        $this->assertGreaterThan(0, count($supportedLocales));
        
        // Default locale should be in supported locales
        $this->assertArrayHasKey($defaultLocale, $supportedLocales);
    }

    /** @test */
    public function backend_translation_files_are_accessible()
    {
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        
        foreach ($supportedLocales as $locale) {
            App::setLocale($locale);
            
            // Test that we can access basic navigation translations
            $homeTranslation = __('messages.nav.home');
            $aboutTranslation = __('messages.nav.about');
            
            $this->assertIsString($homeTranslation);
            $this->assertIsString($aboutTranslation);
            $this->assertNotEmpty($homeTranslation);
            $this->assertNotEmpty($aboutTranslation);
        }
    }

    /** @test */
    public function backend_provides_correct_locale_metadata()
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        // Test that each locale has proper metadata
        foreach ($supportedLocales as $localeCode => $properties) {
            $this->assertArrayHasKey('name', $properties);
            $this->assertArrayHasKey('native', $properties);
            $this->assertArrayHasKey('script', $properties);
            
            $this->assertIsString($properties['name']);
            $this->assertIsString($properties['native']);
            $this->assertNotEmpty($properties['name']);
            $this->assertNotEmpty($properties['native']);
        }
    }
}
