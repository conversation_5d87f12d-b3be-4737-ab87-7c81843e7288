<?php

namespace Tests\Feature;

use Tests\TestCase;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

class LanguageSwitcherCompleteTest extends TestCase
{
    /**
     * Test that all supported languages are available in the language switcher
     *
     * @test
     */
    public function it_shows_all_supported_languages_in_switcher()
    {
        $response = $this->get('/');

        $response->assertStatus(200);

        // Check that all supported languages appear in the language switcher
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];

        foreach ($supportedLocales as $locale) {
            $response->assertSee(strtoupper($locale));
        }
    }

    /**
     * Test that Laravel Localization helper functions work correctly
     *
     * @test
     */
    public function it_uses_laravel_localization_helpers_correctly()
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();

        // Verify all our expected locales are supported
        $expectedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];

        foreach ($expectedLocales as $locale) {
            $this->assertArrayHasKey($locale, $supportedLocales, "Locale {$locale} should be supported");
        }
    }


}
