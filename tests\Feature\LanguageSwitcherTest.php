<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

class LanguageSwitcherTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test country
        Country::factory()->create([
            'name' => 'Germany',
            'code' => 'DE',
            'slug' => 'germany',
            'is_active' => true,
            'sort_order' => 1
        ]);
    }

    /** @test */
    public function it_displays_language_switcher_on_homepage()
    {
        $response = $this->get('/en');

        $response->assertStatus(200);

        // Check that language switcher component is included
        $response->assertSee('language-switcher-top');

        // Check that current locale is displayed
        $response->assertSee('EN'); // Current locale should be shown
    }

    /** @test */
    public function it_displays_language_switcher_on_localized_pages()
    {
        // Test German page
        $response = $this->get('/de');
        $response->assertStatus(200);
        $response->assertSee('language-switcher-top');
        $response->assertSee('DE'); // Current locale should be shown

        // Test French page
        $response = $this->get('/fr');
        $response->assertStatus(200);
        $response->assertSee('language-switcher-top');
        $response->assertSee('FR'); // Current locale should be shown
    }

    /** @test */
    public function it_generates_correct_language_switch_urls()
    {
        // Start on English homepage
        $response = $this->get('/en');
        $response->assertStatus(200);

        // Check that the response contains JavaScript with correct URLs
        $content = $response->getContent();

        // Should contain locale URLs for switching
        $this->assertStringContainsString('de', $content);
        $this->assertStringContainsString('fr', $content);
        $this->assertStringContainsString('es', $content);
    }

    /** @test */
    public function it_maintains_page_context_when_switching_languages()
    {
        // Test from country page
        $response = $this->get('/en/driving-license/germany');
        $response->assertStatus(200);

        // Generate URLs for other locales from this page
        $germanUrl = LaravelLocalization::getLocalizedURL('de');
        $frenchUrl = LaravelLocalization::getLocalizedURL('fr');

        // URLs should maintain the current page context
        $this->assertStringContainsString('/de', $germanUrl);
        $this->assertStringContainsString('/fr', $frenchUrl);

        // Test that we can actually visit these URLs
        $response = $this->get($germanUrl);
        $response->assertStatus(200);

        $response = $this->get($frenchUrl);
        $response->assertStatus(200);
    }

    /** @test */
    public function it_shows_all_supported_locales_in_switcher()
    {
        $response = $this->get('/en');
        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that all supported locales are available
        $supportedLocales = LaravelLocalization::getSupportedLocales();

        foreach ($supportedLocales as $localeCode => $properties) {
            $this->assertStringContainsString(strtoupper($localeCode), $content);
        }
    }

    /** @test */
    public function it_handles_language_switching_via_select_dropdown()
    {
        $response = $this->get('/en');
        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that the select dropdown is present
        $this->assertStringContainsString('<select', $content);
        $this->assertStringContainsString('changeLanguage', $content);

        // Check that options are present for each locale
        $this->assertStringContainsString('<option value="en"', $content);
        $this->assertStringContainsString('<option value="de"', $content);
        $this->assertStringContainsString('<option value="fr"', $content);
    }

    /** @test */
    public function it_marks_current_locale_as_selected()
    {
        // Test English (default)
        $response = $this->get('/en');
        $response->assertStatus(200);
        $content = $response->getContent();
        $this->assertStringContainsString('selected', $content);

        // Test German
        $response = $this->get('/de');
        $response->assertStatus(200);
        $content = $response->getContent();
        // Should show DE as current
        $this->assertStringContainsString('DE', $content);

        // Test French
        $response = $this->get('/fr');
        $response->assertStatus(200);
        $content = $response->getContent();
        // Should show FR as current
        $this->assertStringContainsString('FR', $content);
    }

    /** @test */
    public function it_includes_javascript_for_language_switching()
    {
        $response = $this->get('/en');
        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that JavaScript function is present
        $this->assertStringContainsString('changeLanguage', $content);
        $this->assertStringContainsString('localeUrls', $content);
        $this->assertStringContainsString('window.location.href', $content);
    }

    /** @test */
    public function it_works_on_all_page_types()
    {
        $pagesToTest = [
            '/en',
            '/de',
            '/fr',
            '/en/driving-license/germany',
            '/de/driving-license/germany',
            '/fr/driving-license/germany'
        ];

        foreach ($pagesToTest as $url) {
            $response = $this->get($url);
            $response->assertStatus(200);

            // Each page should have the language switcher
            $response->assertSee('language-switcher-top');
        }
    }

    /** @test */
    public function it_generates_seo_friendly_urls_for_each_locale()
    {
        $country = Country::where('slug', 'germany')->first();

        // Test URL generation for each supported locale
        $supportedLocales = LaravelLocalization::getSupportedLocales();

        foreach ($supportedLocales as $localeCode => $properties) {
            $url = LaravelLocalization::getLocalizedURL($localeCode, route('country.show.en', $country));

            if ($localeCode === 'en') {
                // English should not have locale prefix (hideDefaultLocaleInURL = true)
                $this->assertStringNotContainsString('/en/', $url);
            } else {
                // Other locales should have prefix
                $this->assertStringContainsString("/{$localeCode}/", $url);
            }

            // All URLs should contain the country slug
            $this->assertStringContainsString('germany', $url);
        }
    }

    /** @test */
    public function it_preserves_query_parameters_when_switching_languages()
    {
        // Test with query parameters
        $response = $this->get('/en?test=123');
        $response->assertStatus(200);

        // Language switcher should preserve query parameters
        $germanUrl = LaravelLocalization::getLocalizedURL('de', '/?test=123');
        $this->assertStringContainsString('test=123', $germanUrl);
        $this->assertStringContainsString('/de', $germanUrl);
    }
}
