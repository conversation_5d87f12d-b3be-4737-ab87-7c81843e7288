<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

class LocalizationApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test countries
        Country::factory()->germany()->create();
        Country::factory()->france()->create();
    }

    /** @test */
    public function it_returns_correct_locale_in_response_headers()
    {
        // Test English (default)
        $response = $this->get('/');
        $response->assertStatus(200);
        $this->assertEquals('en', app()->getLocale());
        
        // Test German
        $response = $this->get('/de');
        $response->assertStatus(200);
        $this->assertEquals('de', app()->getLocale());
        
        // Test French
        $response = $this->get('/fr');
        $response->assertStatus(200);
        $this->assertEquals('fr', app()->getLocale());
    }

    /** @test */
    public function it_serves_localized_content_based_on_url_prefix()
    {
        $country = Country::where('slug', 'germany')->first();
        
        // Test that different URL prefixes serve different locales
        $locales = ['en' => '', 'de' => '/de', 'fr' => '/fr', 'es' => '/es'];
        
        foreach ($locales as $locale => $prefix) {
            $url = $prefix . '/driving-license/germany';
            if ($locale === 'en') {
                $url = '/driving-license/germany'; // No prefix for default locale
            }
            
            $response = $this->get($url);
            $response->assertStatus(200);
            $this->assertEquals($locale, app()->getLocale());
            
            // Verify country data is passed correctly
            $response->assertViewHas('country');
            $viewCountry = $response->viewData('country');
            $this->assertEquals($country->id, $viewCountry->id);
        }
    }

    /** @test */
    public function it_provides_correct_translation_data_to_views()
    {
        // Test that views receive proper translation context
        $response = $this->get('/');
        $response->assertStatus(200);
        
        // Check that countries are available for translation
        $response->assertViewHas('countries');
        $countries = $response->viewData('countries');
        $this->assertNotEmpty($countries);
        
        // Test German locale
        $response = $this->get('/de');
        $response->assertStatus(200);
        $response->assertViewHas('countries');
        $this->assertEquals('de', app()->getLocale());
    }

    /** @test */
    public function it_handles_accept_language_header()
    {
        // Test with German Accept-Language header
        $response = $this->withHeaders([
            'Accept-Language' => 'de-DE,de;q=0.9,en;q=0.8'
        ])->get('/');
        
        // Should either redirect to German or serve German content
        $this->assertTrue(
            $response->status() === 200 || 
            $response->status() === 302
        );
        
        // Test with French Accept-Language header
        $response = $this->withHeaders([
            'Accept-Language' => 'fr-FR,fr;q=0.9,en;q=0.8'
        ])->get('/');
        
        $this->assertTrue(
            $response->status() === 200 || 
            $response->status() === 302
        );
    }

    /** @test */
    public function it_maintains_session_locale_preference()
    {
        // Visit German page to set locale in session
        $response = $this->get('/de');
        $response->assertStatus(200);
        
        // Subsequent requests should maintain the locale preference
        // This depends on your session configuration
        $this->assertEquals('de', app()->getLocale());
    }

    /** @test */
    public function it_generates_correct_hreflang_attributes()
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check for hreflang attributes in language switcher
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        foreach ($supportedLocales as $localeCode => $properties) {
            // Should have hreflang attribute for each locale
            $this->assertStringContainsString("hreflang=\"{$localeCode}\"", $content);
        }
    }

    /** @test */
    public function it_serves_correct_meta_tags_per_locale()
    {
        $country = Country::where('slug', 'germany')->first();
        
        // Test English meta tags
        $response = $this->get('/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewHas('seoData');
        
        $seoData = $response->viewData('seoData');
        $this->assertArrayHasKey('title', $seoData);
        $this->assertArrayHasKey('description', $seoData);
        $this->assertStringContainsString('Germany', $seoData['title']);
        
        // Test German meta tags
        $response = $this->get('/de/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewHas('seoData');
        $this->assertEquals('de', app()->getLocale());
    }

    /** @test */
    public function it_handles_route_caching_with_localization()
    {
        // Clear route cache first
        $this->artisan('route:clear');
        
        // Test that routes work after clearing cache
        $response = $this->get('/');
        $response->assertStatus(200);
        
        $response = $this->get('/de');
        $response->assertStatus(200);
        
        $response = $this->get('/fr');
        $response->assertStatus(200);
    }

    /** @test */
    public function it_provides_language_switcher_data_correctly()
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        
        $content = $response->getContent();
        
        // Check that language switcher has correct data structure
        $this->assertStringContainsString('localeUrls', $content);
        
        // Should contain URLs for each supported locale
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        foreach ($supportedLocales as $localeCode => $properties) {
            $this->assertStringContainsString("'{$localeCode}':", $content);
        }
    }

    /** @test */
    public function it_handles_middleware_chain_correctly()
    {
        // Test that all localization middleware works together
        $response = $this->get('/de');
        $response->assertStatus(200);
        
        // Should have proper locale set
        $this->assertEquals('de', app()->getLocale());
        
        // Test redirect middleware
        $response = $this->get('/de/');
        $this->assertTrue(
            $response->status() === 200 || 
            $response->status() === 301 || 
            $response->status() === 302
        );
    }

    /** @test */
    public function it_serves_404_for_unsupported_locales()
    {
        // Test with unsupported locale
        $response = $this->get('/xx/driving-license/germany');
        
        // Should return 404 or redirect
        $this->assertTrue(
            $response->status() === 404 || 
            $response->status() === 302
        );
    }

    /** @test */
    public function it_maintains_url_structure_across_locales()
    {
        $country = Country::where('slug', 'germany')->first();
        
        // Test that URL structure is consistent
        $baseUrl = '/driving-license/germany';
        
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        foreach ($supportedLocales as $localeCode => $properties) {
            if ($localeCode === 'en') {
                $url = $baseUrl;
            } else {
                $url = "/{$localeCode}{$baseUrl}";
            }
            
            $response = $this->get($url);
            $response->assertStatus(200);
            $this->assertEquals($localeCode, app()->getLocale());
        }
    }

    /** @test */
    public function it_provides_correct_canonical_urls()
    {
        $response = $this->get('/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewHas('seoData');
        
        $seoData = $response->viewData('seoData');
        $this->assertArrayHasKey('canonical', $seoData);
        $this->assertStringContainsString('driving-license/germany', $seoData['canonical']);
    }
}
