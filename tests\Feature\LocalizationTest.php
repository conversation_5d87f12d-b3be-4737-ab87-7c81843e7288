<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Country;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

class LocalizationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test countries
        Country::factory()->create([
            'name' => 'Germany',
            'code' => 'DE',
            'slug' => 'germany',
            'is_active' => true,
            'sort_order' => 1
        ]);

        Country::factory()->create([
            'name' => 'France',
            'code' => 'FR',
            'slug' => 'france',
            'is_active' => true,
            'sort_order' => 2
        ]);
    }

    /** @test */
    public function it_serves_homepage_in_default_locale()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewIs('home.index');

        // Check that the app locale is set to default (English)
        $this->assertEquals('en', app()->getLocale());
    }

    /** @test */
    public function it_can_set_locale_programmatically()
    {
        // Test setting locale programmatically
        app()->setLocale('de');
        $this->assertEquals('de', app()->getLocale());

        app()->setLocale('fr');
        $this->assertEquals('fr', app()->getLocale());

        app()->setLocale('en');
        $this->assertEquals('en', app()->getLocale());
    }

    /** @test */
    public function it_has_laravel_localization_configured()
    {
        // Test that Laravel Localization is properly configured
        $supportedLocales = LaravelLocalization::getSupportedLocales();

        $this->assertArrayHasKey('en', $supportedLocales);
        $this->assertArrayHasKey('de', $supportedLocales);
        $this->assertArrayHasKey('fr', $supportedLocales);
        $this->assertArrayHasKey('es', $supportedLocales);
        $this->assertArrayHasKey('it', $supportedLocales);
        $this->assertArrayHasKey('nl', $supportedLocales);
    }

    /** @test */
    public function it_serves_country_pages_in_different_locales()
    {
        $country = Country::where('slug', 'germany')->first();

        // Test English (default)
        $response = $this->get('/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewIs('countries.show');
        $this->assertEquals('en', app()->getLocale());

        // Test German
        $response = $this->get('/de/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewIs('countries.show');
        $this->assertEquals('de', app()->getLocale());

        // Test French
        $response = $this->get('/fr/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewIs('countries.show');
        $this->assertEquals('fr', app()->getLocale());
    }

    /** @test */
    public function it_generates_correct_localized_urls()
    {
        $country = Country::where('slug', 'germany')->first();

        // Test English URL generation
        app()->setLocale('en');
        $englishUrl = route('country.show', $country);
        $this->assertStringContainsString('/driving-license/germany', $englishUrl);

        // Test German URL generation
        app()->setLocale('de');
        $germanUrl = LaravelLocalization::getLocalizedURL('de', route('country.show', $country));
        $this->assertStringContainsString('/de/driving-license/germany', $germanUrl);

        // Test French URL generation
        app()->setLocale('fr');
        $frenchUrl = LaravelLocalization::getLocalizedURL('fr', route('country.show', $country));
        $this->assertStringContainsString('/fr/driving-license/germany', $frenchUrl);
    }

    /** @test */
    public function it_returns_correct_translations_in_response()
    {
        // Test English translations
        $response = $this->get('/');
        $response->assertSee('Home'); // Assuming navigation shows "Home" in English

        // Test German translations
        $response = $this->get('/de');
        // Note: This would require actual German translations in your language files
        // For now, we'll test that the locale is properly set
        $this->assertEquals('de', app()->getLocale());

        // Test French translations
        $response = $this->get('/fr');
        $this->assertEquals('fr', app()->getLocale());
    }

    /** @test */
    public function it_handles_invalid_locale_gracefully()
    {
        // Test with invalid locale - should redirect or use default
        $response = $this->get('/invalid-locale');

        // Should either be 404 or redirect to valid locale
        $this->assertTrue(
            $response->status() === 404 ||
            $response->status() === 302
        );
    }

    /** @test */
    public function it_maintains_locale_across_navigation()
    {
        // Start with German locale
        $response = $this->get('/de');
        $this->assertEquals('de', app()->getLocale());

        // Navigate to country page in German
        $response = $this->get('/de/driving-license/germany');
        $this->assertEquals('de', app()->getLocale());
        $response->assertStatus(200);
    }

    /** @test */
    public function it_provides_correct_countries_data_in_all_locales()
    {
        $locales = ['en', 'de', 'fr', 'es', 'it', 'nl'];

        foreach ($locales as $locale) {
            $url = $locale === 'en' ? '/' : "/{$locale}";

            $response = $this->get($url);
            $response->assertStatus(200);

            // Check that countries are passed to the view
            $response->assertViewHas('countries');

            // Verify locale is set correctly
            $this->assertEquals($locale, app()->getLocale());
        }
    }

    /** @test */
    public function it_handles_route_model_binding_correctly()
    {
        $country = Country::where('slug', 'germany')->first();

        // Test that route model binding works with country slug
        $response = $this->get('/driving-license/germany');
        $response->assertStatus(200);
        $response->assertViewHas('country');

        $viewCountry = $response->viewData('country');
        $this->assertEquals($country->id, $viewCountry->id);
        $this->assertEquals('germany', $viewCountry->slug);
    }

    /** @test */
    public function it_serves_all_supported_locales()
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();

        foreach ($supportedLocales as $localeCode => $properties) {
            $url = $localeCode === 'en' ? '/' : "/{$localeCode}";

            $response = $this->get($url);
            $response->assertStatus(200);

            // Verify the locale is properly set
            $this->assertEquals($localeCode, app()->getLocale());
        }
    }

    /** @test */
    public function it_generates_language_switcher_urls_correctly()
    {
        $country = Country::where('slug', 'germany')->first();

        // Test from country page
        $response = $this->get('/driving-license/germany');
        $response->assertStatus(200);

        // Test that we can generate URLs for other locales
        $germanUrl = LaravelLocalization::getLocalizedURL('de');
        $frenchUrl = LaravelLocalization::getLocalizedURL('fr');

        $this->assertStringContainsString('/de', $germanUrl);
        $this->assertStringContainsString('/fr', $frenchUrl);
    }

    /** @test */
    public function it_handles_middleware_correctly()
    {
        // Test that localization middleware is working
        $response = $this->get('/de');

        // Should have proper headers and locale set
        $this->assertEquals('de', app()->getLocale());
        $response->assertStatus(200);

        // Test middleware redirects if needed
        $response = $this->get('/de/');
        $this->assertTrue(
            $response->status() === 200 ||
            $response->status() === 301 ||
            $response->status() === 302
        );
    }
}
