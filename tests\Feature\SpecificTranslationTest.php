<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\App;

class SpecificTranslationTest extends TestCase
{
    /** @test */
    public function it_translates_english_text_to_french_and_german()
    {
        // Test Case 1: Navigation Items
        $englishTexts = [
            'messages.nav.home' => 'Home',
            'messages.nav.about' => 'About Us',
            'messages.nav.contact' => 'Contact',
            'messages.nav.countries' => 'Countries',
            'messages.nav.how_it_works' => 'How It Works',
            'messages.nav.pricing' => 'Pricing'
        ];

        foreach ($englishTexts as $key => $expectedEnglish) {
            // Verify English
            App::setLocale('en');
            $englishTranslation = __($key);
            $this->assertEquals($expectedEnglish, $englishTranslation, "English translation for '{$key}' should be '{$expectedEnglish}'");

            // Get French translation
            App::setLocale('fr');
            $frenchTranslation = __($key);

            // Get German translation
            App::setLocale('de');
            $germanTranslation = __($key);

            // Verify translations are strings and not empty
            $this->assertIsString($frenchTranslation, "French translation for '{$key}' should be a string");
            $this->assertNotEmpty($frenchTranslation, "French translation for '{$key}' should not be empty");
            $this->assertIsString($germanTranslation, "German translation for '{$key}' should be a string");
            $this->assertNotEmpty($germanTranslation, "German translation for '{$key}' should not be empty");

            // Verify they are different from English (if actual translations exist)
            // If no translation exists, Laravel returns the key, so we check for that too
            // Special case: "Contact" is the same in French and English
            if ($key !== 'messages.nav.contact') {
                $this->assertTrue(
                    $frenchTranslation !== $expectedEnglish || $frenchTranslation === $key,
                    "French translation for '{$key}' should be different from English or be the fallback key"
                );
            }

            $this->assertTrue(
                $germanTranslation !== $expectedEnglish || $germanTranslation === $key,
                "German translation for '{$key}' should be different from English or be the fallback key"
            );

            echo "\n🔤 Translation Test for: {$key}\n";
            echo "🇬🇧 English: {$englishTranslation}\n";
            echo "🇫🇷 French:  {$frenchTranslation}\n";
            echo "🇩🇪 German:  {$germanTranslation}\n";
        }
    }

    /** @test */
    public function it_translates_country_specific_text_to_french_and_german()
    {
        // Test Case 2: Country-specific content
        $countryTexts = [
            'messages.countries.buy_now' => 'Buy Now',
            'messages.countries.learn_more' => 'Learn More',
            'messages.countries.title' => 'Choose Your EU Country',
            'messages.countries.subtitle' => 'Available Countries'
        ];

        foreach ($countryTexts as $key => $expectedEnglish) {
            // Verify English
            App::setLocale('en');
            $englishTranslation = __($key);
            $this->assertEquals($expectedEnglish, $englishTranslation, "English translation for '{$key}' should be '{$expectedEnglish}'");

            // Get French translation
            App::setLocale('fr');
            $frenchTranslation = __($key);

            // Get German translation
            App::setLocale('de');
            $germanTranslation = __($key);

            // Verify translations exist and are meaningful
            $this->assertIsString($frenchTranslation);
            $this->assertNotEmpty($frenchTranslation);
            $this->assertIsString($germanTranslation);
            $this->assertNotEmpty($germanTranslation);

            echo "\n🏷️ Country Text Translation: {$key}\n";
            echo "🇬🇧 English: {$englishTranslation}\n";
            echo "🇫🇷 French:  {$frenchTranslation}\n";
            echo "🇩🇪 German:  {$germanTranslation}\n";
        }
    }

    /** @test */
    public function it_translates_homepage_content_to_french_and_german()
    {
        // Test Case 3: Homepage content
        $homepageTexts = [
            'messages.homepage.title' => 'Get Your EU Driving License Online',
            'messages.homepage.subtitle' => 'Fast, Legal & Authentic EU Driving Licenses',
            'messages.homepage.get_license_now' => 'Get License Now',
            'messages.homepage.welcome_message' => 'Welcome To EU Driving License'
        ];

        foreach ($homepageTexts as $key => $expectedEnglish) {
            // Verify English
            App::setLocale('en');
            $englishTranslation = __($key);
            $this->assertEquals($expectedEnglish, $englishTranslation, "English translation for '{$key}' should be '{$expectedEnglish}'");

            // Get French translation
            App::setLocale('fr');
            $frenchTranslation = __($key);

            // Get German translation
            App::setLocale('de');
            $germanTranslation = __($key);

            // Verify translations exist
            $this->assertIsString($frenchTranslation);
            $this->assertNotEmpty($frenchTranslation);
            $this->assertIsString($germanTranslation);
            $this->assertNotEmpty($germanTranslation);

            echo "\n🏠 Homepage Translation: {$key}\n";
            echo "🇬🇧 English: {$englishTranslation}\n";
            echo "🇫🇷 French:  {$frenchTranslation}\n";
            echo "🇩🇪 German:  {$germanTranslation}\n";
        }
    }

    /** @test */
    public function it_demonstrates_real_time_translation_switching()
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🌍 REAL-TIME TRANSLATION DEMONSTRATION\n";
        echo str_repeat("=", 60) . "\n";

        $testPhrases = [
            'messages.nav.home',
            'messages.countries.buy_now',
            'messages.homepage.title'
        ];

        foreach ($testPhrases as $key) {
            echo "\n📝 Testing: {$key}\n";
            echo str_repeat("-", 40) . "\n";

            // English
            App::setLocale('en');
            $english = __($key);
            echo "🇬🇧 EN: {$english}\n";

            // French
            App::setLocale('fr');
            $french = __($key);
            echo "🇫🇷 FR: {$french}\n";

            // German
            App::setLocale('de');
            $german = __($key);
            echo "🇩🇪 DE: {$german}\n";

            // Verify all are valid
            $this->assertIsString($english);
            $this->assertIsString($french);
            $this->assertIsString($german);
            $this->assertNotEmpty($english);
            $this->assertNotEmpty($french);
            $this->assertNotEmpty($german);
        }

        echo "\n" . str_repeat("=", 60) . "\n";
    }

    /** @test */
    public function it_verifies_translation_accuracy_with_expected_values()
    {
        // This test will check if we have actual French and German translations
        // If not, it will show what needs to be added

        echo "\n🔍 TRANSLATION ACCURACY CHECK\n";
        echo str_repeat("=", 50) . "\n";

        $translationChecks = [
            'messages.nav.home' => [
                'en' => 'Home',
                'expected_fr' => 'Accueil', // Expected French
                'expected_de' => 'Startseite' // Expected German
            ],
            'messages.countries.buy_now' => [
                'en' => 'Buy Now',
                'expected_fr' => 'Acheter Maintenant',
                'expected_de' => 'Jetzt Kaufen'
            ],
            'messages.nav.contact' => [
                'en' => 'Contact',
                'expected_fr' => 'Contact',
                'expected_de' => 'Kontakt'
            ]
        ];

        foreach ($translationChecks as $key => $expectations) {
            echo "\n📋 Checking: {$key}\n";

            // English
            App::setLocale('en');
            $english = __($key);
            echo "🇬🇧 English: {$english}\n";

            // French
            App::setLocale('fr');
            $french = __($key);
            echo "🇫🇷 French:  {$french}";
            if ($french === $expectations['expected_fr']) {
                echo " ✅ (Correct translation)";
            } elseif ($french === $key) {
                echo " ⚠️  (No translation - using key)";
            } else {
                echo " ℹ️  (Different translation)";
            }
            echo "\n";

            // German
            App::setLocale('de');
            $german = __($key);
            echo "🇩🇪 German:  {$german}";
            if ($german === $expectations['expected_de']) {
                echo " ✅ (Correct translation)";
            } elseif ($german === $key) {
                echo " ⚠️  (No translation - using key)";
            } else {
                echo " ℹ️  (Different translation)";
            }
            echo "\n";

            // Assertions
            $this->assertEquals($expectations['en'], $english);
            $this->assertIsString($french);
            $this->assertIsString($german);
            $this->assertNotEmpty($french);
            $this->assertNotEmpty($german);
        }
    }

    /** @test */
    public function it_tests_parameterized_translations()
    {
        echo "\n🔧 PARAMETERIZED TRANSLATION TEST\n";
        echo str_repeat("=", 40) . "\n";

        // Test with parameters
        $name = "John Doe";
        $country = "Germany";

        // English
        App::setLocale('en');
        $englishWelcome = __('Welcome :name to :country', ['name' => $name, 'country' => $country]);
        echo "🇬🇧 English: {$englishWelcome}\n";

        // French
        App::setLocale('fr');
        $frenchWelcome = __('Welcome :name to :country', ['name' => $name, 'country' => $country]);
        echo "🇫🇷 French:  {$frenchWelcome}\n";

        // German
        App::setLocale('de');
        $germanWelcome = __('Welcome :name to :country', ['name' => $name, 'country' => $country]);
        echo "🇩🇪 German:  {$germanWelcome}\n";

        // Verify parameters are included
        $this->assertStringContainsString($name, $englishWelcome);
        $this->assertStringContainsString($country, $englishWelcome);
        $this->assertStringContainsString($name, $frenchWelcome);
        $this->assertStringContainsString($name, $germanWelcome);
    }
}
