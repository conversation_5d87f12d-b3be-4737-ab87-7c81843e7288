<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

class TranslationWorkflowTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function complete_translation_workflow_works_correctly()
    {
        // Step 1: Test that we can set different locales
        $locales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        
        foreach ($locales as $locale) {
            App::setLocale($locale);
            $this->assertEquals($locale, App::getLocale());
        }
    }

    /** @test */
    public function backend_serves_translations_based_on_user_locale_request()
    {
        // Simulate user requesting English content
        App::setLocale('en');
        
        $homeTranslation = __('messages.nav.home');
        $aboutTranslation = __('messages.nav.about');
        $buyNowTranslation = __('messages.countries.buy_now');
        
        $this->assertEquals('Home', $homeTranslation);
        $this->assertEquals('About Us', $aboutTranslation);
        $this->assertEquals('Buy Now', $buyNowTranslation);
        
        // Simulate user requesting German content
        App::setLocale('de');
        
        $homeTranslationDe = __('messages.nav.home');
        $aboutTranslationDe = __('messages.nav.about');
        $buyNowTranslationDe = __('messages.countries.buy_now');
        
        // These should be strings (either translated or fallback)
        $this->assertIsString($homeTranslationDe);
        $this->assertIsString($aboutTranslationDe);
        $this->assertIsString($buyNowTranslationDe);
        $this->assertNotEmpty($homeTranslationDe);
        $this->assertNotEmpty($aboutTranslationDe);
        $this->assertNotEmpty($buyNowTranslationDe);
    }

    /** @test */
    public function backend_provides_correct_url_generation_per_locale()
    {
        // Test URL generation for different locales
        $testPath = '/test-page';
        
        // Test each supported locale
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        foreach ($supportedLocales as $localeCode => $properties) {
            $localizedUrl = LaravelLocalization::getLocalizedURL($localeCode, $testPath);
            
            if ($localeCode === 'en') {
                // English should not have locale prefix (hideDefaultLocaleInURL = true)
                $this->assertStringNotContainsString('/en/', $localizedUrl);
            } else {
                // Other locales should have their prefix
                $this->assertStringContainsString("/{$localeCode}/", $localizedUrl);
            }
            
            // All URLs should contain the test path
            $this->assertStringContainsString('test-page', $localizedUrl);
        }
    }

    /** @test */
    public function backend_handles_language_switching_correctly()
    {
        // Start with English
        App::setLocale('en');
        $this->assertEquals('en', App::getLocale());
        
        $englishHome = __('messages.nav.home');
        $this->assertEquals('Home', $englishHome);
        
        // Switch to German
        App::setLocale('de');
        $this->assertEquals('de', App::getLocale());
        
        $germanHome = __('messages.nav.home');
        $this->assertIsString($germanHome);
        $this->assertNotEmpty($germanHome);
        
        // Switch to French
        App::setLocale('fr');
        $this->assertEquals('fr', App::getLocale());
        
        $frenchHome = __('messages.nav.home');
        $this->assertIsString($frenchHome);
        $this->assertNotEmpty($frenchHome);
        
        // Switch back to English
        App::setLocale('en');
        $this->assertEquals('en', App::getLocale());
        
        $englishHomeAgain = __('messages.nav.home');
        $this->assertEquals('Home', $englishHomeAgain);
    }

    /** @test */
    public function backend_provides_consistent_translation_structure()
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        $requiredTranslationKeys = [
            'messages.nav.home',
            'messages.nav.about',
            'messages.nav.contact',
            'messages.nav.countries',
            'messages.nav.how_it_works',
            'messages.nav.pricing',
            'messages.countries.buy_now',
            'messages.countries.learn_more',
            'messages.homepage.title',
            'messages.homepage.subtitle'
        ];
        
        foreach ($supportedLocales as $localeCode => $properties) {
            App::setLocale($localeCode);
            
            foreach ($requiredTranslationKeys as $key) {
                $translation = __($key);
                
                // Each key should return a non-empty string
                $this->assertIsString($translation, "Translation for key '{$key}' in locale '{$localeCode}' should be a string");
                $this->assertNotEmpty($translation, "Translation for key '{$key}' in locale '{$localeCode}' should not be empty");
            }
        }
    }

    /** @test */
    public function backend_locale_configuration_supports_all_required_languages()
    {
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        $requiredLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        
        foreach ($requiredLocales as $locale) {
            $this->assertArrayHasKey($locale, $supportedLocales, "Locale '{$locale}' should be supported");
            
            // Each locale should have proper metadata
            $this->assertArrayHasKey('name', $supportedLocales[$locale]);
            $this->assertArrayHasKey('native', $supportedLocales[$locale]);
            $this->assertIsString($supportedLocales[$locale]['name']);
            $this->assertIsString($supportedLocales[$locale]['native']);
        }
    }

    /** @test */
    public function backend_translation_fallback_mechanism_works()
    {
        // Test with English (should always work)
        App::setLocale('en');
        $englishTranslation = __('messages.nav.home');
        $this->assertEquals('Home', $englishTranslation);
        
        // Test with potentially missing translations
        App::setLocale('de');
        $germanTranslation = __('messages.nav.home');
        
        // Should either be translated or fall back gracefully
        $this->assertIsString($germanTranslation);
        $this->assertNotEmpty($germanTranslation);
        
        // Test with invalid locale
        App::setLocale('invalid');
        $fallbackTranslation = __('messages.nav.home');
        
        // Should still return something meaningful
        $this->assertIsString($fallbackTranslation);
        $this->assertNotEmpty($fallbackTranslation);
    }

    /** @test */
    public function backend_provides_seo_friendly_locale_urls()
    {
        $testRoutes = [
            '/driving-license/germany',
            '/about',
            '/contact',
            '/pricing'
        ];
        
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        
        foreach ($testRoutes as $route) {
            foreach ($supportedLocales as $localeCode => $properties) {
                $localizedUrl = LaravelLocalization::getLocalizedURL($localeCode, $route);
                
                // URL should be well-formed
                $this->assertIsString($localizedUrl);
                $this->assertNotEmpty($localizedUrl);
                
                // Should contain the original route
                $this->assertStringContainsString(ltrim($route, '/'), $localizedUrl);
                
                // Should have proper locale prefix (except for English)
                if ($localeCode !== 'en') {
                    $this->assertStringContainsString("/{$localeCode}/", $localizedUrl);
                }
            }
        }
    }

    /** @test */
    public function backend_translation_system_performance_is_acceptable()
    {
        $startTime = microtime(true);
        
        // Perform multiple translation operations
        $locales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        $keys = [
            'messages.nav.home',
            'messages.nav.about',
            'messages.countries.buy_now',
            'messages.homepage.title'
        ];
        
        foreach ($locales as $locale) {
            App::setLocale($locale);
            
            foreach ($keys as $key) {
                $translation = __($key);
                $this->assertIsString($translation);
            }
        }
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Should complete within reasonable time (1 second)
        $this->assertLessThan(1.0, $executionTime, 'Translation operations should complete within 1 second');
    }

    /** @test */
    public function backend_maintains_translation_context_correctly()
    {
        // Test that translations maintain context when switching locales
        $contexts = [
            'nav' => ['home', 'about', 'contact'],
            'countries' => ['buy_now', 'learn_more'],
            'homepage' => ['title', 'subtitle']
        ];
        
        foreach (['en', 'de', 'fr'] as $locale) {
            App::setLocale($locale);
            
            foreach ($contexts as $context => $keys) {
                foreach ($keys as $key) {
                    $fullKey = "messages.{$context}.{$key}";
                    $translation = __($fullKey);
                    
                    $this->assertIsString($translation);
                    $this->assertNotEmpty($translation);
                }
            }
        }
    }

    /** @test */
    public function backend_translation_system_is_production_ready()
    {
        // Test that the translation system meets production requirements
        
        // 1. All required locales are supported
        $supportedLocales = LaravelLocalization::getSupportedLocales();
        $this->assertGreaterThanOrEqual(6, count($supportedLocales));
        
        // 2. Default locale is properly configured
        $this->assertEquals('en', config('app.locale'));
        $this->assertEquals('en', config('app.fallback_locale'));
        
        // 3. Laravel Localization is properly configured
        $this->assertTrue(config('laravellocalization.hideDefaultLocaleInURL'));
        $this->assertTrue(config('laravellocalization.useAcceptLanguageHeader'));
        
        // 4. Translation files are accessible
        foreach (['en', 'de', 'fr'] as $locale) {
            App::setLocale($locale);
            $translation = __('messages.nav.home');
            $this->assertIsString($translation);
            $this->assertNotEmpty($translation);
        }
        
        // 5. URL generation works for all locales
        foreach ($supportedLocales as $localeCode => $properties) {
            $url = LaravelLocalization::getLocalizedURL($localeCode, '/test');
            $this->assertIsString($url);
            $this->assertNotEmpty($url);
        }
    }
}
