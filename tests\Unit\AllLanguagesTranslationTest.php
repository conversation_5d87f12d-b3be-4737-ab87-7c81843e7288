<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;

class AllLanguagesTranslationTest extends TestCase
{
    /**
     * Test that all supported languages have complete translation files
     *
     * @test
     */
    public function it_loads_all_supported_languages_correctly()
    {
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        
        foreach ($supportedLocales as $locale) {
            App::setLocale($locale);
            
            // Test navigation translations
            $this->assertNotEmpty(__('messages.nav.home'), "Navigation home translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.nav.about'), "Navigation about translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.nav.countries'), "Navigation countries translation missing for {$locale}");
            
            // Test homepage translations
            $this->assertNotEmpty(__('messages.homepage.title'), "Homepage title translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.homepage.slide2_title1'), "Homepage slide2_title1 translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.homepage.slide2_title2'), "Homepage slide2_title2 translation missing for {$locale}");
            
            // Test features translations
            $this->assertNotEmpty(__('messages.features.legal_service'), "Features legal_service translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.features.fast_processing'), "Features fast_processing translation missing for {$locale}");
            
            // Test about section translations
            $this->assertNotEmpty(__('messages.about.title'), "About title translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.about.subtitle'), "About subtitle translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.about.description'), "About description translation missing for {$locale}");
            
            // Test countries section translations
            $this->assertNotEmpty(__('messages.countries.title'), "Countries title translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.countries.buy_now'), "Countries buy_now translation missing for {$locale}");
            
            // Test counter section translations
            $this->assertNotEmpty(__('messages.counter.happy_customers'), "Counter happy_customers translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.counter.eu_countries'), "Counter eu_countries translation missing for {$locale}");
            
            // Test testimonials section translations
            $this->assertNotEmpty(__('messages.testimonials.title'), "Testimonials title translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.testimonials.subtitle'), "Testimonials subtitle translation missing for {$locale}");
            
            // Test news section translations
            $this->assertNotEmpty(__('messages.news.title'), "News title translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.news.view_all'), "News view_all translation missing for {$locale}");
            
            // Test CTA section translations
            $this->assertNotEmpty(__('messages.cta.title'), "CTA title translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.cta.button'), "CTA button translation missing for {$locale}");
            
            // Test footer translations
            $this->assertNotEmpty(__('messages.footer.description'), "Footer description translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.footer.quick_links'), "Footer quick_links translation missing for {$locale}");
            
            // Test language switcher translations
            $this->assertNotEmpty(__('messages.language.select'), "Language select translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.language.current'), "Language current translation missing for {$locale}");
            
            // Test common translations
            $this->assertNotEmpty(__('messages.common.read_more'), "Common read_more translation missing for {$locale}");
            $this->assertNotEmpty(__('messages.common.learn_more'), "Common learn_more translation missing for {$locale}");
        }
    }
    
    /**
     * Test that translations return different content for different languages
     *
     * @test
     */
    public function it_returns_different_content_for_different_languages()
    {
        // Test homepage title in different languages
        App::setLocale('en');
        $englishTitle = __('messages.homepage.title');
        
        App::setLocale('de');
        $germanTitle = __('messages.homepage.title');
        
        App::setLocale('fr');
        $frenchTitle = __('messages.homepage.title');
        
        App::setLocale('es');
        $spanishTitle = __('messages.homepage.title');
        
        App::setLocale('it');
        $italianTitle = __('messages.homepage.title');
        
        App::setLocale('nl');
        $dutchTitle = __('messages.homepage.title');
        
        // Ensure all titles are different (not just returning the key)
        $this->assertNotEquals($englishTitle, $germanTitle);
        $this->assertNotEquals($englishTitle, $frenchTitle);
        $this->assertNotEquals($englishTitle, $spanishTitle);
        $this->assertNotEquals($englishTitle, $italianTitle);
        $this->assertNotEquals($englishTitle, $dutchTitle);
        
        // Ensure none of them are returning the translation key
        $this->assertNotEquals('messages.homepage.title', $englishTitle);
        $this->assertNotEquals('messages.homepage.title', $germanTitle);
        $this->assertNotEquals('messages.homepage.title', $frenchTitle);
        $this->assertNotEquals('messages.homepage.title', $spanishTitle);
        $this->assertNotEquals('messages.homepage.title', $italianTitle);
        $this->assertNotEquals('messages.homepage.title', $dutchTitle);
    }
    
    /**
     * Test that language-specific content is correctly formatted
     *
     * @test
     */
    public function it_provides_correctly_formatted_language_specific_content()
    {
        $expectedLanguageCodes = [
            'en' => 'EN',
            'de' => 'DE', 
            'fr' => 'FR',
            'es' => 'ES',
            'it' => 'IT',
            'nl' => 'NL'
        ];
        
        foreach ($expectedLanguageCodes as $locale => $expectedCode) {
            App::setLocale($locale);
            $this->assertEquals($expectedCode, __('messages.language.current'), "Language code incorrect for {$locale}");
        }
    }
}
