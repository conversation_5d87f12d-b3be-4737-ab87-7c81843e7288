<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Lang;

class TranslationTest extends TestCase
{
    /** @test */
    public function it_loads_english_translations_correctly()
    {
        App::setLocale('en');

        // Test navigation translations
        $this->assertEquals('Home', __('messages.nav.home'));
        $this->assertEquals('About Us', __('messages.nav.about'));
        $this->assertEquals('Contact', __('messages.nav.contact'));
        $this->assertEquals('Countries', __('messages.nav.countries'));
        $this->assertEquals('How It Works', __('messages.nav.how_it_works'));
        $this->assertEquals('Pricing', __('messages.nav.pricing'));
    }

    /** @test */
    public function it_loads_german_translations_correctly()
    {
        App::setLocale('de');

        // Test that German translations exist and are different from English
        $homeTranslation = __('messages.nav.home');
        $aboutTranslation = __('messages.nav.about');

        // These should not be the same as English if translations exist
        // If translations don't exist, Laravel returns the key
        $this->assertTrue(
            $homeTranslation !== 'Home' ||
            $homeTranslation === 'messages.nav.home'
        );
    }

    /** @test */
    public function it_loads_french_translations_correctly()
    {
        App::setLocale('fr');

        $homeTranslation = __('messages.nav.home');
        $aboutTranslation = __('messages.nav.about');

        // These should not be the same as English if translations exist
        $this->assertTrue(
            $homeTranslation !== 'Home' ||
            $homeTranslation === 'messages.nav.home'
        );
    }

    /** @test */
    public function it_falls_back_to_default_locale_for_missing_translations()
    {
        // Set to a locale that might not have all translations
        App::setLocale('es');

        // Test that we get either the translation or the fallback
        $translation = __('messages.nav.home');

        // Should either be translated or fall back to key/default
        $this->assertIsString($translation);
        $this->assertNotEmpty($translation);
    }

    /** @test */
    public function it_handles_translation_parameters_correctly()
    {
        App::setLocale('en');

        // Test translations with parameters
        $welcomeMessage = __('messages.welcome', ['name' => 'John']);

        // Should handle parameters correctly
        $this->assertIsString($welcomeMessage);
    }

    /** @test */
    public function it_provides_country_specific_translations()
    {
        App::setLocale('en');

        // Test country-specific translations
        $buyNow = __('messages.countries.buy_now');
        $learnMore = __('messages.countries.learn_more');

        $this->assertIsString($buyNow);
        $this->assertIsString($learnMore);
        $this->assertNotEmpty($buyNow);
        $this->assertNotEmpty($learnMore);
    }

    /** @test */
    public function it_loads_all_supported_locale_files()
    {
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];

        foreach ($supportedLocales as $locale) {
            App::setLocale($locale);

            // Test that basic translations load without errors
            $homeTranslation = __('messages.nav.home');

            $this->assertIsString($homeTranslation);
            $this->assertNotEmpty($homeTranslation);
        }
    }

    /** @test */
    public function it_maintains_translation_consistency_across_locales()
    {
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];
        $translationKeys = [
            'messages.nav.home',
            'messages.nav.about',
            'messages.nav.contact',
            'messages.nav.countries',
            'messages.nav.how_it_works',
            'messages.nav.pricing'
        ];

        foreach ($supportedLocales as $locale) {
            App::setLocale($locale);

            foreach ($translationKeys as $key) {
                $translation = __($key);

                // Each key should return a string (either translation or key itself)
                $this->assertIsString($translation);
                $this->assertNotEmpty($translation);
            }
        }
    }

    /** @test */
    public function it_handles_pluralization_correctly()
    {
        App::setLocale('en');

        // Test pluralization if you have plural translations
        $singleItem = trans_choice('messages.items', 1);
        $multipleItems = trans_choice('messages.items', 5);

        $this->assertIsString($singleItem);
        $this->assertIsString($multipleItems);
    }

    /** @test */
    public function it_validates_translation_file_structure()
    {
        $supportedLocales = ['en', 'de', 'fr', 'es', 'it', 'nl'];

        foreach ($supportedLocales as $locale) {
            // Check if translation files exist
            $messagesPath = resource_path("lang/{$locale}/messages.php");
            $routesPath = resource_path("lang/{$locale}/routes.php");

            // At minimum, we should have these files or the locale should handle gracefully
            if (file_exists($messagesPath)) {
                $messages = include $messagesPath;
                $this->assertIsArray($messages);
            }

            if (file_exists($routesPath)) {
                $routes = include $routesPath;
                $this->assertIsArray($routes);
            }
        }
    }

    /** @test */
    public function it_returns_correct_locale_specific_content()
    {
        // Test that different locales return different content where expected
        $locales = ['en', 'de', 'fr'];
        $translations = [];

        foreach ($locales as $locale) {
            App::setLocale($locale);
            $translations[$locale] = __('messages.nav.home');
        }

        // If translations exist, they should be different
        // If they don't exist, they'll all be the same (the key)
        $uniqueTranslations = array_unique($translations);

        // Either we have different translations or they're all the same fallback
        $this->assertTrue(
            count($uniqueTranslations) > 1 ||
            count($uniqueTranslations) === 1
        );
    }
}
